{% extends "base.html" %}

{% block title %}Edit Record - {{ table_name.replace('_', ' ').title() }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-edit me-2 text-primary"></i>
                        Edit Record
                    </h1>
                    <p class="text-muted mb-0">{{ table_name.replace('_', ' ').title() }} - ID: {{ record_id }}</p>
                </div>
                <div>
                    <a href="{{ url_for('admin_view_table', table_name=table_name) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Table
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit me-2"></i>
                        Record Details
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            {% for col in schema %}
                                <div class="col-md-6 mb-3">
                                    <label for="{{ col['name'] }}" class="form-label">
                                        {{ col['name'].replace('_', ' ').title() }}
                                        {% if col['pk'] %}
                                            <span class="badge bg-primary">Primary Key</span>
                                        {% elif col['notnull'] and not col['dflt_value'] %}
                                            <span class="text-danger">*</span>
                                        {% endif %}
                                    </label>
                                    
                                    {% if col['pk'] %}
                                        <!-- Primary Key - Read Only -->
                                        <input type="text" class="form-control" value="{{ record[col['name']] }}" readonly>
                                        <small class="form-text text-muted">Primary key cannot be modified</small>
                                    {% elif col['name'] == 'created_at' %}
                                        <!-- Created At - Read Only -->
                                        <input type="text" class="form-control" 
                                               value="{{ record[col['name']] if record[col['name']] else 'N/A' }}" readonly>
                                        <small class="form-text text-muted">Creation timestamp</small>
                                    {% elif col['name'] == 'updated_at' %}
                                        <!-- Updated At - Read Only -->
                                        <input type="text" class="form-control" 
                                               value="{{ record[col['name']] if record[col['name']] else 'Will be set automatically' }}" readonly>
                                        <small class="form-text text-muted">Will be updated automatically</small>
                                    {% elif col['name'] in foreign_keys %}
                                        <!-- Foreign Key Dropdown -->
                                        <select class="form-select" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                                {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                            <option value="">Select {{ foreign_keys[col['name']]['table'].replace('_', ' ').title() }}</option>
                                            {% for option in foreign_keys[col['name']]['options'] %}
                                            <option value="{{ option[0] if option|length > 0 else '' }}"
                                                    {% if option|length > 0 and option[0] == record[col['name']] %}selected{% endif %}>
                                                {% if option|length > 1 and option[1] %}
                                                    {{ option[1] }}
                                                {% elif option|length > 0 %}
                                                    {{ option[0] }}
                                                {% else %}
                                                    Unknown
                                                {% endif %}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    {% elif col['name'] == 'password_hash' %}
                                        <!-- Password Field -->
                                        <input type="password" class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                               placeholder="Enter new password (leave blank to keep current)">
                                        <small class="form-text text-muted">Leave blank to keep current password</small>
                                    {% elif col['name'] == 'email' %}
                                        <!-- Email Field -->
                                        <input type="email" class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                               value="{{ record[col['name']] or '' }}"
                                               {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                    {% elif col['name'].endswith('_date') or col['name'].endswith('_at') %}
                                        <!-- Date/DateTime Field -->
                                        <input type="datetime-local" class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                               value="{{ record[col['name']] if record[col['name']] else '' }}"
                                               {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                    {% elif col['type']|string|upper == 'INTEGER' %}
                                        <!-- Integer Field -->
                                        <input type="number" class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                               value="{{ record[col['name']] or '' }}"
                                               {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                    {% elif col['type']|string|upper == 'REAL' %}
                                        <!-- Float Field -->
                                        <input type="number" step="0.01" class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                               value="{{ record[col['name']] or '' }}"
                                               {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                    {% elif col['type']|string|upper == 'BOOLEAN' %}
                                        <!-- Boolean Field -->
                                        <select class="form-select" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                                {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                            <option value="">Select...</option>
                                            <option value="1" {% if record[col['name']] == 1 %}selected{% endif %}>True</option>
                                            <option value="0" {% if record[col['name']] == 0 %}selected{% endif %}>False</option>
                                        </select>
                                    {% elif col['name'] in ['description', 'notes', 'address'] %}
                                        <!-- Textarea for long text -->
                                        <textarea class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}" rows="3"
                                                  {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>{{ record[col['name']] or '' }}</textarea>
                                    {% elif col['name'] == 'face_encoding' %}
                                        <!-- Face Encoding - Read Only -->
                                        <input type="text" class="form-control" value="[Binary facial recognition data]" readonly>
                                        <small class="form-text text-muted">Facial encoding data cannot be manually edited</small>
                                    {% else %}
                                        <!-- Text Field -->
                                        <input type="text" class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                               value="{{ record[col['name']] or '' }}"
                                               {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                    {% endif %}
                                    
                                    {% if col['dflt_value'] %}
                                    <small class="form-text text-muted">Default: {{ col['dflt_value'] }}</small>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('admin_view_table', table_name=table_name) }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        Update Record
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Record Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="m-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Record Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="text-primary">Record ID</h6>
                            <p class="mb-0">{{ record_id }}</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-primary">Created</h6>
                            <p class="mb-0">
                                {% if record.created_at %}
                                    {{ record.created_at }}
                                {% else %}
                                    Not available
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-primary">Last Updated</h6>
                            <p class="mb-0">
                                {% if record.updated_at %}
                                    {{ record.updated_at }}
                                {% else %}
                                    Never updated
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> Modifying this record may affect related data in other tables. 
                        Please ensure you understand the relationships before making changes.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
