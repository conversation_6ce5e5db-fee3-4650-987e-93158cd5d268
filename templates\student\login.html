{% extends "base.html" %}

{% block title %}Student Login - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-5">
            <div class="card border-0 shadow-lg">
                <div class="card-header text-center py-4">
                    <i class="fas fa-user-graduate text-primary" style="font-size: 3rem;"></i>
                    <h3 class="mt-3 mb-0">Student Login</h3>
                    <p class="text-muted mb-0">Access your attendance dashboard</p>
                </div>
                <div class="card-body p-5">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form method="POST">
                        <div class="mb-4">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-1"></i>Student Number or Email
                            </label>
                            <input type="text" class="form-control form-control-lg" id="username" 
                                   name="username" required placeholder="Enter your student number or email">
                        </div>

                        <div class="mb-4">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-1"></i>Password
                            </label>
                            <input type="password" class="form-control form-control-lg" id="password" 
                                   name="password" required placeholder="Enter your password">
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    Remember me
                                </label>
                            </div>
                        </div>

                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Login
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-3">Don't have an account?</p>
                        <a href="{{ url_for('student_register') }}" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus me-2"></i>Register as Student
                        </a>
                    </div>

                    <div class="text-center mt-4">
                        <a href="{{ url_for('choose_portal') }}" class="btn btn-link">
                            <i class="fas fa-arrow-left me-1"></i>Back to Portal Selection
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
