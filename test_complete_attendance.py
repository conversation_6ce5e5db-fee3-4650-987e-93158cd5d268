import requests
import json
import base64
from PIL import Image, ImageDraw
import io

def create_face_like_image():
    """Create a simple image that might be detected as a face"""
    # Create a larger image with face-like features
    img = Image.new('RGB', (400, 400), color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw a face-like oval
    draw.ellipse([100, 100, 300, 300], fill='peachpuff', outline='black', width=2)
    
    # Draw eyes
    draw.ellipse([140, 160, 170, 190], fill='black')  # Left eye
    draw.ellipse([230, 160, 260, 190], fill='black')  # Right eye
    
    # Draw nose
    draw.polygon([(200, 200), (190, 230), (210, 230)], fill='pink')
    
    # Draw mouth
    draw.arc([170, 240, 230, 270], 0, 180, fill='red', width=3)
    
    return img

def test_complete_attendance():
    """Test complete attendance marking with a face-like image"""
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Step 1: Login as student
    print("🔍 Step 1: Logging in as student...")
    login_data = {
        'username': '22302925',
        'password': 'student123'
    }
    
    login_response = session.post(
        'http://localhost:5000/student/login',
        data=login_data,
        allow_redirects=False
    )
    
    if login_response.status_code != 302:
        print("❌ Login failed")
        return
    
    print("✅ Login successful")
    
    # Step 2: Create a face-like image
    print("\n🔍 Step 2: Creating face-like image...")
    img = create_face_like_image()
    
    # Convert to base64
    img_buffer = io.BytesIO()
    img.save(img_buffer, format='JPEG')
    img_buffer.seek(0)
    img_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
    image_data_url = f"data:image/jpeg;base64,{img_base64}"
    
    print(f"✅ Image created (size: {len(image_data_url)} characters)")
    
    # Step 3: Mark attendance
    print("\n🔍 Step 3: Marking attendance...")
    attendance_data = {
        'session_id': 2,
        'image': image_data_url
    }
    
    attendance_response = session.post(
        'http://localhost:5000/api/mark_attendance',
        json=attendance_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Attendance response status: {attendance_response.status_code}")
    
    try:
        response_data = attendance_response.json()
        print(f"Response: {json.dumps(response_data, indent=2)}")
        
        if response_data.get('success'):
            print("🎉 Attendance marked successfully!")
        else:
            print(f"❌ Attendance failed: {response_data.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error parsing response: {e}")
        print(f"Response text: {attendance_response.text[:500]}")

def test_with_simulated_face():
    """Test attendance with OpenCV disabled (simulated face detection)"""
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Login
    login_data = {
        'username': '22302925',
        'password': 'student123'
    }
    
    login_response = session.post(
        'http://localhost:5000/student/login',
        data=login_data,
        allow_redirects=False
    )
    
    if login_response.status_code != 302:
        print("❌ Login failed")
        return
    
    print("✅ Login successful")
    
    # Create simple image
    img = Image.new('RGB', (100, 100), color='blue')
    img_buffer = io.BytesIO()
    img.save(img_buffer, format='JPEG')
    img_buffer.seek(0)
    img_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
    image_data_url = f"data:image/jpeg;base64,{img_base64}"
    
    # Mark attendance
    attendance_data = {
        'session_id': 2,
        'image': image_data_url
    }
    
    attendance_response = session.post(
        'http://localhost:5000/api/mark_attendance',
        json=attendance_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Response status: {attendance_response.status_code}")
    try:
        response_data = attendance_response.json()
        print(f"Response: {json.dumps(response_data, indent=2)}")
    except:
        print(f"Response text: {attendance_response.text[:500]}")

if __name__ == "__main__":
    print("Testing with face-like image:")
    test_complete_attendance()
    
    print("\n" + "="*60)
    print("Testing with simple image (should use fallback):")
    test_with_simulated_face()
