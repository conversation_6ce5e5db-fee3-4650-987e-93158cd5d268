#!/usr/bin/env python3
"""
Open a test attendance session for debugging
"""

import sqlite3
from datetime import datetime, timedelta

def open_test_session():
    conn = sqlite3.connect('attendance_fixed.db')
    
    print("🔄 Opening a test attendance session...")
    
    # Get an existing session to update
    session = conn.execute("""
        SELECT session_id, session_name, module_id 
        FROM attendance_sessions 
        ORDER BY session_id DESC 
        LIMIT 1
    """).fetchone()
    
    if session:
        session_id = session[0]
        
        # Open attendance window for 30 minutes from now
        now = datetime.now()
        window_end = now + timedelta(minutes=30)
        
        conn.execute("""
            UPDATE attendance_sessions 
            SET attendance_window_start = ?, 
                attendance_window_end = ?, 
                is_active = 1,
                is_completed = 0
            WHERE session_id = ?
        """, (now.isoformat(), window_end.isoformat(), session_id))
        
        conn.commit()
        
        print(f"✅ Opened attendance window for session {session_id}")
        print(f"   Session: {session[1]}")
        print(f"   Window: {now.strftime('%H:%M:%S')} to {window_end.strftime('%H:%M:%S')}")
        print(f"   Students can now mark attendance!")
        
        # Verify the update
        updated = conn.execute("""
            SELECT session_id, session_name, attendance_window_start, attendance_window_end, is_active
            FROM attendance_sessions 
            WHERE session_id = ?
        """, (session_id,)).fetchone()
        
        print(f"\n🔍 Verification:")
        print(f"   Session ID: {updated[0]}")
        print(f"   Session Name: {updated[1]}")
        print(f"   Window Start: {updated[2]}")
        print(f"   Window End: {updated[3]}")
        print(f"   Is Active: {updated[4]}")
        
    else:
        print("❌ No sessions found")
    
    conn.close()

if __name__ == "__main__":
    open_test_session()
