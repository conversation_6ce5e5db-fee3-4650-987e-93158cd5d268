#!/usr/bin/env python3
"""
Test the attendance marking API
"""

import requests
import base64
import json
from PIL import Image
import io

def create_test_image():
    """Create a simple test image"""
    # Create a simple 200x200 test image
    img = Image.new('RGB', (200, 200), color='lightblue')
    
    # Convert to base64
    buffer = io.BytesIO()
    img.save(buffer, format='JPEG')
    img_data = buffer.getvalue()
    img_base64 = base64.b64encode(img_data).decode('utf-8')
    
    return f"data:image/jpeg;base64,{img_base64}"

def test_attendance_marking():
    """Test the attendance marking API"""
    print("🧪 Testing attendance marking API...")
    
    # First, we need to login as a student
    session = requests.Session()
    
    # Login
    print("1. Logging in as student...")
    login_data = {
        'student_number': '22302925',
        'password': 'password123'
    }
    
    login_response = session.post('http://localhost:5000/student/login', data=login_data)
    
    if login_response.status_code == 200 and 'dashboard' in login_response.url:
        print("   ✅ Login successful")
    else:
        print(f"   ❌ Login failed: {login_response.status_code}")
        return False
    
    # Get active session ID
    print("2. Getting active session...")
    dashboard_response = session.get('http://localhost:5000/student/dashboard')
    
    if dashboard_response.status_code == 200:
        print("   ✅ Dashboard accessed")
        # For now, we'll use session_id = 4 (the one we opened earlier)
        session_id = 4
    else:
        print(f"   ❌ Dashboard access failed: {dashboard_response.status_code}")
        return False
    
    # Test attendance marking
    print("3. Testing attendance marking...")
    
    test_image = create_test_image()
    
    attendance_data = {
        'session_id': session_id,
        'image': test_image
    }
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    attendance_response = session.post(
        'http://localhost:5000/api/mark_attendance',
        data=json.dumps(attendance_data),
        headers=headers
    )
    
    print(f"   Response status: {attendance_response.status_code}")
    
    try:
        response_data = attendance_response.json()
        print(f"   Response data: {response_data}")
        
        if attendance_response.status_code == 200 and response_data.get('success'):
            print("   ✅ Attendance marked successfully!")
            return True
        else:
            print(f"   ❌ Attendance marking failed: {response_data.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error parsing response: {e}")
        print(f"   Raw response: {attendance_response.text}")
        return False

if __name__ == "__main__":
    success = test_attendance_marking()
    if success:
        print("\n🎉 Attendance marking test PASSED!")
    else:
        print("\n❌ Attendance marking test FAILED!")
