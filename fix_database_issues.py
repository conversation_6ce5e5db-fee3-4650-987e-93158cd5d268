#!/usr/bin/env python3
"""
Database Migration Script - Fix Missing Tables and Columns
Fixes:
1. Add student_id column to module_registrations table
2. Create attendance_marks table
3. Update any related queries
"""

import sqlite3
import sys

def fix_database():
    """Fix database schema issues"""
    try:
        conn = sqlite3.connect('attendance_fixed.db')
        conn.execute("PRAGMA foreign_keys = ON")
        
        print("🔧 Starting database fixes...")
        
        # 1. Add student_id column to module_registrations table
        print("\n1. Adding student_id column to module_registrations...")
        try:
            # Check if column already exists
            columns = [col[1] for col in conn.execute('PRAGMA table_info(module_registrations)').fetchall()]
            if 'student_id' not in columns:
                # Add the column
                conn.execute('ALTER TABLE module_registrations ADD COLUMN student_id INTEGER')
                
                # Populate the student_id column from student_enrollments
                conn.execute('''
                    UPDATE module_registrations 
                    SET student_id = (
                        SELECT se.student_id 
                        FROM student_enrollments se 
                        WHERE se.enrollment_id = module_registrations.enrollment_id
                    )
                ''')
                
                # Add foreign key constraint (recreate table)
                print("   - Recreating table with proper foreign key...")
                conn.execute('''
                    CREATE TABLE module_registrations_new (
                        registration_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        enrollment_id INTEGER NOT NULL,
                        module_id INTEGER NOT NULL,
                        student_id INTEGER NOT NULL,
                        registration_date DATE DEFAULT CURRENT_DATE,
                        registration_status VARCHAR(20) DEFAULT 'active',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (enrollment_id) REFERENCES student_enrollments(enrollment_id),
                        FOREIGN KEY (module_id) REFERENCES modules(module_id),
                        FOREIGN KEY (student_id) REFERENCES students(student_id)
                    )
                ''')
                
                # Copy data
                conn.execute('''
                    INSERT INTO module_registrations_new 
                    SELECT * FROM module_registrations
                ''')
                
                # Drop old table and rename new one
                conn.execute('DROP TABLE module_registrations')
                conn.execute('ALTER TABLE module_registrations_new RENAME TO module_registrations')
                
                print("   ✅ student_id column added successfully")
            else:
                print("   ✅ student_id column already exists")
        except Exception as e:
            print(f"   ❌ Error adding student_id column: {e}")
        
        # 2. Create attendance_marks table
        print("\n2. Creating attendance_marks table...")
        try:
            # Check if table exists
            tables = [row[0] for row in conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()]
            if 'attendance_marks' not in tables:
                conn.execute('''
                    CREATE TABLE attendance_marks (
                        mark_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        student_id INTEGER NOT NULL,
                        module_id INTEGER NOT NULL,
                        academic_year VARCHAR(20) NOT NULL,
                        semester VARCHAR(20),
                        total_sessions INTEGER DEFAULT 0,
                        attended_sessions INTEGER DEFAULT 0,
                        attendance_percentage DECIMAL(5,2) DEFAULT 0.00,
                        attendance_mark DECIMAL(5,2) DEFAULT 0.00,
                        max_attendance_mark DECIMAL(5,2) DEFAULT 10.00,
                        calculated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_finalized BOOLEAN DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (student_id) REFERENCES students(student_id),
                        FOREIGN KEY (module_id) REFERENCES modules(module_id),
                        UNIQUE(student_id, module_id, academic_year, semester)
                    )
                ''')
                print("   ✅ attendance_marks table created successfully")
            else:
                print("   ✅ attendance_marks table already exists")
        except Exception as e:
            print(f"   ❌ Error creating attendance_marks table: {e}")
        
        # 3. Create attendance_records table if it doesn't exist (for session attendance)
        print("\n3. Checking attendance_records table...")
        try:
            if 'attendance_records' not in tables:
                conn.execute('''
                    CREATE TABLE attendance_records (
                        record_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id INTEGER NOT NULL,
                        student_id INTEGER NOT NULL,
                        attendance_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        attendance_status VARCHAR(20) DEFAULT 'present',
                        verification_method VARCHAR(50) DEFAULT 'facial_recognition',
                        confidence_score DECIMAL(5,4),
                        ip_address VARCHAR(45),
                        user_agent TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES attendance_sessions(session_id),
                        FOREIGN KEY (student_id) REFERENCES students(student_id),
                        UNIQUE(session_id, student_id)
                    )
                ''')
                print("   ✅ attendance_records table created successfully")
            else:
                print("   ✅ attendance_records table already exists")
        except Exception as e:
            print(f"   ❌ Error creating attendance_records table: {e}")
        
        # 4. Verify the fixes
        print("\n4. Verifying fixes...")
        
        # Check module_registrations columns
        mr_columns = [col[1] for col in conn.execute('PRAGMA table_info(module_registrations)').fetchall()]
        if 'student_id' in mr_columns:
            print("   ✅ module_registrations.student_id column exists")
        else:
            print("   ❌ module_registrations.student_id column missing")
        
        # Check attendance_marks table
        tables = [row[0] for row in conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()]
        if 'attendance_marks' in tables:
            print("   ✅ attendance_marks table exists")
        else:
            print("   ❌ attendance_marks table missing")
        
        if 'attendance_records' in tables:
            print("   ✅ attendance_records table exists")
        else:
            print("   ❌ attendance_records table missing")
        
        conn.commit()
        conn.close()
        
        print("\n🎉 Database fixes completed successfully!")
        print("\nYou can now:")
        print("- Start attendance sessions without errors")
        print("- Calculate attendance marks properly")
        print("- View attendance records in admin panel")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing database: {e}")
        return False

if __name__ == "__main__":
    success = fix_database()
    sys.exit(0 if success else 1)
