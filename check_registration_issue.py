#!/usr/bin/env python3
"""
Check the specific registration issue
"""

import sqlite3

def check_registration_issue():
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    print("🔍 Checking registration issue...")
    
    # Check the module_registrations table structure
    print("\n1. module_registrations table structure:")
    schema = conn.execute("PRAGMA table_info(module_registrations)").fetchall()
    for col in schema:
        print(f"   {col['name']} - {col['type']} - nullable: {not col['notnull']} - pk: {col['pk']}")
    
    # Check if there's a student_id column directly in module_registrations
    print("\n2. Checking module_registrations data:")
    registrations = conn.execute("""
        SELECT * FROM module_registrations LIMIT 5
    """).fetchall()
    
    for reg in registrations:
        print(f"   Registration: {dict(reg)}")
    
    # Check the relationship between tables
    print("\n3. Checking table relationships:")
    
    # Check if module_registrations has student_id directly or through enrollment_id
    has_student_id = any(col['name'] == 'student_id' for col in schema)
    has_enrollment_id = any(col['name'] == 'enrollment_id' for col in schema)
    
    print(f"   module_registrations has student_id: {has_student_id}")
    print(f"   module_registrations has enrollment_id: {has_enrollment_id}")
    
    if has_enrollment_id and not has_student_id:
        print("\n   ⚠️  ISSUE FOUND: module_registrations uses enrollment_id, not student_id directly!")
        print("   The query in mark_attendance is looking for student_id in module_registrations,")
        print("   but it should join through student_enrollments table.")
        
        # Show the correct query
        print("\n4. Testing correct query:")
        correct_query = """
            SELECT mr.registration_id, se.student_id, mr.module_id, mr.registration_status
            FROM module_registrations mr
            JOIN student_enrollments se ON mr.enrollment_id = se.enrollment_id
            WHERE se.student_id = ? AND mr.module_id = ? AND mr.registration_status = 'active'
        """
        
        # Test with student 1 and module 26
        result = conn.execute(correct_query, (1, 26)).fetchone()
        print(f"   Correct query result: {dict(result) if result else 'None'}")
        
        # Show what the incorrect query returns
        print("\n5. Testing incorrect query (current code):")
        incorrect_query = """
            SELECT mr.registration_id, mr.student_id, mr.module_id, mr.registration_status
            FROM module_registrations mr
            WHERE mr.student_id = ? AND mr.module_id = ? AND mr.registration_status = 'active'
        """
        
        try:
            result = conn.execute(incorrect_query, (1, 26)).fetchone()
            print(f"   Incorrect query result: {dict(result) if result else 'None'}")
        except Exception as e:
            print(f"   Incorrect query error: {e}")
    
    conn.close()

if __name__ == "__main__":
    check_registration_issue()
