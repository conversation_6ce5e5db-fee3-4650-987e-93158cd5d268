import sqlite3
from datetime import datetime

def check_sessions():
    conn = sqlite3.connect('attendance_fixed.db')
    
    print("🔍 Checking attendance sessions...")
    
    # Check all sessions
    sessions = conn.execute('''
        SELECT as_.session_id, as_.module_id, as_.lecturer_id, as_.session_name, 
               as_.session_date, as_.is_active, as_.is_completed,
               m.module_code, m.module_name, u.username as lecturer_name
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN users u ON as_.lecturer_id = u.user_id
        ORDER BY as_.created_at DESC
    ''').fetchall()
    
    print(f"📊 Found {len(sessions)} sessions:")
    for session in sessions:
        print(f"   - Session {session[0]}: {session[3]} ({session[7]} - {session[8]})")
        print(f"     Lecturer: {session[9]}, Date: {session[4]}, Active: {session[5]}, Completed: {session[6]}")
    
    if not sessions:
        print("❌ No sessions found!")
        return
    
    # Check student enrollments for the modules
    print(f"\n🎓 Checking student enrollments...")
    enrollments = conn.execute('''
        SELECT DISTINCT mr.student_id, mr.module_id, s.student_number, s.first_name, s.last_name,
               m.module_code, m.module_name
        FROM module_registrations mr
        JOIN students s ON mr.student_id = s.student_id
        JOIN modules m ON mr.module_id = m.module_id
        WHERE mr.registration_status = 'active'
        ORDER BY mr.module_id, s.student_number
    ''').fetchall()
    
    print(f"📋 Found {len(enrollments)} active enrollments:")
    for enrollment in enrollments:
        print(f"   - Student {enrollment[2]} ({enrollment[3]} {enrollment[4]}) enrolled in {enrollment[5]} - {enrollment[6]}")
    
    # Check if students can see sessions for their modules
    print(f"\n🔍 Checking session visibility for students...")
    if sessions and enrollments:
        # Get a sample student and check what sessions they should see
        sample_student = enrollments[0]
        student_id = sample_student[0]
        
        print(f"Testing for student {sample_student[2]} (ID: {student_id}):")
        
        # Query that should show sessions for this student
        student_sessions = conn.execute('''
            SELECT as_.session_id, as_.session_name, as_.session_date, as_.is_active,
                   m.module_code, m.module_name
            FROM attendance_sessions as_
            JOIN modules m ON as_.module_id = m.module_id
            JOIN module_registrations mr ON m.module_id = mr.module_id
            WHERE mr.student_id = ? AND mr.registration_status = 'active'
            AND as_.is_active = 1
            ORDER BY as_.session_date DESC
        ''', (student_id,)).fetchall()
        
        print(f"   Sessions visible to student: {len(student_sessions)}")
        for session in student_sessions:
            print(f"     - {session[1]} ({session[4]} - {session[5]}) - Active: {session[3]}")
    
    # Check lecturer sessions
    print(f"\n👨‍🏫 Checking lecturer session visibility...")
    if sessions:
        sample_session = sessions[0]
        lecturer_id = sample_session[2]
        
        lecturer_sessions = conn.execute('''
            SELECT as_.session_id, as_.session_name, as_.session_date, as_.is_active, as_.is_completed,
                   m.module_code, m.module_name
            FROM attendance_sessions as_
            JOIN modules m ON as_.module_id = m.module_id
            WHERE as_.lecturer_id = ?
            ORDER BY as_.created_at DESC
        ''', (lecturer_id,)).fetchall()
        
        print(f"   Sessions for lecturer ID {lecturer_id}: {len(lecturer_sessions)}")
        for session in lecturer_sessions:
            print(f"     - {session[1]} ({session[5]} - {session[6]}) - Active: {session[3]}, Completed: {session[4]}")
    
    conn.close()

if __name__ == "__main__":
    check_sessions()
