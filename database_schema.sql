-- =====================================================
-- FACIAL RECOGNITION ATTENDANCE SYSTEM DATABASE SCHEMA
-- =====================================================
-- Designed for scalability, security, and anti-tampering
-- Supports SQLite, MySQL, and PostgreSQL

-- =====================================================
-- 1. INSTITUTIONS, FACULTIES AND DEPARTMENTS
-- =====================================================

CREATE TABLE institutions (
    institution_id INTEGER PRIMARY KEY AUTOINCREMENT,
    institution_name VARCHAR(255) NOT NULL UNIQUE,
    institution_code VARCHAR(20) NOT NULL UNIQUE,
    address TEXT,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Faculties (e.g., Engineering, Business, Arts)
CREATE TABLE faculties (
    faculty_id INTEGER PRIMARY KEY AUTOINCREMENT,
    institution_id INTEGER NOT NULL,
    faculty_name VARCHAR(255) NOT NULL,
    faculty_code VARCHAR(20) NOT NULL,
    dean_name VARCHAR(255),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (institution_id) REFERENCES institutions(institution_id) ON DELETE CASCADE,
    UNIQUE(institution_id, faculty_code)
);

-- Departments within faculties (e.g., Computer Science, Mechanical Engineering)
CREATE TABLE departments (
    department_id INTEGER PRIMARY KEY AUTOINCREMENT,
    faculty_id INTEGER NOT NULL,
    department_name VARCHAR(255) NOT NULL,
    department_code VARCHAR(20) NOT NULL,
    head_of_department VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (faculty_id) REFERENCES faculties(faculty_id) ON DELETE CASCADE,
    UNIQUE(faculty_id, department_code)
);

-- =====================================================
-- 2. USER MANAGEMENT (LECTURERS/ADMINISTRATORS)
-- =====================================================

CREATE TABLE user_roles (
    role_id INTEGER PRIMARY KEY AUTOINCREMENT,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    permissions JSON -- Store permissions as JSON for flexibility
);

-- Insert default roles
INSERT INTO user_roles (role_name, description, permissions) VALUES 
('super_admin', 'System Administrator', '{"all": true}'),
('admin', 'Institution Administrator', '{"manage_users": true, "view_all_attendance": true, "manage_courses": true}'),
('lecturer', 'Course Lecturer', '{"view_own_courses": true, "mark_attendance": true, "generate_reports": true}'),
('assistant', 'Teaching Assistant', '{"view_assigned_courses": true, "mark_attendance": true}');

CREATE TABLE users (
    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
    institution_id INTEGER NOT NULL,
    faculty_id INTEGER,
    department_id INTEGER,
    employee_id VARCHAR(50) UNIQUE,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL, -- Use bcrypt or similar
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role_id INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP,
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (institution_id) REFERENCES institutions(institution_id) ON DELETE CASCADE,
    FOREIGN KEY (faculty_id) REFERENCES faculties(faculty_id) ON DELETE SET NULL,
    FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL,
    FOREIGN KEY (role_id) REFERENCES user_roles(role_id) ON DELETE RESTRICT
);

-- =====================================================
-- 3. STUDENTS
-- =====================================================

CREATE TABLE students (
    student_id INTEGER PRIMARY KEY AUTOINCREMENT,
    institution_id INTEGER NOT NULL,
    faculty_id INTEGER NOT NULL,
    department_id INTEGER,
    student_number VARCHAR(50) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL, -- Students can login to manage their profile
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE,
    phone VARCHAR(20),
    address TEXT,
    enrollment_date DATE NOT NULL,
    graduation_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    is_facial_registered BOOLEAN DEFAULT FALSE, -- Track if facial recognition is set up
    registration_completed BOOLEAN DEFAULT FALSE, -- Track if full registration is done
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (institution_id) REFERENCES institutions(institution_id) ON DELETE CASCADE,
    FOREIGN KEY (faculty_id) REFERENCES faculties(faculty_id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL,
    UNIQUE(institution_id, student_number)
);

-- =====================================================
-- 4. FACIAL RECOGNITION DATA (SECURE STORAGE)
-- =====================================================

CREATE TABLE facial_embeddings (
    embedding_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    embedding_vector BLOB NOT NULL, -- Store as binary data for security
    embedding_hash VARCHAR(64) NOT NULL, -- SHA-256 hash for integrity verification
    model_version VARCHAR(50) NOT NULL, -- Track which model generated this embedding
    confidence_threshold DECIMAL(5,4) DEFAULT 0.8500, -- Minimum confidence for recognition
    is_primary BOOLEAN DEFAULT FALSE, -- Allow multiple embeddings per student
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- =====================================================
-- 5. COURSES, ACADEMIC YEARS AND MODULES
-- =====================================================

-- Courses (e.g., Bachelor of Computer Science, Diploma in Engineering)
CREATE TABLE courses (
    course_id INTEGER PRIMARY KEY AUTOINCREMENT,
    institution_id INTEGER NOT NULL,
    faculty_id INTEGER NOT NULL,
    department_id INTEGER,
    course_code VARCHAR(20) NOT NULL,
    course_name VARCHAR(255) NOT NULL,
    course_description TEXT,
    duration_years INTEGER DEFAULT 3, -- Course duration
    qualification_type VARCHAR(50) DEFAULT 'Bachelor', -- Bachelor, Diploma, Certificate, etc.
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (institution_id) REFERENCES institutions(institution_id) ON DELETE CASCADE,
    FOREIGN KEY (faculty_id) REFERENCES faculties(faculty_id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL,
    UNIQUE(institution_id, course_code)
);

-- Academic years within courses (Year 1, Year 2, Year 3, etc.)
CREATE TABLE course_years (
    course_year_id INTEGER PRIMARY KEY AUTOINCREMENT,
    course_id INTEGER NOT NULL,
    year_number INTEGER NOT NULL, -- 1, 2, 3, etc.
    year_name VARCHAR(100), -- "First Year", "Second Year", etc.
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
    UNIQUE(course_id, year_number)
);

-- Modules/Subjects within each year (e.g., Programming 101, Mathematics, etc.)
CREATE TABLE modules (
    module_id INTEGER PRIMARY KEY AUTOINCREMENT,
    course_year_id INTEGER NOT NULL,
    module_code VARCHAR(20) NOT NULL,
    module_name VARCHAR(255) NOT NULL,
    module_description TEXT,
    credits INTEGER DEFAULT 3,
    semester VARCHAR(20), -- "Semester 1", "Semester 2", "Full Year"
    is_core BOOLEAN DEFAULT TRUE, -- Core vs Elective
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (course_year_id) REFERENCES course_years(course_year_id) ON DELETE CASCADE,
    UNIQUE(course_year_id, module_code)
);

-- Module lecturers (staff assigned to teach specific modules)
CREATE TABLE module_lecturers (
    module_lecturer_id INTEGER PRIMARY KEY AUTOINCREMENT,
    module_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    role VARCHAR(20) DEFAULT 'lecturer', -- 'lecturer', 'assistant', 'coordinator'
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE(module_id, user_id)
);

-- Student enrollments in specific course years
CREATE TABLE student_enrollments (
    enrollment_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    course_id INTEGER NOT NULL,
    course_year_id INTEGER NOT NULL,
    academic_year VARCHAR(10) NOT NULL, -- "2024/2025"
    enrollment_date DATE NOT NULL,
    enrollment_status VARCHAR(20) DEFAULT 'active', -- 'active', 'dropped', 'completed', 'suspended'
    final_grade VARCHAR(5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
    FOREIGN KEY (course_year_id) REFERENCES course_years(course_year_id) ON DELETE CASCADE,
    UNIQUE(student_id, course_id, academic_year)
);

-- Student module registrations (which modules they're taking)
CREATE TABLE module_registrations (
    registration_id INTEGER PRIMARY KEY AUTOINCREMENT,
    enrollment_id INTEGER NOT NULL,
    module_id INTEGER NOT NULL,
    registration_date DATE NOT NULL,
    registration_status VARCHAR(20) DEFAULT 'active', -- 'active', 'dropped', 'completed'
    final_mark DECIMAL(5,2),
    final_grade VARCHAR(5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (enrollment_id) REFERENCES student_enrollments(enrollment_id) ON DELETE CASCADE,
    FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
    UNIQUE(enrollment_id, module_id)
);

-- =====================================================
-- 6. CLASS SESSIONS
-- =====================================================

CREATE TABLE class_sessions (
    session_id INTEGER PRIMARY KEY AUTOINCREMENT,
    module_id INTEGER NOT NULL,
    session_name VARCHAR(255),
    session_type VARCHAR(50) DEFAULT 'lecture', -- 'lecture', 'tutorial', 'lab', 'exam', 'practical'
    scheduled_start_time TIMESTAMP NOT NULL,
    scheduled_end_time TIMESTAMP NOT NULL,
    actual_start_time TIMESTAMP,
    actual_end_time TIMESTAMP,
    location VARCHAR(255),
    venue_capacity INTEGER, -- Maximum students for the venue
    attendance_window_minutes INTEGER DEFAULT 15, -- How long after start time to allow attendance
    late_arrival_window_minutes INTEGER DEFAULT 30, -- How long to allow late arrivals
    created_by INTEGER NOT NULL,
    session_notes TEXT, -- Additional notes about the session
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE RESTRICT
);

-- =====================================================
-- 7. ATTENDANCE RECORDS (ANTI-TAMPERING DESIGN)
-- =====================================================

CREATE TABLE attendance_records (
    attendance_id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id INTEGER NOT NULL,
    student_id INTEGER NOT NULL,
    recognition_confidence DECIMAL(5,4) NOT NULL, -- Confidence score from facial recognition
    attendance_timestamp TIMESTAMP NOT NULL,
    recognition_method VARCHAR(20) DEFAULT 'facial', -- 'facial', 'manual', 'override'
    device_info VARCHAR(255), -- Camera/device used for recognition
    ip_address VARCHAR(45), -- IPv4 or IPv6 address
    location_coordinates VARCHAR(50), -- GPS coordinates if available
    image_hash VARCHAR(64), -- Hash of the captured image for verification
    processed_by INTEGER, -- User who processed manual entries

    -- Anti-tampering fields
    record_hash VARCHAR(64) NOT NULL, -- SHA-256 hash of critical fields
    previous_record_hash VARCHAR(64), -- Chain previous record for integrity
    digital_signature VARCHAR(512), -- Digital signature for non-repudiation

    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_verified BOOLEAN DEFAULT FALSE, -- Manual verification flag
    verification_notes TEXT,

    FOREIGN KEY (session_id) REFERENCES class_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES users(user_id) ON DELETE SET NULL,

    -- Prevent duplicate attendance for same session
    UNIQUE(session_id, student_id)
);

-- =====================================================
-- 8. ATTENDANCE AUDIT TRAIL (IMMUTABLE LOG)
-- =====================================================

CREATE TABLE attendance_audit_log (
    audit_id INTEGER PRIMARY KEY AUTOINCREMENT,
    attendance_id INTEGER NOT NULL,
    action_type VARCHAR(20) NOT NULL, -- 'INSERT', 'UPDATE', 'DELETE', 'VERIFY'
    old_values JSON, -- Previous values before change
    new_values JSON, -- New values after change
    changed_by INTEGER NOT NULL,
    change_reason TEXT,
    change_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,

    -- Audit trail integrity
    audit_hash VARCHAR(64) NOT NULL,
    previous_audit_hash VARCHAR(64),

    FOREIGN KEY (attendance_id) REFERENCES attendance_records(attendance_id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(user_id) ON DELETE RESTRICT
);

-- =====================================================
-- 9. SYSTEM CONFIGURATION AND SECURITY
-- =====================================================

CREATE TABLE system_config (
    config_id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    config_type VARCHAR(20) DEFAULT 'string', -- 'string', 'integer', 'boolean', 'json'
    description TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    updated_by INTEGER,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Insert default configuration
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('facial_recognition_threshold', '0.85', 'decimal', 'Minimum confidence threshold for facial recognition'),
('attendance_window_default', '15', 'integer', 'Default attendance window in minutes'),
('max_login_attempts', '5', 'integer', 'Maximum failed login attempts before lockout'),
('session_timeout_minutes', '60', 'integer', 'User session timeout in minutes'),
('backup_retention_days', '90', 'integer', 'Number of days to retain backup files'),
('enable_gps_tracking', 'false', 'boolean', 'Enable GPS location tracking for attendance'),
('require_manual_verification', 'false', 'boolean', 'Require manual verification for all attendance records');

CREATE TABLE security_events (
    event_id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_type VARCHAR(50) NOT NULL, -- 'login_failed', 'unauthorized_access', 'data_breach', etc.
    user_id INTEGER,
    ip_address VARCHAR(45),
    user_agent TEXT,
    event_details JSON,
    severity_level VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'critical'
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_by INTEGER,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- =====================================================
-- 10. PERFORMANCE OPTIMIZATION INDEXES
-- =====================================================

-- Primary lookup indexes
CREATE INDEX idx_students_student_number ON students(student_number);
CREATE INDEX idx_students_institution ON students(institution_id);
CREATE INDEX idx_students_active ON students(is_active);

-- Facial recognition indexes
CREATE INDEX idx_facial_embeddings_student ON facial_embeddings(student_id);
CREATE INDEX idx_facial_embeddings_active ON facial_embeddings(is_active);
CREATE INDEX idx_facial_embeddings_primary ON facial_embeddings(is_primary);

-- Course and enrollment indexes
CREATE INDEX idx_courses_code ON courses(course_code);
CREATE INDEX idx_courses_institution ON courses(institution_id);
CREATE INDEX idx_course_enrollments_student ON course_enrollments(student_id);
CREATE INDEX idx_course_enrollments_course ON course_enrollments(course_id);
CREATE INDEX idx_course_enrollments_status ON course_enrollments(enrollment_status);

-- Session and attendance indexes
CREATE INDEX idx_class_sessions_course ON class_sessions(course_id);
CREATE INDEX idx_class_sessions_time ON class_sessions(scheduled_start_time);
CREATE INDEX idx_attendance_session ON attendance_records(session_id);
CREATE INDEX idx_attendance_student ON attendance_records(student_id);
CREATE INDEX idx_attendance_timestamp ON attendance_records(attendance_timestamp);
CREATE INDEX idx_attendance_method ON attendance_records(recognition_method);

-- Security and audit indexes
CREATE INDEX idx_audit_log_attendance ON attendance_audit_log(attendance_id);
CREATE INDEX idx_audit_log_timestamp ON attendance_audit_log(change_timestamp);
CREATE INDEX idx_security_events_type ON security_events(event_type);
CREATE INDEX idx_security_events_timestamp ON security_events(created_at);

-- User management indexes
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_institution ON users(institution_id);
CREATE INDEX idx_users_active ON users(is_active);

-- =====================================================
-- 11. DATABASE TRIGGERS FOR SECURITY AND INTEGRITY
-- =====================================================

-- Trigger to automatically create audit log entries
-- Note: Syntax may vary between database systems
-- This example is for SQLite/PostgreSQL style

-- Update timestamp triggers
CREATE TRIGGER update_students_timestamp
    BEFORE UPDATE ON students
    FOR EACH ROW
    BEGIN
        UPDATE students SET updated_at = CURRENT_TIMESTAMP WHERE student_id = NEW.student_id;
    END;

CREATE TRIGGER update_users_timestamp
    BEFORE UPDATE ON users
    FOR EACH ROW
    BEGIN
        UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE user_id = NEW.user_id;
    END;

CREATE TRIGGER update_courses_timestamp
    BEFORE UPDATE ON courses
    FOR EACH ROW
    BEGIN
        UPDATE courses SET updated_at = CURRENT_TIMESTAMP WHERE course_id = NEW.course_id;
    END;

-- Attendance audit trigger
CREATE TRIGGER attendance_audit_trigger
    AFTER INSERT ON attendance_records
    FOR EACH ROW
    BEGIN
        INSERT INTO attendance_audit_log (
            attendance_id, action_type, new_values, changed_by,
            change_timestamp, audit_hash
        ) VALUES (
            NEW.attendance_id, 'INSERT',
            json_object(
                'student_id', NEW.student_id,
                'session_id', NEW.session_id,
                'confidence', NEW.recognition_confidence,
                'timestamp', NEW.attendance_timestamp,
                'method', NEW.recognition_method
            ),
            NEW.processed_by,
            CURRENT_TIMESTAMP,
            -- Simple hash calculation (implement proper hashing in application)
            substr(hex(randomblob(16)), 1, 32)
        );
    END;

-- =====================================================
-- 12. USEFUL VIEWS FOR REPORTING
-- =====================================================

-- Student attendance summary view
CREATE VIEW student_attendance_summary AS
SELECT
    s.student_id,
    s.student_number,
    s.first_name,
    s.last_name,
    c.course_code,
    c.course_name,
    COUNT(cs.session_id) as total_sessions,
    COUNT(ar.attendance_id) as attended_sessions,
    ROUND(
        (COUNT(ar.attendance_id) * 100.0 / COUNT(cs.session_id)), 2
    ) as attendance_percentage
FROM students s
JOIN course_enrollments ce ON s.student_id = ce.student_id
JOIN courses c ON ce.course_id = c.course_id
JOIN class_sessions cs ON c.course_id = cs.course_id
LEFT JOIN attendance_records ar ON cs.session_id = ar.session_id
    AND s.student_id = ar.student_id
WHERE ce.enrollment_status = 'active'
    AND cs.is_active = TRUE
GROUP BY s.student_id, c.course_id;

-- Course attendance overview
CREATE VIEW course_attendance_overview AS
SELECT
    c.course_id,
    c.course_code,
    c.course_name,
    c.semester,
    c.academic_year,
    COUNT(DISTINCT ce.student_id) as enrolled_students,
    COUNT(DISTINCT cs.session_id) as total_sessions,
    COUNT(ar.attendance_id) as total_attendance_records,
    ROUND(
        (COUNT(ar.attendance_id) * 100.0 /
         (COUNT(DISTINCT ce.student_id) * COUNT(DISTINCT cs.session_id))), 2
    ) as overall_attendance_rate
FROM courses c
LEFT JOIN course_enrollments ce ON c.course_id = ce.course_id
    AND ce.enrollment_status = 'active'
LEFT JOIN class_sessions cs ON c.course_id = cs.course_id
    AND cs.is_active = TRUE
LEFT JOIN attendance_records ar ON cs.session_id = ar.session_id
WHERE c.is_active = TRUE
GROUP BY c.course_id;

-- Daily attendance report view
CREATE VIEW daily_attendance_report AS
SELECT
    DATE(cs.scheduled_start_time) as session_date,
    c.course_code,
    c.course_name,
    cs.session_name,
    cs.session_type,
    cs.location,
    COUNT(DISTINCT ce.student_id) as enrolled_students,
    COUNT(ar.attendance_id) as present_students,
    ROUND(
        (COUNT(ar.attendance_id) * 100.0 / COUNT(DISTINCT ce.student_id)), 2
    ) as attendance_percentage
FROM class_sessions cs
JOIN courses c ON cs.course_id = c.course_id
LEFT JOIN course_enrollments ce ON c.course_id = ce.course_id
    AND ce.enrollment_status = 'active'
LEFT JOIN attendance_records ar ON cs.session_id = ar.session_id
WHERE cs.is_active = TRUE
GROUP BY cs.session_id
ORDER BY session_date DESC, cs.scheduled_start_time;

-- =====================================================
-- 13. SAMPLE DATA INSERTION QUERIES
-- =====================================================

-- Sample institution
INSERT INTO institutions (institution_name, institution_code, address, contact_email)
VALUES ('University of Technology', 'UTECH', '123 University Ave, Tech City', '<EMAIL>');

-- Sample department
INSERT INTO departments (institution_id, department_name, department_code)
VALUES (1, 'Computer Science', 'CS');

-- Sample admin user (password should be hashed in real implementation)
INSERT INTO users (institution_id, department_id, username, email, password_hash,
                  first_name, last_name, role_id)
VALUES (1, 1, 'admin', '<EMAIL>', 'hashed_password_here',
        'System', 'Administrator', 1);

-- =====================================================
-- END OF SCHEMA
-- =====================================================

-- Performance notes:
-- 1. Consider partitioning attendance_records by date for large datasets
-- 2. Implement proper connection pooling in application
-- 3. Use read replicas for reporting queries
-- 4. Regular maintenance: ANALYZE tables, UPDATE statistics
-- 5. Monitor query performance and add indexes as needed
