import requests
import json
import base64
from PIL import Image
import io

def test_authenticated_attendance():
    """Test attendance marking with proper authentication"""
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Step 1: Login as student
    print("🔍 Step 1: Logging in as student...")
    login_data = {
        'username': '22302925',  # Student login expects 'username' field
        'password': 'student123'  # Reset password
    }
    
    login_response = session.post(
        'http://localhost:5000/student/login',
        data=login_data,
        allow_redirects=False
    )
    
    print(f"Login response status: {login_response.status_code}")
    print(f"Login response headers: {dict(login_response.headers)}")
    
    if login_response.status_code == 302:
        print("✅ Login successful (redirect)")
    else:
        print("❌ Login failed")
        print(f"Response text: {login_response.text[:500]}")
        return
    
    # Step 2: Check if we can access student dashboard
    print("\n🔍 Step 2: Accessing student dashboard...")
    dashboard_response = session.get('http://localhost:5000/student/dashboard')
    print(f"Dashboard response status: {dashboard_response.status_code}")
    
    if dashboard_response.status_code == 200:
        print("✅ Dashboard accessible")
    else:
        print("❌ Dashboard not accessible")
        return
    
    # Step 3: Try to mark attendance
    print("\n🔍 Step 3: Marking attendance...")
    
    # Create a simple test image
    img = Image.new('RGB', (100, 100), color='red')
    img_buffer = io.BytesIO()
    img.save(img_buffer, format='JPEG')
    img_buffer.seek(0)
    
    # Convert to base64
    img_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
    image_data_url = f"data:image/jpeg;base64,{img_base64}"
    
    # Test data
    attendance_data = {
        'session_id': 2,  # Active session
        'image': image_data_url
    }
    
    attendance_response = session.post(
        'http://localhost:5000/api/mark_attendance',
        json=attendance_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Attendance response status: {attendance_response.status_code}")
    print(f"Attendance response headers: {dict(attendance_response.headers)}")
    
    try:
        response_data = attendance_response.json()
        print(f"Attendance response data: {json.dumps(response_data, indent=2)}")
    except:
        print(f"Attendance response text: {attendance_response.text[:1000]}")

def test_student_login_system():
    """Test the student login system to understand the user ID mapping"""
    import sqlite3
    
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    print("🔍 Testing student login system...")
    
    # Check students table
    students = conn.execute('''
        SELECT student_id, student_number, first_name, last_name, email, is_active
        FROM students
        WHERE is_active = 1
        ORDER BY student_id
    ''').fetchall()
    
    print(f"\n📊 Active students in database:")
    for student in students:
        print(f"   ID: {student['student_id']}, Number: {student['student_number']}, Name: {student['first_name']} {student['last_name']}")
        print(f"       Flask-Login ID would be: {student['student_id'] + 10000}")
    
    # Check if student 22302925 exists and is active
    target_student = conn.execute('''
        SELECT student_id, student_number, first_name, last_name, is_active
        FROM students
        WHERE student_number = ?
    ''', ('22302925',)).fetchone()
    
    if target_student:
        print(f"\n🎯 Target student (22302925):")
        print(f"   Database ID: {target_student['student_id']}")
        print(f"   Flask-Login ID: {target_student['student_id'] + 10000}")
        print(f"   Name: {target_student['first_name']} {target_student['last_name']}")
        print(f"   Active: {target_student['is_active']}")
    else:
        print(f"\n❌ Student 22302925 not found in database")
    
    conn.close()

if __name__ == "__main__":
    test_student_login_system()
    print("\n" + "="*60)
    test_authenticated_attendance()
