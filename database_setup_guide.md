# Database Setup Guide for Facial Recognition Attendance System

## Overview
This guide provides database-specific setup instructions and explains the schema design decisions for the facial recognition attendance system.

## Schema Features

### 🔒 Security Features
- **Anti-tampering**: Record hashing and digital signatures
- **Audit trail**: Immutable log of all changes
- **Biometric data protection**: Encrypted storage of facial embeddings
- **Access control**: Role-based permissions system
- **Security event logging**: Track unauthorized access attempts

### 📊 Scalability Features
- **Optimized indexes**: For fast queries on large datasets
- **Partitioning ready**: Attendance records can be partitioned by date
- **Efficient relationships**: Proper foreign key constraints
- **Performance views**: Pre-calculated attendance summaries

### 🛡️ Data Integrity Features
- **Unique constraints**: Prevent duplicate records
- **Cascade deletes**: Maintain referential integrity
- **Timestamp tracking**: Automatic creation/update timestamps
- **Validation triggers**: Ensure data consistency

## Database-Specific Setup Instructions

### SQLite (Development/Small Scale)
```sql
-- SQLite doesn't support all features, use simplified version
-- Remove JSON columns and use TEXT instead
-- Triggers syntax is slightly different

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Enable WAL mode for better concurrency
PRAGMA journal_mode = WAL;

-- Optimize for performance
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = memory;
```

### MySQL/MariaDB (Production)
```sql
-- Create database with proper charset
CREATE DATABASE facial_attendance_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Use InnoDB engine for ACID compliance
-- Add to table definitions: ENGINE=InnoDB

-- MySQL specific optimizations
SET innodb_buffer_pool_size = '70%';  -- Adjust based on available RAM
SET innodb_log_file_size = 256M;
SET innodb_flush_log_at_trx_commit = 1;

-- For large datasets, consider partitioning
ALTER TABLE attendance_records 
PARTITION BY RANGE (YEAR(attendance_timestamp)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### PostgreSQL (Enterprise)
```sql
-- Create database
CREATE DATABASE facial_attendance_db 
WITH ENCODING 'UTF8' 
LC_COLLATE='en_US.UTF-8' 
LC_CTYPE='en_US.UTF-8';

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- PostgreSQL specific optimizations
-- In postgresql.conf:
-- shared_buffers = 256MB
-- effective_cache_size = 1GB
-- work_mem = 4MB
-- maintenance_work_mem = 64MB

-- Use UUID for better distribution
-- Replace INTEGER PRIMARY KEY with UUID DEFAULT uuid_generate_v4()
```

## Key Schema Components

### 1. Students Table
- Stores basic student information
- Links to facial embeddings for recognition
- Supports multiple institutions/departments

### 2. Facial Embeddings Table
- **Security**: Embeddings stored as BLOB with hash verification
- **Flexibility**: Multiple embeddings per student (different angles/lighting)
- **Versioning**: Track which ML model generated embeddings
- **Confidence**: Configurable recognition thresholds

### 3. Attendance Records Table
- **Anti-tampering**: Record hash chains prevent modification
- **Metadata**: Device info, IP address, GPS coordinates
- **Confidence tracking**: ML confidence scores for each recognition
- **Audit trail**: Complete change history

### 4. Security Features
- **Role-based access**: Granular permissions system
- **Audit logging**: Track all system changes
- **Security events**: Monitor suspicious activities
- **Configuration management**: Centralized system settings

## Performance Optimization

### Indexing Strategy
```sql
-- Most critical indexes for performance
CREATE INDEX idx_attendance_session_student ON attendance_records(session_id, student_id);
CREATE INDEX idx_facial_embeddings_lookup ON facial_embeddings(student_id, is_active, is_primary);
CREATE INDEX idx_sessions_course_time ON class_sessions(course_id, scheduled_start_time);
```

### Query Optimization Tips
1. **Use prepared statements** to prevent SQL injection
2. **Limit result sets** with OFFSET/LIMIT for pagination
3. **Use covering indexes** for frequently accessed columns
4. **Partition large tables** by date or institution
5. **Regular maintenance**: VACUUM, ANALYZE, UPDATE STATISTICS

### Scaling Considerations
- **Read replicas**: For reporting and analytics
- **Connection pooling**: Use pgbouncer, MySQL Proxy, or similar
- **Caching**: Redis/Memcached for session data and frequent queries
- **CDN**: For serving static facial recognition model files
- **Horizontal sharding**: By institution_id for multi-tenant deployments

## Security Best Practices

### Data Protection
1. **Encrypt facial embeddings** at rest using AES-256
2. **Hash sensitive data** using bcrypt or Argon2
3. **Use TLS/SSL** for all database connections
4. **Regular backups** with encryption
5. **Access logging** for all database operations

### Compliance Considerations
- **GDPR**: Right to erasure for facial data
- **FERPA**: Student privacy protection
- **Biometric laws**: Vary by jurisdiction
- **Data retention**: Configurable retention periods

## Monitoring and Maintenance

### Key Metrics to Monitor
- Query response times
- Database connection counts
- Storage usage growth
- Failed recognition attempts
- Security event frequency

### Regular Maintenance Tasks
- Weekly: Update table statistics
- Monthly: Rebuild fragmented indexes
- Quarterly: Archive old attendance records
- Annually: Review and update security policies

## Migration Strategy

### From Existing Systems
1. **Data mapping**: Map existing student/course data
2. **Facial enrollment**: Bulk import or gradual enrollment
3. **Historical data**: Import past attendance if needed
4. **Testing**: Parallel run with existing system
5. **Cutover**: Planned migration with rollback plan

### Version Upgrades
- Use database migration scripts
- Test on staging environment first
- Backup before any schema changes
- Document all changes for rollback
