import sqlite3

conn = sqlite3.connect('attendance_fixed.db')

print("Module registrations data:")
rows = conn.execute('SELECT * FROM module_registrations LIMIT 5').fetchall()
for row in rows:
    print(f"  registration_id: {row[0]}, enrollment_id: {row[1]}, module_id: {row[2]}, student_id: {row[3] if len(row) > 3 else 'NULL'}")

print("\nColumns in module_registrations:")
columns = conn.execute('PRAGMA table_info(module_registrations)').fetchall()
for col in columns:
    print(f"  - {col[1]} ({col[2]})")

conn.close()
