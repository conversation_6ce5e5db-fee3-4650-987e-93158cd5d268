"""
Flask routes for facial recognition functionality
Handles web-based face enrollment and recognition
"""

from flask import Blueprint, render_template, request, jsonify, session, current_app, redirect, url_for
from flask_login import login_required, current_user
import base64
import json
import logging

# Conditional imports for facial recognition (will be installed later)
try:
    import cv2
    import numpy as np
    from facial_recognition_system import FacialRecognitionSystem
    FACIAL_RECOGNITION_AVAILABLE = True
except ImportError:
    FACIAL_RECOGNITION_AVAILABLE = False
    cv2 = None
    np = None
    FacialRecognitionSystem = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create blueprint
facial_bp = Blueprint('facial', __name__, url_prefix='/facial')

# Initialize facial recognition system
fr_system = None

def init_facial_recognition(app):
    """Initialize facial recognition system with app context"""
    global fr_system
    if FACIAL_RECOGNITION_AVAILABLE:
        with app.app_context():
            database_path = app.config.get('DATABASE', 'attendance_new.db')
            fr_system = FacialRecognitionSystem(database_path)
    else:
        logger.warning("Facial recognition libraries not available. Install opencv-python and face-recognition to enable facial recognition features.")

@facial_bp.route('/setup')
@login_required
def facial_setup():
    """Facial recognition setup page for students"""
    if not current_user.is_student:
        return redirect(url_for('staff_dashboard'))
    
    return render_template('facial/setup.html')

@facial_bp.route('/capture')
@login_required
def capture_interface():
    """Web-based face capture interface"""
    if not current_user.is_student:
        return redirect(url_for('staff_dashboard'))
    
    return render_template('facial/capture.html')

@facial_bp.route('/api/capture_image', methods=['POST'])
@login_required
def capture_image():
    """API endpoint to capture and process face image"""
    try:
        data = request.get_json()
        image_data = data.get('image')
        
        if not image_data:
            return jsonify({'success': False, 'error': 'No image data provided'})
        
        # Decode base64 image
        image_data = image_data.split(',')[1]  # Remove data:image/jpeg;base64, prefix
        image_bytes = base64.b64decode(image_data)
        
        # Convert to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            return jsonify({'success': False, 'error': 'Invalid image data'})
        
        # Store image in session for enrollment
        if 'captured_images' not in session:
            session['captured_images'] = []
        
        # Convert image to base64 for storage in session
        _, buffer = cv2.imencode('.jpg', image)
        image_b64 = base64.b64encode(buffer).decode('utf-8')
        session['captured_images'].append(image_b64)
        
        # Check if we have enough images
        num_captured = len(session['captured_images'])
        required_images = 5
        
        return jsonify({
            'success': True,
            'captured': num_captured,
            'required': required_images,
            'complete': num_captured >= required_images
        })
        
    except Exception as e:
        logger.error(f"Error capturing image: {e}")
        return jsonify({'success': False, 'error': str(e)})

@facial_bp.route('/api/enroll', methods=['POST'])
@login_required
def enroll_face():
    """API endpoint to enroll captured face images"""
    try:
        if not current_user.is_student:
            return jsonify({'success': False, 'error': 'Only students can enroll faces'})
        
        # Get captured images from session
        captured_images = session.get('captured_images', [])
        
        if len(captured_images) < 3:  # Minimum 3 images required
            return jsonify({'success': False, 'error': 'Not enough images captured'})
        
        # Convert base64 images back to numpy arrays
        images = []
        for img_b64 in captured_images:
            img_bytes = base64.b64decode(img_b64)
            nparr = np.frombuffer(img_bytes, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            if image is not None:
                images.append(image)
        
        if len(images) == 0:
            return jsonify({'success': False, 'error': 'No valid images found'})
        
        # Enroll the student's face
        success = fr_system.enroll_student_face(current_user.id, images)
        
        if success:
            # Clear captured images from session
            session.pop('captured_images', None)
            return jsonify({'success': True, 'message': 'Face enrollment successful!'})
        else:
            return jsonify({'success': False, 'error': 'Face enrollment failed'})
        
    except Exception as e:
        logger.error(f"Error enrolling face: {e}")
        return jsonify({'success': False, 'error': str(e)})

@facial_bp.route('/api/recognize', methods=['POST'])
def recognize_face():
    """API endpoint for face recognition"""
    try:
        data = request.get_json()
        image_data = data.get('image')
        session_id = data.get('session_id')
        
        if not image_data:
            return jsonify({'success': False, 'error': 'No image data provided'})
        
        # Decode base64 image
        image_data = image_data.split(',')[1]
        image_bytes = base64.b64decode(image_data)
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            return jsonify({'success': False, 'error': 'Invalid image data'})
        
        # Recognize face
        student_id, name, confidence = fr_system.recognize_face(image)
        
        if student_id:
            # Record attendance if session_id provided
            if session_id:
                attendance_recorded = fr_system.record_attendance(
                    student_id, session_id, confidence,
                    device_info="web_camera",
                    ip_address=request.remote_addr
                )
                
                return jsonify({
                    'success': True,
                    'student_id': student_id,
                    'name': name,
                    'confidence': round(confidence, 3),
                    'attendance_recorded': attendance_recorded
                })
            else:
                return jsonify({
                    'success': True,
                    'student_id': student_id,
                    'name': name,
                    'confidence': round(confidence, 3)
                })
        else:
            return jsonify({
                'success': False,
                'error': 'No face recognized',
                'confidence': 0.0
            })
        
    except Exception as e:
        logger.error(f"Error recognizing face: {e}")
        return jsonify({'success': False, 'error': str(e)})

@facial_bp.route('/attendance')
@login_required
def attendance_interface():
    """Face recognition interface for attendance marking"""
    if current_user.is_student:
        return redirect(url_for('student_facial_setup'))
    
    return render_template('facial/attendance.html')

@facial_bp.route('/api/clear_images', methods=['POST'])
@login_required
def clear_captured_images():
    """Clear captured images from session"""
    session.pop('captured_images', None)
    return jsonify({'success': True})

@facial_bp.route('/api/status')
@login_required
def get_status():
    """Get facial recognition system status"""
    try:
        if current_user.is_student:
            # Check if student has enrolled face
            conn = fr_system.get_db_connection()
            enrolled = conn.execute(
                "SELECT is_facial_registered FROM students WHERE student_id = ?",
                (current_user.id,)
            ).fetchone()
            conn.close()
            
            is_enrolled = enrolled['is_facial_registered'] if enrolled else False
            
            return jsonify({
                'success': True,
                'enrolled': is_enrolled,
                'known_faces': len(fr_system.known_face_encodings),
                'confidence_threshold': fr_system.confidence_threshold
            })
        else:
            return jsonify({
                'success': True,
                'known_faces': len(fr_system.known_face_encodings),
                'confidence_threshold': fr_system.confidence_threshold
            })
        
    except Exception as e:
        logger.error(f"Error getting status: {e}")
        return jsonify({'success': False, 'error': str(e)})

# Utility functions for image processing
def process_image_for_recognition(image_data: str):
    """Process base64 image data for face recognition"""
    try:
        if not FACIAL_RECOGNITION_AVAILABLE:
            return None

        # Remove data URL prefix
        if ',' in image_data:
            image_data = image_data.split(',')[1]

        # Decode base64
        image_bytes = base64.b64decode(image_data)
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        return image
    except Exception as e:
        logger.error(f"Error processing image: {e}")
        return None

def validate_face_image(image) -> bool:
    """Validate that image contains a detectable face"""
    try:
        if not FACIAL_RECOGNITION_AVAILABLE:
            return False

        import face_recognition

        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Detect faces
        face_locations = face_recognition.face_locations(rgb_image)

        return len(face_locations) > 0
    except Exception as e:
        logger.error(f"Error validating face image: {e}")
        return False
