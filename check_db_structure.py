import sqlite3

conn = sqlite3.connect('attendance_fixed.db')

print("Checking course_years table:")
print("Columns:")
for row in conn.execute('PRAGMA table_info(course_years)').fetchall():
    print(f"  {row[1]} ({row[2]})")

print("\nSample data:")
for row in conn.execute('SELECT * FROM course_years LIMIT 3').fetchall():
    print(f"  {row}")

print("\nChecking year_name field:")
for row in conn.execute('SELECT course_year_id, year_name FROM course_years LIMIT 3').fetchall():
    print(f"  ID: {row[0]}, Name: {row[1]}")

conn.close()
