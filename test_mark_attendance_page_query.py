#!/usr/bin/env python3
"""
Test the mark_attendance_page query that might be failing
"""

import sqlite3
from datetime import datetime

def test_mark_attendance_page_query():
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    print("🔍 Testing mark_attendance_page query...")
    
    # Get test data
    session_id = 4  # Active session
    student_id = 1  # Student 22302925
    
    print(f"📍 Testing with session_id={session_id}, student_id={student_id}")
    
    # Test the current complex query from mark_attendance_page
    print("\n1. Testing current complex query:")
    current_query = '''
        SELECT as_.*, m.module_code, m.module_name
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        JOIN student_enrollments se ON mr.enrollment_id = se.enrollment_id
        WHERE as_.session_id = ? AND se.student_id = ? AND mr.registration_status = 'active'
        AND as_.is_active = 1
        AND CURRENT_TIMESTAMP BETWEEN as_.attendance_window_start AND as_.attendance_window_end
    '''
    
    try:
        result = conn.execute(current_query, (session_id, student_id)).fetchone()
        print(f"   Current query result: {'✅ Found' if result else '❌ None'}")
        if result:
            print(f"   Session: {result['session_name']}")
            print(f"   Module: {result['module_code']} - {result['module_name']}")
    except Exception as e:
        print(f"   Current query error: {e}")
    
    # Test a simplified query using direct student_id in module_registrations
    print("\n2. Testing simplified query:")
    simplified_query = '''
        SELECT as_.*, m.module_code, m.module_name
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        WHERE as_.session_id = ? AND mr.student_id = ? AND mr.registration_status = 'active'
        AND as_.is_active = 1
        AND CURRENT_TIMESTAMP BETWEEN as_.attendance_window_start AND as_.attendance_window_end
    '''
    
    try:
        result = conn.execute(simplified_query, (session_id, student_id)).fetchone()
        print(f"   Simplified query result: {'✅ Found' if result else '❌ None'}")
        if result:
            print(f"   Session: {result['session_name']}")
            print(f"   Module: {result['module_code']} - {result['module_name']}")
    except Exception as e:
        print(f"   Simplified query error: {e}")
    
    # Test without time window constraint
    print("\n3. Testing without time window constraint:")
    no_time_query = '''
        SELECT as_.*, m.module_code, m.module_name
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        WHERE as_.session_id = ? AND mr.student_id = ? AND mr.registration_status = 'active'
        AND as_.is_active = 1
    '''
    
    try:
        result = conn.execute(no_time_query, (session_id, student_id)).fetchone()
        print(f"   No time constraint result: {'✅ Found' if result else '❌ None'}")
        if result:
            print(f"   Session: {result['session_name']}")
            print(f"   Module: {result['module_code']} - {result['module_name']}")
            print(f"   Window start: {result['attendance_window_start']}")
            print(f"   Window end: {result['attendance_window_end']}")
            
            # Check time window manually
            now = datetime.now()
            try:
                window_start = datetime.fromisoformat(result['attendance_window_start'])
                window_end = datetime.fromisoformat(result['attendance_window_end'])
                
                print(f"   Current time: {now}")
                print(f"   Window open: {window_start <= now <= window_end}")
            except Exception as e:
                print(f"   Time parsing error: {e}")
                
    except Exception as e:
        print(f"   No time constraint error: {e}")
    
    # Check the student_enrollments relationship
    print("\n4. Checking student_enrollments relationship:")
    enrollment_query = '''
        SELECT se.enrollment_id, se.student_id, se.course_year_id, se.status,
               mr.registration_id, mr.module_id, mr.registration_status
        FROM student_enrollments se
        JOIN module_registrations mr ON se.enrollment_id = mr.enrollment_id
        WHERE se.student_id = ?
    '''
    
    enrollments = conn.execute(enrollment_query, (student_id,)).fetchall()
    print(f"   Found {len(enrollments)} enrollment-registration pairs:")
    for enr in enrollments:
        print(f"     Enrollment {enr['enrollment_id']} -> Registration {enr['registration_id']} (Module {enr['module_id']}) - Status: {enr['registration_status']}")
    
    conn.close()

if __name__ == "__main__":
    test_mark_attendance_page_query()
