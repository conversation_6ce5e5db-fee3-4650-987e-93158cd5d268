{% extends "base.html" %}

{% block title %}Student Registration - Step 5: Module Selection{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Progress Steps -->
            <div class="progress-step mb-4">
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Basic Info</small>
                </div>
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Faculty</small>
                </div>
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Course</small>
                </div>
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Year</small>
                </div>
                <div class="step active">
                    <div class="step-number">5</div>
                    <div class="step-line"></div>
                    <small>Modules</small>
                </div>
                <div class="step">
                    <div class="step-number">6</div>
                    <small>Facial Setup</small>
                </div>
            </div>

            <!-- Module Selection -->
            <div class="card shadow-lg border-0">
                <div class="card-header text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-book me-2"></i>
                        Select Your Modules
                    </h2>
                    <p class="mb-0 mt-2">Step 5: Choose modules for {{ year_info.year_name }} in {{ year_info.course_name }}</p>
                </div>
                <div class="card-body p-5">
                    {% if modules %}
                        <form method="POST" action="{{ url_for('student_confirm_registration') }}" id="moduleForm">
                            <!-- Core Modules -->
                            {% set core_modules = modules|rejectattr('is_elective')|list %}
                            {% if core_modules %}
                            <div class="mb-5">
                                <h5 class="mb-3">
                                    <i class="fas fa-star text-warning me-2"></i>
                                    Core Modules (Required)
                                </h5>
                                <div class="row g-3">
                                    {% for module in core_modules %}
                                    <div class="col-md-6">
                                        <div class="card border-warning">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                           name="modules" value="{{ module.module_id }}" 
                                                           id="module_{{ module.module_id }}" 
                                                           checked disabled>
                                                    <label class="form-check-label w-100" for="module_{{ module.module_id }}">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div>
                                                                <h6 class="mb-1">{{ module.module_name }}</h6>
                                                                <p class="text-muted small mb-1">{{ module.module_code }}</p>
                                                                {% if module.module_description %}
                                                                <p class="small mb-2">{{ module.module_description[:100] }}{% if module.module_description|length > 100 %}...{% endif %}</p>
                                                                {% endif %}
                                                                <div class="d-flex gap-3">
                                                                    <span class="badge bg-info">{{ module.credits }} Credits</span>
                                                                    <span class="badge bg-secondary">{{ module.semester }}</span>
                                                                    <span class="badge bg-warning">Core</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </label>
                                                    <!-- Hidden input to ensure core modules are submitted -->
                                                    <input type="hidden" name="modules" value="{{ module.module_id }}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}

                            <!-- Elective Modules -->
                            {% set elective_modules = modules|selectattr('is_elective')|list %}
                            {% if elective_modules %}
                            <div class="mb-5">
                                <h5 class="mb-3">
                                    <i class="fas fa-plus-circle text-success me-2"></i>
                                    Elective Modules (Optional)
                                </h5>
                                <div class="row g-3">
                                    {% for module in elective_modules %}
                                    <div class="col-md-6">
                                        <div class="card border-success">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                           name="modules" value="{{ module.module_id }}" 
                                                           id="module_{{ module.module_id }}">
                                                    <label class="form-check-label w-100" for="module_{{ module.module_id }}">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div>
                                                                <h6 class="mb-1">{{ module.module_name }}</h6>
                                                                <p class="text-muted small mb-1">{{ module.module_code }}</p>
                                                                {% if module.module_description %}
                                                                <p class="small mb-2">{{ module.module_description[:100] }}{% if module.module_description|length > 100 %}...{% endif %}</p>
                                                                {% endif %}
                                                                <div class="d-flex gap-3">
                                                                    <span class="badge bg-info">{{ module.credits }} Credits</span>
                                                                    <span class="badge bg-secondary">{{ module.semester }}</span>
                                                                    <span class="badge bg-success">Elective</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}

                            <!-- Summary -->
                            <div class="card bg-light border-0 mb-4">
                                <div class="card-body">
                                    <h6 class="mb-2">
                                        <i class="fas fa-calculator me-2"></i>
                                        Credit Summary
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <p class="mb-1"><strong>Core Credits:</strong> <span id="coreCredits">{{ core_modules|sum(attribute='credits') or 0 }}</span></p>
                                        </div>
                                        <div class="col-md-4">
                                            <p class="mb-1"><strong>Elective Credits:</strong> <span id="electiveCredits">0</span></p>
                                        </div>
                                        <div class="col-md-4">
                                            <p class="mb-1"><strong>Total Credits:</strong> <span id="totalCredits">{{ core_modules|sum(attribute='credits') or 0 }}</span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg" id="continueBtn">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    Continue to Confirmation
                                </button>
                            </div>
                        </form>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                            <h4 class="mt-3">No Modules Available</h4>
                            <p class="text-muted">
                                No modules are currently available for this academic year. 
                                Please contact the administration for assistance.
                            </p>
                            <a href="javascript:history.back()" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Go Back
                            </a>
                        </div>
                    {% endif %}

                    <!-- Navigation Buttons -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="javascript:history.back()" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Year Selection
                        </a>
                        
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                Core modules are automatically selected
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Calculate credits dynamically
function updateCredits() {
    const coreCredits = {{ core_modules|sum(attribute='credits') or 0 }};
    let electiveCredits = 0;
    
    // Calculate elective credits
    const electiveCheckboxes = document.querySelectorAll('input[name="modules"]:not([disabled]):checked');
    electiveCheckboxes.forEach(checkbox => {
        const card = checkbox.closest('.card');
        const creditsText = card.querySelector('.badge.bg-info').textContent;
        const credits = parseInt(creditsText.match(/\d+/)[0]);
        electiveCredits += credits;
    });
    
    const totalCredits = coreCredits + electiveCredits;
    
    document.getElementById('electiveCredits').textContent = electiveCredits;
    document.getElementById('totalCredits').textContent = totalCredits;
}

// Add event listeners to elective checkboxes
document.addEventListener('DOMContentLoaded', function() {
    const electiveCheckboxes = document.querySelectorAll('input[name="modules"]:not([disabled])');
    electiveCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateCredits);
    });
    
    // Form validation
    document.getElementById('moduleForm').addEventListener('submit', function(e) {
        const checkedModules = document.querySelectorAll('input[name="modules"]:checked');
        if (checkedModules.length === 0) {
            e.preventDefault();
            alert('Please select at least one module!');
            return false;
        }
    });
});
</script>
{% endblock %}
