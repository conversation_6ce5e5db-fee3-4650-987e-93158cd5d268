"""
Database Initialization Script
Creates the database schema and inserts sample data
"""

import sqlite3
import os
from datetime import datetime, date
import hashlib

def create_database():
    """Create the database and tables"""

    # Create a new database with fixed schema
    db_name = 'attendance_fixed.db'

    # Remove if exists
    if os.path.exists(db_name):
        try:
            os.remove(db_name)
            print(f"Removed existing {db_name}")
        except PermissionError:
            print(f"Database {db_name} is in use, using different name")
            db_name = f'attendance_fixed_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'

    conn = sqlite3.connect(db_name)
    print(f"Connected to database: {db_name}")
    conn.execute("PRAGMA foreign_keys = ON")

    # Create tables with simplified SQLite-compatible schema
    tables_sql = """
    -- Institutions
    CREATE TABLE institutions (
        institution_id INTEGER PRIMARY KEY AUTOINCREMENT,
        institution_name VARCHAR(255) NOT NULL UNIQUE,
        institution_code VARCHAR(20) NOT NULL UNIQUE,
        address TEXT,
        contact_email VARCHAR(255),
        contact_phone VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Faculties
    CREATE TABLE faculties (
        faculty_id INTEGER PRIMARY KEY AUTOINCREMENT,
        institution_id INTEGER NOT NULL,
        faculty_name VARCHAR(255) NOT NULL,
        faculty_code VARCHAR(20) NOT NULL,
        dean_name VARCHAR(255),
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (institution_id) REFERENCES institutions(institution_id) ON DELETE CASCADE,
        UNIQUE(institution_id, faculty_code)
    );

    -- Departments
    CREATE TABLE departments (
        department_id INTEGER PRIMARY KEY AUTOINCREMENT,
        faculty_id INTEGER NOT NULL,
        department_name VARCHAR(255) NOT NULL,
        department_code VARCHAR(20) NOT NULL,
        head_of_department VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (faculty_id) REFERENCES faculties(faculty_id) ON DELETE CASCADE,
        UNIQUE(faculty_id, department_code)
    );

    -- User roles
    CREATE TABLE user_roles (
        role_id INTEGER PRIMARY KEY AUTOINCREMENT,
        role_name VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        permissions TEXT
    );

    -- Users (staff/lecturers)
    CREATE TABLE users (
        user_id INTEGER PRIMARY KEY AUTOINCREMENT,
        institution_id INTEGER NOT NULL,
        faculty_id INTEGER,
        department_id INTEGER,
        employee_id VARCHAR(50) UNIQUE,
        username VARCHAR(100) NOT NULL UNIQUE,
        email VARCHAR(255) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        role_id INTEGER NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        last_login TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (institution_id) REFERENCES institutions(institution_id) ON DELETE CASCADE,
        FOREIGN KEY (faculty_id) REFERENCES faculties(faculty_id) ON DELETE SET NULL,
        FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL,
        FOREIGN KEY (role_id) REFERENCES user_roles(role_id) ON DELETE RESTRICT
    );

    -- Students
    CREATE TABLE students (
        student_id INTEGER PRIMARY KEY AUTOINCREMENT,
        institution_id INTEGER NOT NULL,
        faculty_id INTEGER NOT NULL,
        department_id INTEGER,
        student_number VARCHAR(50) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        date_of_birth DATE,
        phone VARCHAR(20),
        address TEXT,
        enrollment_date DATE NOT NULL,
        graduation_date DATE,
        is_active BOOLEAN DEFAULT 1,
        is_facial_registered BOOLEAN DEFAULT 0,
        registration_completed BOOLEAN DEFAULT 0,
        last_login TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (institution_id) REFERENCES institutions(institution_id) ON DELETE CASCADE,
        FOREIGN KEY (faculty_id) REFERENCES faculties(faculty_id) ON DELETE CASCADE,
        FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL,
        UNIQUE(institution_id, student_number)
    );

    -- Courses
    CREATE TABLE courses (
        course_id INTEGER PRIMARY KEY AUTOINCREMENT,
        institution_id INTEGER NOT NULL,
        faculty_id INTEGER NOT NULL,
        department_id INTEGER,
        course_code VARCHAR(20) NOT NULL,
        course_name VARCHAR(255) NOT NULL,
        course_description TEXT,
        duration_years INTEGER DEFAULT 3,
        qualification_type VARCHAR(50) DEFAULT 'Bachelor',
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (institution_id) REFERENCES institutions(institution_id) ON DELETE CASCADE,
        FOREIGN KEY (faculty_id) REFERENCES faculties(faculty_id) ON DELETE CASCADE,
        FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL,
        UNIQUE(institution_id, course_code)
    );

    -- Course years
    CREATE TABLE course_years (
        course_year_id INTEGER PRIMARY KEY AUTOINCREMENT,
        course_id INTEGER NOT NULL,
        year_number INTEGER NOT NULL,
        year_name VARCHAR(100),
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
        UNIQUE(course_id, year_number)
    );

    -- Modules
    CREATE TABLE modules (
        module_id INTEGER PRIMARY KEY AUTOINCREMENT,
        course_year_id INTEGER NOT NULL,
        module_code VARCHAR(20) NOT NULL,
        module_name VARCHAR(255) NOT NULL,
        module_description TEXT,
        credits INTEGER DEFAULT 3,
        semester VARCHAR(20),
        is_core BOOLEAN DEFAULT 1,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (course_year_id) REFERENCES course_years(course_year_id) ON DELETE CASCADE,
        UNIQUE(course_year_id, module_code)
    );

    -- Student enrollments in specific course years
    CREATE TABLE student_enrollments (
        enrollment_id INTEGER PRIMARY KEY AUTOINCREMENT,
        student_id INTEGER NOT NULL,
        course_id INTEGER NOT NULL,
        course_year_id INTEGER NOT NULL,
        academic_year VARCHAR(10) NOT NULL,
        enrollment_date DATE NOT NULL,
        enrollment_status VARCHAR(20) DEFAULT 'active',
        final_grade VARCHAR(5),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
        FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
        FOREIGN KEY (course_year_id) REFERENCES course_years(course_year_id) ON DELETE CASCADE,
        UNIQUE(student_id, course_id, academic_year)
    );

    -- Student module registrations
    CREATE TABLE module_registrations (
        registration_id INTEGER PRIMARY KEY AUTOINCREMENT,
        enrollment_id INTEGER NOT NULL,
        module_id INTEGER NOT NULL,
        registration_date DATE NOT NULL,
        registration_status VARCHAR(20) DEFAULT 'active',
        final_mark DECIMAL(5,2),
        final_grade VARCHAR(5),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (enrollment_id) REFERENCES student_enrollments(enrollment_id) ON DELETE CASCADE,
        FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
        UNIQUE(enrollment_id, module_id)
    );

    -- Student face encodings for facial recognition
    CREATE TABLE student_face_encodings (
        encoding_id INTEGER PRIMARY KEY AUTOINCREMENT,
        student_id INTEGER NOT NULL UNIQUE,
        face_encoding TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE
    );

    -- Module lecturers assignment
    CREATE TABLE module_lecturers (
        assignment_id INTEGER PRIMARY KEY AUTOINCREMENT,
        module_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        assigned_date DATE DEFAULT CURRENT_DATE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        UNIQUE(module_id, user_id)
    );

    -- Attendance sessions (lecturer-controlled attendance windows)
    CREATE TABLE attendance_sessions (
        session_id INTEGER PRIMARY KEY AUTOINCREMENT,
        module_id INTEGER NOT NULL,
        lecturer_id INTEGER NOT NULL,
        session_name VARCHAR(255) NOT NULL,
        session_type VARCHAR(50) DEFAULT 'lecture',
        session_date DATE NOT NULL,
        scheduled_start_time TIME NOT NULL,
        scheduled_end_time TIME NOT NULL,
        attendance_window_start TIMESTAMP,
        attendance_window_end TIMESTAMP,
        is_active BOOLEAN DEFAULT FALSE,
        is_completed BOOLEAN DEFAULT FALSE,
        location VARCHAR(255),
        max_students INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
        FOREIGN KEY (lecturer_id) REFERENCES users(user_id) ON DELETE CASCADE
    );

    -- Student attendance records (individual attendance marks)
    CREATE TABLE student_attendance (
        attendance_id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id INTEGER NOT NULL,
        student_id INTEGER NOT NULL,
        marked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        recognition_confidence DECIMAL(5,4) DEFAULT 1.0000,
        attendance_status VARCHAR(20) DEFAULT 'present',
        recognition_method VARCHAR(20) DEFAULT 'facial',
        ip_address VARCHAR(45),
        device_info TEXT,
        is_verified BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (session_id) REFERENCES attendance_sessions(session_id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
        UNIQUE(session_id, student_id)
    );

    -- Attendance marks calculation (per module per student)
    CREATE TABLE attendance_marks (
        mark_id INTEGER PRIMARY KEY AUTOINCREMENT,
        student_id INTEGER NOT NULL,
        module_id INTEGER NOT NULL,
        academic_year VARCHAR(10) NOT NULL,
        total_sessions INTEGER DEFAULT 0,
        attended_sessions INTEGER DEFAULT 0,
        attendance_percentage DECIMAL(5,2) DEFAULT 0.00,
        attendance_mark DECIMAL(5,2) DEFAULT 0.00,
        final_attendance_contribution DECIMAL(5,2) DEFAULT 0.00,
        last_calculated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_finalized BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
        FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
        UNIQUE(student_id, module_id, academic_year)
    );

    -- Legacy attendance records table (keeping for compatibility)
    CREATE TABLE attendance_records (
        record_id INTEGER PRIMARY KEY AUTOINCREMENT,
        student_id INTEGER NOT NULL,
        module_id INTEGER NOT NULL,
        attendance_date DATE NOT NULL,
        check_in_time TIMESTAMP,
        check_out_time TIMESTAMP,
        status VARCHAR(20) DEFAULT 'present' CHECK(status IN ('present', 'late', 'absent')),
        confidence_score DECIMAL(3,2),
        recognition_method VARCHAR(20) DEFAULT 'facial' CHECK(recognition_method IN ('facial', 'manual')),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
        FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
        UNIQUE(student_id, module_id, attendance_date)
    );
    """

    # Execute table creation
    conn.executescript(tables_sql)
    conn.commit()
    print("Database schema created successfully")

    return conn

def insert_sample_data(conn):
    """Insert sample data for testing"""

    try:
        # Insert user roles first
        roles = [
            ('super_admin', 'System Administrator', '{"all": true}'),
            ('admin', 'Institution Administrator', '{"manage_users": true, "view_all_attendance": true}'),
            ('lecturer', 'Course Lecturer', '{"view_own_courses": true, "mark_attendance": true}'),
            ('assistant', 'Teaching Assistant', '{"view_assigned_courses": true}')
        ]

        for role_name, description, permissions in roles:
            conn.execute("""
                INSERT INTO user_roles (role_name, description, permissions)
                VALUES (?, ?, ?)
            """, (role_name, description, permissions))

        # Insert institution
        conn.execute("""
            INSERT INTO institutions (institution_name, institution_code, address, contact_email)
            VALUES ('University of Technology', 'UTECH', '123 University Ave, Tech City', '<EMAIL>')
        """)

        # Insert faculties
        faculties = [
            ('Engineering', 'ENG', 'Prof. Smith', 'Faculty of Engineering and Technology'),
            ('Business', 'BUS', 'Dr. Johnson', 'Faculty of Business and Management'),
            ('Science', 'SCI', 'Prof. Williams', 'Faculty of Natural Sciences'),
            ('Arts', 'ART', 'Dr. Brown', 'Faculty of Arts and Humanities'),
            ('Medicine', 'MED', 'Prof. Davis', 'Faculty of Medicine and Health Sciences'),
            ('Law', 'LAW', 'Dr. Wilson', 'Faculty of Law and Legal Studies')
        ]

        for faculty_name, faculty_code, dean, description in faculties:
            conn.execute("""
                INSERT INTO faculties (institution_id, faculty_name, faculty_code, dean_name, description)
                VALUES (1, ?, ?, ?, ?)
            """, (faculty_name, faculty_code, dean, description))
        
        # Insert departments
        departments = [
            (1, 'Computer Science', 'CS'),  # Engineering
            (1, 'Electrical Engineering', 'EE'),
            (1, 'Mechanical Engineering', 'ME'),
            (2, 'Business Administration', 'BA'),  # Business
            (2, 'Accounting', 'ACC'),
            (3, 'Mathematics', 'MATH'),  # Science
            (3, 'Physics', 'PHYS'),
            (4, 'English Literature', 'ENG'),  # Arts
            (4, 'Fine Arts', 'FA')
        ]
        
        for faculty_id, dept_name, dept_code in departments:
            conn.execute("""
                INSERT INTO departments (faculty_id, department_name, department_code)
                VALUES (?, ?, ?)
            """, (faculty_id, dept_name, dept_code))
        
        # Insert courses
        courses = [
            (1, 1, 'CS001', 'Bachelor of Computer Science', 'Comprehensive computer science program', 3, 'Bachelor'),
            (1, 1, 'IT001', 'Bachelor of Information Technology', 'IT and systems management program', 3, 'Bachelor'),
            (1, 2, 'EE001', 'Bachelor of Electrical Engineering', 'Electrical engineering program', 4, 'Bachelor'),
            (2, 4, 'BA001', 'Bachelor of Business Administration', 'Business management program', 3, 'Bachelor'),
            (2, 5, 'ACC001', 'Bachelor of Accounting', 'Accounting and finance program', 3, 'Bachelor'),
            (3, 6, 'MATH001', 'Bachelor of Mathematics', 'Pure and applied mathematics', 3, 'Bachelor')
        ]
        
        for faculty_id, dept_id, course_code, course_name, description, duration, qualification in courses:
            conn.execute("""
                INSERT INTO courses (institution_id, faculty_id, department_id, course_code, 
                                   course_name, course_description, duration_years, qualification_type)
                VALUES (1, ?, ?, ?, ?, ?, ?, ?)
            """, (faculty_id, dept_id, course_code, course_name, description, duration, qualification))
        
        # Insert course years
        for course_id in range(1, 7):  # For each course
            for year in range(1, 4):  # 3 years each
                year_name = f"Year {year}" if year <= 3 else f"Year {year}"
                conn.execute("""
                    INSERT INTO course_years (course_id, year_number, year_name)
                    VALUES (?, ?, ?)
                """, (course_id, year, year_name))
        
        # Insert comprehensive modules for all courses

        # Computer Science Modules (Course ID 1)
        cs_modules = [
            # Year 1 (course_year_id = 1)
            (1, 'CS101', 'Programming Fundamentals', 'Introduction to programming with Python', 6, 'Semester 1', True),
            (1, 'CS102', 'Data Structures & Algorithms', 'Basic data structures and algorithms', 6, 'Semester 1', True),
            (1, 'MATH101', 'Mathematics for Computing', 'Discrete mathematics and logic', 6, 'Semester 1', True),
            (1, 'CS103', 'Object-Oriented Programming', 'OOP principles with Java', 6, 'Semester 2', True),
            (1, 'CS104', 'Database Systems', 'Database design and SQL', 6, 'Semester 2', True),
            (1, 'ENG101', 'Technical Communication', 'Writing and presentation skills', 3, 'Semester 2', True),
            (1, 'CS105', 'Web Development Basics', 'HTML, CSS, JavaScript fundamentals', 3, 'Semester 2', False),

            # Year 2 (course_year_id = 2)
            (2, 'CS201', 'Advanced Programming', 'Advanced programming concepts and patterns', 6, 'Semester 1', True),
            (2, 'CS202', 'Computer Networks', 'Network protocols and architecture', 6, 'Semester 1', True),
            (2, 'CS203', 'Operating Systems', 'OS concepts and system programming', 6, 'Semester 1', True),
            (2, 'CS204', 'Software Engineering', 'Software development methodologies', 6, 'Semester 2', True),
            (2, 'CS205', 'Computer Graphics', 'Graphics programming and visualization', 6, 'Semester 2', False),
            (2, 'CS206', 'Mobile App Development', 'Android and iOS development', 6, 'Semester 2', False),
            (2, 'MATH201', 'Statistics for Computing', 'Statistical analysis and probability', 3, 'Semester 1', True),

            # Year 3 (course_year_id = 3)
            (3, 'CS301', 'Artificial Intelligence', 'AI algorithms and machine learning', 6, 'Semester 1', True),
            (3, 'CS302', 'Cybersecurity', 'Information security and cryptography', 6, 'Semester 1', True),
            (3, 'CS303', 'Final Year Project', 'Capstone project development', 12, 'Full Year', True),
            (3, 'CS304', 'Cloud Computing', 'Cloud platforms and distributed systems', 6, 'Semester 2', False),
            (3, 'CS305', 'Data Science', 'Big data analytics and visualization', 6, 'Semester 2', False),
            (3, 'CS306', 'Game Development', 'Game design and development', 6, 'Semester 1', False),
        ]

        # Information Technology Modules (Course ID 2)
        it_modules = [
            # Year 1 (course_year_id = 4)
            (4, 'IT101', 'Introduction to IT', 'Overview of information technology', 6, 'Semester 1', True),
            (4, 'IT102', 'Computer Hardware', 'Hardware components and assembly', 6, 'Semester 1', True),
            (4, 'IT103', 'Network Fundamentals', 'Basic networking concepts', 6, 'Semester 1', True),
            (4, 'IT104', 'Programming Logic', 'Problem solving and algorithms', 6, 'Semester 2', True),
            (4, 'IT105', 'Database Basics', 'Introduction to databases', 6, 'Semester 2', True),
            (4, 'IT106', 'Digital Literacy', 'Computer applications and tools', 3, 'Semester 2', True),

            # Year 2 (course_year_id = 5)
            (5, 'IT201', 'System Administration', 'Server and network administration', 6, 'Semester 1', True),
            (5, 'IT202', 'Web Technologies', 'Web development and deployment', 6, 'Semester 1', True),
            (5, 'IT203', 'IT Project Management', 'Project management methodologies', 6, 'Semester 2', True),
            (5, 'IT204', 'Information Security', 'Security policies and procedures', 6, 'Semester 2', True),
            (5, 'IT205', 'Cloud Services', 'Cloud computing platforms', 6, 'Semester 1', False),

            # Year 3 (course_year_id = 6)
            (6, 'IT301', 'Enterprise Systems', 'ERP and business systems', 6, 'Semester 1', True),
            (6, 'IT302', 'IT Capstone Project', 'Final project implementation', 12, 'Full Year', True),
            (6, 'IT303', 'Emerging Technologies', 'Latest IT trends and innovations', 6, 'Semester 2', False),
        ]

        # Business Administration Modules (Course ID 4)
        ba_modules = [
            # Year 1 (course_year_id = 10)
            (10, 'BA101', 'Introduction to Business', 'Business fundamentals and concepts', 6, 'Semester 1', True),
            (10, 'BA102', 'Accounting Principles', 'Basic accounting and bookkeeping', 6, 'Semester 1', True),
            (10, 'BA103', 'Business Mathematics', 'Mathematical applications in business', 6, 'Semester 1', True),
            (10, 'BA104', 'Marketing Fundamentals', 'Introduction to marketing', 6, 'Semester 2', True),
            (10, 'BA105', 'Business Communication', 'Professional communication skills', 3, 'Semester 2', True),
            (10, 'BA106', 'Economics for Business', 'Microeconomics and macroeconomics', 6, 'Semester 2', True),

            # Year 2 (course_year_id = 11)
            (11, 'BA201', 'Management Principles', 'Management theories and practices', 6, 'Semester 1', True),
            (11, 'BA202', 'Financial Management', 'Corporate finance and investment', 6, 'Semester 1', True),
            (11, 'BA203', 'Human Resource Management', 'HR policies and practices', 6, 'Semester 2', True),
            (11, 'BA204', 'Operations Management', 'Production and operations', 6, 'Semester 2', True),
            (11, 'BA205', 'Digital Marketing', 'Online marketing strategies', 6, 'Semester 1', False),

            # Year 3 (course_year_id = 12)
            (12, 'BA301', 'Strategic Management', 'Business strategy and planning', 6, 'Semester 1', True),
            (12, 'BA302', 'Business Ethics', 'Corporate social responsibility', 3, 'Semester 1', True),
            (12, 'BA303', 'Business Capstone', 'Final business project', 9, 'Semester 2', True),
            (12, 'BA304', 'International Business', 'Global business operations', 6, 'Semester 2', False),
        ]

        # Combine all modules
        all_modules = cs_modules + it_modules + ba_modules

        for course_year_id, module_code, module_name, description, credits, semester, is_core in all_modules:
            conn.execute("""
                INSERT INTO modules (course_year_id, module_code, module_name, module_description,
                                   credits, semester, is_core)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (course_year_id, module_code, module_name, description, credits, semester, is_core))
        
        # Insert sample staff user (using simple hash for demo - use bcrypt in production)
        password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
        conn.execute("""
            INSERT INTO users (institution_id, faculty_id, username, email, password_hash,
                             first_name, last_name, role_id)
            VALUES (1, 1, 'admin', '<EMAIL>', ?, 'System', 'Administrator', 1)
        """, (password_hash,))

        # Insert sample lecturer
        lecturer_hash = hashlib.sha256('lecturer123'.encode()).hexdigest()
        conn.execute("""
            INSERT INTO users (institution_id, faculty_id, department_id, username, email, password_hash,
                             first_name, last_name, role_id)
            VALUES (1, 1, 1, 'prof.smith', '<EMAIL>', ?, 'John', 'Smith', 3)
        """, (lecturer_hash,))

        # Get the lecturer user_id
        lecturer_id = conn.execute("SELECT user_id FROM users WHERE username = 'prof.smith'").fetchone()[0]

        # Assign lecturer to some modules (first 10 modules)
        for module_id in range(1, 11):
            conn.execute("""
                INSERT INTO module_lecturers (module_id, user_id)
                VALUES (?, ?)
            """, (module_id, lecturer_id))

        conn.commit()
        print("Sample data inserted successfully")
        
        # Print summary
        print("\n=== Database Summary ===")
        
        # Count records
        tables = ['institutions', 'faculties', 'departments', 'courses', 'course_years', 'modules', 'users']
        for table in tables:
            count = conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
            print(f"{table.capitalize()}: {count} records")
        
        print("\n=== Sample Login Credentials ===")
        print("Admin User:")
        print("  Username: admin")
        print("  Password: admin123")
        print("\nLecturer User:")
        print("  Username: prof.smith")
        print("  Password: lecturer123")
        
    except sqlite3.Error as e:
        print(f"Error inserting sample data: {e}")
        conn.rollback()

def main():
    """Main function"""
    print("Initializing Facial Recognition Attendance Database...")
    print("=" * 50)
    
    # Create database and schema
    conn = create_database()
    
    # Insert sample data
    insert_sample_data(conn)
    
    # Close connection
    conn.close()
    
    print("\n" + "=" * 50)
    print("Database initialization completed!")
    print("You can now run the Flask application with: python app.py")

if __name__ == "__main__":
    main()
