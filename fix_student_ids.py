import sqlite3

def fix_student_ids():
    conn = sqlite3.connect('attendance_fixed.db')
    
    print("🔧 Fixing student_id values in module_registrations...")
    
    # Update student_id column with correct values from student_enrollments
    conn.execute('''
        UPDATE module_registrations 
        SET student_id = (
            SELECT se.student_id 
            FROM student_enrollments se 
            WHERE se.enrollment_id = module_registrations.enrollment_id
        )
        WHERE student_id IS NULL OR student_id NOT IN (
            SELECT student_id FROM students
        )
    ''')
    
    conn.commit()
    
    print("✅ Fixed student_id values")
    
    # Verify the fix
    print("\nVerifying fixed data:")
    rows = conn.execute('''
        SELECT mr.registration_id, mr.enrollment_id, mr.module_id, mr.student_id, s.student_number
        FROM module_registrations mr
        LEFT JOIN students s ON mr.student_id = s.student_id
        LIMIT 5
    ''').fetchall()
    
    for row in rows:
        print(f"  registration_id: {row[0]}, enrollment_id: {row[1]}, module_id: {row[2]}, student_id: {row[3]}, student_number: {row[4]}")
    
    conn.close()

if __name__ == "__main__":
    fix_student_ids()
