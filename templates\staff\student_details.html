{% extends "base.html" %}

{% block title %}{{ student.first_name }} {{ student.last_name }} - Student Details{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="display-6">
                    <i class="fas fa-user-graduate me-2"></i>
                    Student Details
                </h1>
                <a href="{{ url_for('manage_students') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Students
                </a>
            </div>
        </div>
    </div>

    <!-- Student Info Card -->
    <div class="row g-4 mb-4">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-id-card me-2"></i>
                        Student Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted small">Student Number</label>
                                <div class="fw-bold">{{ student.student_number }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted small">First Name</label>
                                <div>{{ student.first_name }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted small">Last Name</label>
                                <div>{{ student.last_name }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted small">Email</label>
                                <div>{{ student.email }}</div>
                            </div>
                            {% if student.phone_number %}
                            <div class="mb-3">
                                <label class="form-label text-muted small">Phone</label>
                                <div>{{ student.phone_number }}</div>
                            </div>
                            {% endif %}
                            <div class="mb-3">
                                <label class="form-label text-muted small">Registered</label>
                                <div>{{ student.created_at if student.created_at else 'N/A' }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Quick Stats
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-lg bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2">
                            {{ student.first_name[0] }}{{ student.last_name[0] }}
                        </div>
                        <h6 class="mb-0">{{ student.first_name }} {{ student.last_name }}</h6>
                        <small class="text-muted">{{ student.student_number }}</small>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Modules:</span>
                        <span class="fw-bold">{{ enrollments|length }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted">Attendance Records:</span>
                        <span class="fw-bold">{{ recent_attendance|length }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Module Enrollments -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-book me-2"></i>
                        Module Enrollments
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if enrollments %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Module Code</th>
                                    <th>Module Name</th>
                                    <th>Enrollment Date</th>
                                    <th>Status</th>
                                    <th>Attendance</th>
                                    <th>Percentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in enrollments %}
                                <tr>
                                    <td>
                                        <span class="fw-bold text-primary">{{ enrollment.module_code }}</span>
                                    </td>
                                    <td>{{ enrollment.module_name }}</td>
                                    <td>
                                        <small class="text-muted">{{ enrollment.registration_date }}</small>
                                    </td>
                                    <td>
                                        {% if enrollment.registration_status == 'active' %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ enrollment.registration_status.title() }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ enrollment.attendance_count }}/{{ enrollment.total_sessions }}</span>
                                    </td>
                                    <td>
                                        {% set percentage = (enrollment.attendance_count / enrollment.total_sessions * 100) if enrollment.total_sessions > 0 else 0 %}
                                        {% if percentage >= 80 %}
                                            <span class="badge bg-success">{{ "%.1f"|format(percentage) }}%</span>
                                        {% elif percentage >= 60 %}
                                            <span class="badge bg-warning">{{ "%.1f"|format(percentage) }}%</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ "%.1f"|format(percentage) }}%</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-book-open text-muted" style="font-size: 3rem; opacity: 0.5;"></i>
                        <p class="text-muted mt-3 mb-0">No module enrollments found</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Attendance -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        Recent Attendance Records
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if recent_attendance %}
                    <div class="list-group list-group-flush">
                        {% for record in recent_attendance %}
                        <div class="list-group-item border-0 px-3 py-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ record.module_code }} - {{ record.session_name }}</h6>
                                    <p class="mb-1 small text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ record.attendance_time }}
                                    </p>
                                </div>
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>{{ record.attendance_status.title() }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clock text-muted" style="font-size: 3rem; opacity: 0.5;"></i>
                        <p class="text-muted mt-3 mb-0">No attendance records found</p>
                        <small class="text-muted">Attendance records will appear here once the student starts attending sessions</small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 64px;
    height: 64px;
    font-size: 1.5rem;
    font-weight: 600;
}
</style>
{% endblock %}
