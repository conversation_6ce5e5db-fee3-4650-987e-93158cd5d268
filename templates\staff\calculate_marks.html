{% extends "base.html" %}

{% block title %}Calculate Attendance Marks - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1">Calculate Attendance Marks</h1>
                    <p class="text-muted mb-0">Finalize attendance marks and grade contributions</p>
                </div>
                <div>
                    <a href="{{ url_for('staff_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Calculation Form -->
    <div class="row mb-5">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        Calculate Marks
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="module_id" class="form-label">Module (Optional)</label>
                                <select class="form-select" id="module_id" name="module_id">
                                    <option value="">All My Modules</option>
                                    {% for module in modules %}
                                    <option value="{{ module.module_id }}">
                                        {{ module.module_code }} - {{ module.module_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Leave blank to calculate for all your modules</div>
                            </div>
                            <div class="col-md-6">
                                <label for="academic_year" class="form-label">Academic Year</label>
                                <select class="form-select" id="academic_year" name="academic_year">
                                    <option value="2024/2025" selected>2024/2025</option>
                                    <option value="2023/2024">2023/2024</option>
                                    <option value="2025/2026">2025/2026</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-calculator me-2"></i>
                                Calculate Attendance Marks
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Information Panel -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Calculation Rules
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Session Mark:</strong> 100% for attended sessions
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-times text-danger me-2"></i>
                            <strong>Missed Sessions:</strong> 0% mark
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-percentage text-info me-2"></i>
                            <strong>Attendance %:</strong> (Attended / Total) × 100
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-star text-warning me-2"></i>
                            <strong>Final Contribution:</strong> Attendance % × 10%
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Calculations -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Recent Calculations
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if recent_calculations %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Student</th>
                                    <th>Module</th>
                                    <th>Sessions</th>
                                    <th>Attended</th>
                                    <th>Attendance %</th>
                                    <th>Grade Contribution</th>
                                    <th>Calculated</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for calc in recent_calculations %}
                                <tr>
                                    <td>
                                        <strong>{{ calc.student_number }}</strong><br>
                                        <small class="text-muted">{{ calc.first_name }} {{ calc.last_name }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ calc.module_code }}</strong><br>
                                        <small class="text-muted">{{ calc.module_name }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ calc.total_sessions }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ calc.attended_sessions }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="progress me-2" style="width: 50px; height: 15px;">
                                                <div class="progress-bar 
                                                    {% if calc.attendance_percentage >= 80 %}bg-success
                                                    {% elif calc.attendance_percentage >= 60 %}bg-warning
                                                    {% else %}bg-danger{% endif %}" 
                                                    style="width: {{ calc.attendance_percentage }}%"></div>
                                            </div>
                                            <span class="small">{{ "%.1f"|format(calc.attendance_percentage) }}%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <strong class="{% if calc.final_attendance_contribution >= 8 %}text-success{% elif calc.final_attendance_contribution >= 6 %}text-warning{% else %}text-danger{% endif %}">
                                            {{ "%.2f"|format(calc.final_attendance_contribution) }}%
                                        </strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ calc.last_calculated }}
                                        </small>
                                    </td>
                                    <td>
                                        {% if calc.attendance_percentage >= 80 %}
                                        <span class="badge bg-success">Excellent</span>
                                        {% elif calc.attendance_percentage >= 60 %}
                                        <span class="badge bg-warning">Good</span>
                                        {% elif calc.attendance_percentage >= 40 %}
                                        <span class="badge bg-danger">Poor</span>
                                        {% else %}
                                        <span class="badge bg-dark">Critical</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calculator text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3 text-muted">No Calculations Yet</h5>
                        <p class="text-muted">Use the form above to calculate attendance marks for your modules.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    {% if recent_calculations %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <h6 class="mb-3">Export Options</h6>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-success" onclick="exportToCSV()">
                            <i class="fas fa-file-csv me-2"></i>Export to CSV
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-2"></i>Export to Excel
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf me-2"></i>Export to PDF
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function exportToCSV() {
    // Simple CSV export functionality
    const table = document.querySelector('table');
    if (!table) return;
    
    let csv = [];
    const rows = table.querySelectorAll('tr');
    
    for (let i = 0; i < rows.length; i++) {
        const row = [];
        const cols = rows[i].querySelectorAll('td, th');
        
        for (let j = 0; j < cols.length; j++) {
            let cellText = cols[j].innerText.replace(/\n/g, ' ').replace(/,/g, ';');
            row.push('"' + cellText + '"');
        }
        csv.push(row.join(','));
    }
    
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'attendance_marks_' + new Date().toISOString().split('T')[0] + '.csv';
    a.click();
    window.URL.revokeObjectURL(url);
}

function exportToExcel() {
    alert('Excel export functionality would be implemented with a library like SheetJS');
}

function exportToPDF() {
    alert('PDF export functionality would be implemented with a library like jsPDF');
}

// Auto-refresh every 30 seconds if there are active calculations
setInterval(function() {
    const hasCalculations = {{ 'true' if recent_calculations else 'false' }};
    if (hasCalculations) {
        // Could implement auto-refresh here
    }
}, 30000);
</script>

<style>
.progress {
    border-radius: 10px;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.badge {
    font-size: 0.8rem;
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
</style>
{% endblock %}
