{% extends "base.html" %}

{% block title %}Student Dashboard - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="display-6">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Student Dashboard
                </h1>
                <div>
                    <span class="badge bg-success fs-6">
                        <i class="fas fa-user-check me-1"></i>
                        {{ current_user.username }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Welcome Card -->
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-primary text-white">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="card-title mb-2">
                                <i class="fas fa-graduation-cap me-2"></i>
                                Welcome to Your Dashboard!
                            </h4>
                            <p class="card-text mb-0">
                                Your facial recognition setup is complete. You're ready to use the automated attendance system.
                            </p>
                        </div>
                        <div class="col-md-4 text-center">
                            <i class="fas fa-user-circle" style="font-size: 4rem; opacity: 0.8;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2 text-warning"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <a href="{{ url_for('student_facial_setup') }}" class="btn btn-outline-primary">
                            <i class="fas fa-camera me-2"></i>
                            Facial Recognition Setup
                        </a>
                        <a href="{{ url_for('student_facial_capture') }}" class="btn btn-outline-success">
                            <i class="fas fa-video me-2"></i>
                            Capture Facial Data
                        </a>
                        <a href="{{ url_for('student_facial_test') }}" class="btn btn-outline-warning">
                            <i class="fas fa-user-check me-2"></i>
                            Test Recognition
                        </a>
                        <a href="{{ url_for('student_attendance') }}" class="btn btn-outline-success">
                            <i class="fas fa-calendar-check me-2"></i>
                            Mark Attendance
                        </a>
                        <a href="{{ url_for('view_attendance_records') }}" class="btn btn-outline-info">
                            <i class="fas fa-chart-bar me-2"></i>
                            View Records
                        </a>
                        <a href="{{ url_for('student_view_modules') }}" class="btn btn-outline-info">
                            <i class="fas fa-book me-2"></i>
                            My Modules
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2 text-info"></i>
                        System Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Registration Status</span>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Complete
                            </span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Facial Recognition</span>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Setup Complete
                            </span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Attendance Tracking</span>
                            <span class="badge bg-warning">
                                <i class="fas fa-clock me-1"></i>Ready
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2 text-secondary"></i>
                        Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    {% if active_sessions %}
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-broadcast-tower me-2"></i>
                            Active Attendance Sessions
                        </h6>
                        {% for session in active_sessions %}
                        <div class="d-flex justify-content-between align-items-center border-bottom py-3">
                            <div>
                                <h6 class="mb-1">{{ session.session_name }}</h6>
                                <small class="text-muted">{{ session.module_code }} - {{ session.module_name }}</small>
                                <br>
                                <small class="text-info">
                                    <i class="fas fa-calendar me-1"></i>{{ session.session_date }}
                                </small>
                            </div>
                            <div class="text-end">
                                {% if session.already_attended %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Attended
                                    </span>
                                {% else %}
                                    <a href="{{ url_for('student_attendance') }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-camera me-1"></i>Mark Attendance
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    {% endif %}

                    {% if recent_attendance %}
                        <h6 class="text-secondary mb-3 mt-4">
                            <i class="fas fa-history me-2"></i>
                            Recent Attendance Records
                        </h6>
                        {% for record in recent_attendance %}
                        <div class="d-flex justify-content-between align-items-center py-2">
                            <div>
                                <small class="fw-bold">{{ record.module_code }} - {{ record.session_name }}</small>
                                <br>
                                <small class="text-muted">{{ record.attendance_time }}</small>
                            </div>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>{{ record.attendance_status.title() }}
                            </span>
                        </div>
                        {% endfor %}
                    {% endif %}

                    {% if not active_sessions and not recent_attendance %}
                        <div class="text-center py-4">
                            <i class="fas fa-clock text-muted" style="font-size: 3rem; opacity: 0.5;"></i>
                            <p class="text-muted mt-3 mb-0">No recent activity to display</p>
                            <small class="text-muted">Your attendance records will appear here once classes begin</small>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-light">
                <div class="card-body p-4">
                    <h5 class="card-title">
                        <i class="fas fa-question-circle me-2 text-primary"></i>
                        Need Help?
                    </h5>
                    <p class="card-text mb-3">
                        If you experience any issues with the facial recognition system or have questions about attendance tracking, 
                        please contact your lecturer or the IT support team.
                    </p>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-envelope me-1"></i>
                                Email: <EMAIL>
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-phone me-1"></i>
                                Phone: +27 (0)31 373 2000
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.badge {
    font-size: 0.75rem;
}

.btn:disabled {
    opacity: 0.6;
}
</style>
{% endblock %}
