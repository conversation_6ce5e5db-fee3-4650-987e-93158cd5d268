{% extends "base.html" %}

{% block title %}Student Registration - Step 3: Course Selection{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Progress Steps -->
            <div class="progress-step mb-4">
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Basic Info</small>
                </div>
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Faculty</small>
                </div>
                <div class="step active">
                    <div class="step-number">3</div>
                    <div class="step-line"></div>
                    <small>Course</small>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-line"></div>
                    <small>Year</small>
                </div>
                <div class="step">
                    <div class="step-number">5</div>
                    <div class="step-line"></div>
                    <small>Modules</small>
                </div>
                <div class="step">
                    <div class="step-number">6</div>
                    <small>Facial Setup</small>
                </div>
            </div>

            <!-- Course Selection -->
            <div class="card shadow-lg border-0">
                <div class="card-header text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>
                        Select Your Course
                    </h2>
                    <p class="mb-0 mt-2">Step 3: Choose your degree program in {{ faculty.faculty_name }}</p>
                </div>
                <div class="card-body p-5">
                    {% if courses %}
                        <div class="row g-4">
                            {% for course in courses %}
                            <div class="col-md-6">
                                <div class="card h-100 course-card border-2" style="cursor: pointer;" 
                                     onclick="selectCourse({{ course.course_id }})">
                                    <div class="card-body p-4">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h5 class="card-title mb-0">{{ course.course_name }}</h5>
                                            <span class="badge bg-primary">{{ course.qualification_type }}</span>
                                        </div>
                                        
                                        <p class="text-muted small mb-2">
                                            <i class="fas fa-code me-1"></i>
                                            Course Code: {{ course.course_code }}
                                        </p>
                                        
                                        <p class="text-muted small mb-3">
                                            <i class="fas fa-clock me-1"></i>
                                            Duration: {{ course.duration_years }} years
                                        </p>
                                        
                                        {% if course.course_description %}
                                        <p class="card-text small mb-3">
                                            {{ course.course_description[:150] }}{% if course.course_description|length > 150 %}...{% endif %}
                                        </p>
                                        {% endif %}
                                        
                                        <div class="mt-auto">
                                            <span class="btn btn-outline-primary btn-sm w-100">
                                                <i class="fas fa-arrow-right me-1"></i>
                                                Select Course
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                            <h4 class="mt-3">No Courses Available</h4>
                            <p class="text-muted">
                                No courses are currently available in this faculty. 
                                Please contact the administration for assistance.
                            </p>
                            <a href="{{ url_for('student_select_faculty') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Choose Different Faculty
                            </a>
                        </div>
                    {% endif %}

                    <!-- Navigation Buttons -->
                    <div class="d-flex justify-content-between mt-5">
                        <a href="{{ url_for('student_select_faculty') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Faculty Selection
                        </a>
                        
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                Click on a course card to continue
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.course-card {
    transition: all 0.3s ease;
    border-color: #e9ecef !important;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    border-color: #667eea !important;
}

.course-card:hover .card-title {
    color: #667eea;
}

.course-card:hover .btn-outline-primary {
    background-color: #667eea;
    color: white;
    border-color: #667eea;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function selectCourse(courseId) {
    // Add loading state
    const cards = document.querySelectorAll('.course-card');
    cards.forEach(card => {
        card.style.opacity = '0.6';
        card.style.pointerEvents = 'none';
    });
    
    // Show loading message
    const selectedCard = event.currentTarget;
    const btn = selectedCard.querySelector('.btn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Loading...';
    btn.classList.remove('btn-outline-primary');
    btn.classList.add('btn-primary');
    
    // Navigate to year selection
    setTimeout(() => {
        window.location.href = `/student/select_year/${courseId}`;
    }, 500);
}
</script>
{% endblock %}
