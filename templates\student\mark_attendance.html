{% extends "base.html" %}

{% block title %}Mark Attendance - {{ session.session_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Session Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">{{ session.session_name }}</h2>
                            <p class="text-muted mb-2">{{ session.module_code }} - {{ session.module_name }}</p>
                            <div class="d-flex gap-3">
                                <span class="badge bg-info">{{ session.session_type|title }}</span>
                                <span class="badge bg-secondary">{{ session.session_date }}</span>
                                {% if session.location %}
                                <span class="badge bg-warning">{{ session.location }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="mb-2">
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-circle me-1"></i>ACTIVE
                                </span>
                            </div>
                            <div id="countdown" class="text-danger fw-bold fs-5"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Marking Interface -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header text-center py-4">
                    <h4 class="mb-0">
                        <i class="fas fa-camera me-2"></i>
                        Mark Your Attendance
                    </h4>
                    <p class="mb-0 mt-2">Position your face in the camera and click capture</p>
                </div>
                <div class="card-body p-5">
                    <!-- Camera Interface -->
                    <div class="text-center mb-4">
                        <div class="position-relative d-inline-block camera-container">
                            <video id="video" width="400" height="300" autoplay class="border rounded shadow-sm"></video>
                            <canvas id="canvas" width="400" height="300" style="display: none;"></canvas>

                            <!-- Face outline guide -->
                            <div class="face-guide-overlay">
                                <div class="face-outline">
                                    <div class="face-guide-text">Position your face here</div>
                                </div>
                            </div>

                            <!-- Face Detection Overlay -->
                            <div id="faceOverlay" class="position-absolute top-0 start-0 w-100 h-100 d-none">
                                <div class="face-detection-box"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Status Messages -->
                    <div id="statusMessages" class="text-center mb-4">
                        <div id="cameraStatus" class="alert alert-info">
                            <i class="fas fa-camera me-2"></i>
                            Initializing camera...
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="text-center">
                        <button id="captureBtn" class="btn btn-primary btn-lg me-3" disabled>
                            <i class="fas fa-camera me-2"></i>
                            Capture & Mark Attendance
                        </button>
                        <a href="{{ url_for('student_attendance') }}" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>
                            Cancel
                        </a>
                    </div>

                    <!-- Progress Indicator -->
                    <div id="progressContainer" class="mt-4 d-none">
                        <div class="progress">
                            <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <p id="progressText" class="text-center mt-2">Processing...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>Instructions for Best Results:</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="mb-0">
                            <li>Ensure good lighting on your face</li>
                            <li>Look directly at the camera</li>
                            <li>Remove sunglasses or hats if possible</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="mb-0">
                            <li>Keep your face centered in the frame</li>
                            <li>Stay still when capturing</li>
                            <li>Make sure your entire face is visible</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let video, canvas, context;
let stream = null;
let faceDetectionInterval = null;

// Session data
const sessionId = {{ session.session_id }};
const sessionEndTime = new Date('{{ session.attendance_window_end }}').getTime();

// Initialize camera and face detection
async function initializeCamera() {
    video = document.getElementById('video');
    canvas = document.getElementById('canvas');
    context = canvas.getContext('2d');
    
    try {
        // Request camera access
        stream = await navigator.mediaDevices.getUserMedia({ 
            video: { 
                width: 400, 
                height: 300,
                facingMode: 'user'
            } 
        });
        
        video.srcObject = stream;
        
        video.onloadedmetadata = function() {
            updateStatus('Camera ready! Position your face in the frame.', 'success');
            document.getElementById('captureBtn').disabled = false;
            
            // Start face detection
            startFaceDetection();
        };
        
    } catch (error) {
        console.error('Error accessing camera:', error);
        updateStatus('Camera access denied or not available. Please check your camera permissions.', 'danger');
    }
}

// Update status message
function updateStatus(message, type = 'info') {
    const statusDiv = document.getElementById('cameraStatus');
    statusDiv.className = `alert alert-${type}`;
    statusDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : type === 'danger' ? 'exclamation-triangle' : 'info'} me-2"></i>${message}`;
}

// Simple face detection using video analysis
function startFaceDetection() {
    // This is a simplified face detection - in a real implementation,
    // you might use a more sophisticated face detection library
    faceDetectionInterval = setInterval(() => {
        if (video.videoWidth > 0 && video.videoHeight > 0) {
            // For demo purposes, we'll assume a face is detected if video is playing
            document.getElementById('faceOverlay').classList.remove('d-none');
        }
    }, 100);
}

// Capture image and mark attendance
async function captureAndMarkAttendance() {
    if (!video.videoWidth || !video.videoHeight) {
        updateStatus('Camera not ready. Please wait...', 'warning');
        return;
    }
    
    // Show progress
    document.getElementById('progressContainer').classList.remove('d-none');
    document.getElementById('captureBtn').disabled = true;
    updateProgress(20, 'Capturing image...');
    
    // Capture image from video (flip back to normal orientation for processing)
    context.save();
    context.scale(-1, 1);
    context.drawImage(video, -400, 0, 400, 300);
    context.restore();
    const imageData = canvas.toDataURL('image/jpeg', 0.8);
    
    updateProgress(50, 'Processing facial recognition...');
    
    try {
        // Send to server for attendance marking
        const response = await fetch('/api/mark_attendance', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: sessionId,
                image: imageData
            })
        });
        
        updateProgress(80, 'Verifying attendance...');
        
        const result = await response.json();
        
        if (response.ok && result.success) {
            updateProgress(100, 'Attendance marked successfully!');
            
            // Show success message
            setTimeout(() => {
                updateStatus(`Attendance marked successfully! Confidence: ${(result.confidence * 100).toFixed(1)}%`, 'success');
                
                // Redirect after 3 seconds
                setTimeout(() => {
                    window.location.href = '/student/attendance';
                }, 3000);
            }, 1000);
            
        } else {
            throw new Error(result.error || 'Failed to mark attendance');
        }
        
    } catch (error) {
        console.error('Error marking attendance:', error);
        updateStatus(`Error: ${error.message}`, 'danger');
        document.getElementById('captureBtn').disabled = false;
        document.getElementById('progressContainer').classList.add('d-none');
    }
}

// Update progress bar
function updateProgress(percentage, text) {
    document.getElementById('progressBar').style.width = percentage + '%';
    document.getElementById('progressText').textContent = text;
}

// Countdown timer
function updateCountdown() {
    const now = new Date().getTime();
    const timeLeft = sessionEndTime - now;
    
    if (timeLeft > 0) {
        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
        
        document.getElementById('countdown').innerHTML = 
            `${minutes}:${seconds.toString().padStart(2, '0')} remaining`;
    } else {
        document.getElementById('countdown').innerHTML = 'Session Expired';
        document.getElementById('captureBtn').disabled = true;
        updateStatus('Attendance session has expired.', 'danger');
        
        // Redirect to attendance page
        setTimeout(() => {
            window.location.href = '/student/attendance';
        }, 3000);
    }
}

// Event listeners
document.getElementById('captureBtn').addEventListener('click', captureAndMarkAttendance);

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeCamera();
    
    // Update countdown every second
    setInterval(updateCountdown, 1000);
    updateCountdown();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (stream) {
        stream.getTracks().forEach(track => track.stop());
    }
    if (faceDetectionInterval) {
        clearInterval(faceDetectionInterval);
    }
});
</script>

<style>
.face-detection-box {
    border: 3px solid #28a745;
    border-radius: 10px;
    width: 200px;
    height: 200px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

#video {
    background-color: #000;
    object-fit: cover;
    transform: scaleX(-1); /* Flip the video horizontally to mirror the user */
}

.camera-container {
    position: relative;
    display: inline-block;
}

.face-guide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.face-outline {
    width: 200px;
    height: 250px;
    border: 3px solid rgba(255, 193, 7, 0.8);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    position: relative;
    background: rgba(255, 193, 7, 0.1);
    animation: pulse-guide 2s ease-in-out infinite;
}

.face-guide-text {
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255, 193, 7, 0.9);
    font-size: 12px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
    white-space: nowrap;
}

@keyframes pulse-guide {
    0% {
        border-color: rgba(255, 193, 7, 0.8);
        background: rgba(255, 193, 7, 0.1);
    }
    50% {
        border-color: rgba(255, 193, 7, 1);
        background: rgba(255, 193, 7, 0.2);
    }
    100% {
        border-color: rgba(255, 193, 7, 0.8);
        background: rgba(255, 193, 7, 0.1);
    }
}

.progress {
    height: 25px;
}

.progress-bar {
    font-size: 14px;
    line-height: 25px;
}

.card {
    border-radius: 15px;
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
}

.alert {
    border-radius: 10px;
}

.fs-5 {
    font-size: 1.25rem !important;
}

.fs-6 {
    font-size: 1rem !important;
}
</style>
{% endblock %}
