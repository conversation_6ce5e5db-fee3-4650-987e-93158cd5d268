{% extends "base.html" %}

{% block title %}Student Registration - Step 1{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Progress Steps -->
            <div class="progress-step mb-4">
                <div class="step active">
                    <div class="step-number">1</div>
                    <div class="step-line"></div>
                    <small>Basic Info</small>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-line"></div>
                    <small>Faculty</small>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-line"></div>
                    <small>Course</small>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-line"></div>
                    <small>Year</small>
                </div>
                <div class="step">
                    <div class="step-number">5</div>
                    <div class="step-line"></div>
                    <small>Modules</small>
                </div>
                <div class="step">
                    <div class="step-number">6</div>
                    <small>Facial Setup</small>
                </div>
            </div>

            <!-- Registration Form -->
            <div class="card shadow-lg border-0">
                <div class="card-header text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        Student Registration
                    </h2>
                    <p class="mb-0 mt-2">Step 1: Basic Information</p>
                </div>
                <div class="card-body p-5">
                    <form method="POST" id="registrationForm">
                        <div class="row g-3">
                            <!-- Student Number -->
                            <div class="col-md-6">
                                <label for="student_number" class="form-label">
                                    <i class="fas fa-id-card me-1"></i>Student Number *
                                </label>
                                <input type="text" class="form-control" id="student_number"
                                       name="student_number" required
                                       placeholder="e.g., 20240001"
                                       pattern="[0-9]{8}"
                                       title="Format: 8 digits only (e.g., 20240001)">
                                <div class="form-text">Format: 8 digits only</div>
                            </div>

                            <!-- Email -->
                            <div class="col-md-6">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Student Email *
                                </label>
                                <input type="email" class="form-control" id="email" 
                                       name="email" required 
                                       placeholder="<EMAIL>">
                                <div class="form-text">Use your official university email</div>
                            </div>

                            <!-- First Name -->
                            <div class="col-md-6">
                                <label for="first_name" class="form-label">
                                    <i class="fas fa-user me-1"></i>First Name *
                                </label>
                                <input type="text" class="form-control" id="first_name" 
                                       name="first_name" required 
                                       placeholder="Enter your first name">
                            </div>

                            <!-- Last Name -->
                            <div class="col-md-6">
                                <label for="last_name" class="form-label">
                                    <i class="fas fa-user me-1"></i>Last Name *
                                </label>
                                <input type="text" class="form-control" id="last_name" 
                                       name="last_name" required 
                                       placeholder="Enter your last name">
                            </div>

                            <!-- Password -->
                            <div class="col-md-6">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Password *
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" 
                                           name="password" required 
                                           placeholder="Create a strong password"
                                           minlength="8">
                                    <button class="btn btn-outline-secondary" type="button" 
                                            onclick="togglePassword('password')">
                                        <i class="fas fa-eye" id="password-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">Minimum 8 characters</div>
                            </div>

                            <!-- Confirm Password -->
                            <div class="col-md-6">
                                <label for="confirm_password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Confirm Password *
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="confirm_password" 
                                           name="confirm_password" required 
                                           placeholder="Confirm your password"
                                           minlength="8">
                                    <button class="btn btn-outline-secondary" type="button" 
                                            onclick="togglePassword('confirm_password')">
                                        <i class="fas fa-eye" id="confirm_password-eye"></i>
                                    </button>
                                </div>
                                <div id="password-match" class="form-text"></div>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="mt-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms and Conditions</a> 
                                    and <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">Privacy Policy</a> *
                                </label>
                            </div>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" id="biometric_consent" required>
                                <label class="form-check-label" for="biometric_consent">
                                    I consent to the collection and processing of my biometric data (facial features) 
                                    for attendance tracking purposes *
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-arrow-right me-2"></i>
                                Continue to Faculty Selection
                            </button>
                        </div>

                        <!-- Login Link -->
                        <div class="text-center mt-3">
                            <p class="mb-0">
                                Already have an account? 
                                <a href="{{ url_for('staff_login') }}">Login here</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Terms and Conditions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>1. Acceptance of Terms</h6>
                <p>By registering for this facial recognition attendance system, you agree to these terms and conditions.</p>
                
                <h6>2. Data Collection and Use</h6>
                <p>We collect facial biometric data solely for the purpose of automated attendance tracking. This data is encrypted and securely stored.</p>
                
                <h6>3. Privacy and Security</h6>
                <p>Your biometric data will not be shared with third parties and is protected using industry-standard security measures.</p>
                
                <h6>4. User Responsibilities</h6>
                <p>You are responsible for maintaining the confidentiality of your account credentials and for all activities under your account.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Privacy Policy</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Information We Collect</h6>
                <p>We collect personal information including name, student number, email, and facial biometric data.</p>
                
                <h6>How We Use Your Information</h6>
                <p>Your information is used exclusively for attendance tracking and academic record management.</p>
                
                <h6>Data Security</h6>
                <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
                
                <h6>Your Rights</h6>
                <p>You have the right to access, update, or delete your personal information. Contact IT support for assistance.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eye = document.getElementById(fieldId + '-eye');
    
    if (field.type === 'password') {
        field.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}

// Password matching validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    const matchDiv = document.getElementById('password-match');
    
    if (confirmPassword === '') {
        matchDiv.textContent = '';
        matchDiv.className = 'form-text';
    } else if (password === confirmPassword) {
        matchDiv.textContent = 'Passwords match ✓';
        matchDiv.className = 'form-text text-success';
    } else {
        matchDiv.textContent = 'Passwords do not match ✗';
        matchDiv.className = 'form-text text-danger';
    }
});

// Form validation
document.getElementById('registrationForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (password !== confirmPassword) {
        e.preventDefault();
        alert('Passwords do not match!');
        return false;
    }
});
</script>
{% endblock %}
