"""
Flask Application for Facial Recognition Attendance System
Main application file with routes for student registration and staff login
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, Response
from flask_bcrypt import Bcrypt
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, UserMixin, login_user, logout_user, login_required, current_user
import sqlite3
import os
from datetime import datetime, timedelta
import json
try:
    import cv2
    import numpy as np
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False
    print("⚠️ OpenCV not available - facial recognition will be simulated")

import base64
from PIL import Image
import io

# Import facial recognition routes (temporarily disabled for testing)
# from facial_recognition_routes import facial_bp, init_facial_recognition

# SQLite datetime handling - removed problematic converters to avoid parsing errors

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'
app.config['DATABASE'] = 'attendance_fixed.db'

# Initialize extensions
bcrypt = Bcrypt(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'staff_login'

# Register blueprints (temporarily disabled for testing)
# app.register_blueprint(facial_bp)

# Initialize facial recognition system (temporarily disabled for testing)
# init_facial_recognition(app)

# User class for Flask-Login
class User(UserMixin):
    def __init__(self, user_id, username, email, role, is_student=False):
        self.id = user_id
        self.username = username
        self.email = email
        self.role = role
        self.is_student = is_student

@login_manager.user_loader
def load_user(user_id):
    """Load user for Flask-Login"""
    conn = get_db_connection()
    user_id = int(user_id)

    # Check if this is a student ID (offset by 10000)
    if user_id >= 10000:
        student_id = user_id - 10000
        student = conn.execute(
            'SELECT student_id, student_number, email FROM students '
            'WHERE student_id = ? AND is_active = 1', (student_id,)
        ).fetchone()

        conn.close()

        if student:
            return User(user_id, student['student_number'], student['email'], 'student', True)
    else:
        # Try to load as staff user
        user = conn.execute(
            'SELECT u.user_id, u.username, u.email, ur.role_name FROM users u '
            'JOIN user_roles ur ON u.role_id = ur.role_id '
            'WHERE u.user_id = ? AND u.is_active = 1', (user_id,)
        ).fetchone()

        conn.close()

        if user:
            return User(user['user_id'], user['username'], user['email'], user['role_name'], False)

    return None

def get_db_connection():
    """Get database connection with proper configuration"""
    # Remove PARSE_DECLTYPES to avoid automatic timestamp conversion issues
    conn = sqlite3.connect(app.config['DATABASE'], timeout=30)
    conn.row_factory = sqlite3.Row
    # Enable foreign key constraints
    conn.execute("PRAGMA foreign_keys = ON")
    # Enable WAL mode for better concurrency
    conn.execute('PRAGMA journal_mode=WAL')
    return conn

# =====================================================
# HOME AND MAIN ROUTES
# =====================================================

@app.route('/')
def index():
    """Main landing page"""
    return render_template('index.html')

@app.route('/choose_portal')
def choose_portal():
    """Portal selection page"""
    return render_template('choose_portal.html')

# =====================================================
# STUDENT REGISTRATION FLOW
# =====================================================

@app.route('/student/register', methods=['GET', 'POST'])
def student_register():
    """Student registration - Step 1: Basic Information"""
    if request.method == 'POST':
        # Get form data
        student_number = request.form['student_number']
        first_name = request.form['first_name']
        last_name = request.form['last_name']
        email = request.form['email']
        password = request.form['password']
        confirm_password = request.form['confirm_password']
        
        # Validation
        if password != confirm_password:
            flash('Passwords do not match!', 'error')
            return render_template('student/register.html')

        # Validate student number format (8 digits only)
        if not student_number.isdigit() or len(student_number) != 8:
            flash('Student number must be exactly 8 digits!', 'error')
            return render_template('student/register.html')
        
        # Check if student already exists
        conn = get_db_connection()
        existing = conn.execute(
            'SELECT student_id FROM students WHERE student_number = ? OR email = ?',
            (student_number, email)
        ).fetchone()
        
        if existing:
            flash('Student number or email already registered!', 'error')
            conn.close()
            return render_template('student/register.html')
        
        # Hash password and store basic info in session
        password_hash = bcrypt.generate_password_hash(password).decode('utf-8')
        
        session['student_registration'] = {
            'student_number': student_number,
            'first_name': first_name,
            'last_name': last_name,
            'email': email,
            'password_hash': password_hash
        }
        
        conn.close()
        return redirect(url_for('student_select_faculty'))
    
    return render_template('student/register.html')

@app.route('/student/select_faculty')
def student_select_faculty():
    """Student registration - Step 2: Faculty Selection"""
    if 'student_registration' not in session:
        return redirect(url_for('student_register'))
    
    conn = get_db_connection()
    faculties = conn.execute(
        'SELECT faculty_id, faculty_name, faculty_code, description '
        'FROM faculties WHERE institution_id = 1 ORDER BY faculty_name'
    ).fetchall()
    conn.close()
    
    return render_template('student/select_faculty.html', faculties=faculties)

@app.route('/student/select_course/<int:faculty_id>')
def student_select_course(faculty_id):
    """Student registration - Step 3: Course Selection"""
    if 'student_registration' not in session:
        return redirect(url_for('student_register'))

    conn = get_db_connection()

    # Get faculty info
    faculty = conn.execute(
        'SELECT faculty_name FROM faculties WHERE faculty_id = ?', (faculty_id,)
    ).fetchone()

    if not faculty:
        flash('Faculty not found!', 'error')
        return redirect(url_for('student_select_faculty'))

    # Get courses in this faculty
    courses = conn.execute(
        'SELECT course_id, course_code, course_name, description FROM courses '
        'WHERE faculty_id = ? ORDER BY course_name',
        (faculty_id,)
    ).fetchall()

    conn.close()

    # Store faculty_id in session
    session['student_registration']['faculty_id'] = faculty_id
    session.modified = True  # Ensure session is saved

    return render_template('student/select_course.html',
                         faculty=faculty, courses=courses)

@app.route('/student/select_year/<int:course_id>')
def student_select_year(course_id):
    """Student registration - Step 4: Academic Year Selection"""
    if 'student_registration' not in session:
        return redirect(url_for('student_register'))

    conn = get_db_connection()

    # Get course info
    course = conn.execute(
        'SELECT course_name FROM courses WHERE course_id = ?',
        (course_id,)
    ).fetchone()

    if not course:
        flash('Course not found!', 'error')
        return redirect(url_for('student_select_faculty'))

    # Get available years for this course
    years = conn.execute(
        'SELECT course_year_id, year_level, year_name FROM course_years '
        'WHERE course_id = ? ORDER BY year_level',
        (course_id,)
    ).fetchall()

    conn.close()

    # Store course_id in session
    session['student_registration']['course_id'] = course_id
    session.modified = True

    return render_template('student/select_year.html',
                         course=course, years=years)

@app.route('/student/select_modules/<int:course_year_id>')
def student_select_modules(course_year_id):
    """Student registration - Step 5: Module Selection"""
    if 'student_registration' not in session:
        return redirect(url_for('student_register'))

    conn = get_db_connection()

    # Get year info
    year_info = conn.execute(
        'SELECT cy.year_name, c.course_name FROM course_years cy '
        'JOIN courses c ON cy.course_id = c.course_id '
        'WHERE cy.course_year_id = ?', (course_year_id,)
    ).fetchone()

    if not year_info:
        flash('Academic year not found!', 'error')
        return redirect(url_for('student_select_faculty'))

    # Get modules for this year
    modules = conn.execute(
        'SELECT module_id, module_code, module_name, description, '
        'credits, semester, is_elective FROM modules '
        'WHERE course_year_id = ? '
        'ORDER BY is_elective, semester, module_name',
        (course_year_id,)
    ).fetchall()

    conn.close()

    # Store course_year_id in session
    session['student_registration']['course_year_id'] = course_year_id
    session.modified = True

    return render_template('student/select_modules.html',
                         year_info=year_info, modules=modules)

@app.route('/student/confirm_registration', methods=['POST'])
def student_confirm_registration():
    """Student registration - Step 6: Confirmation"""
    print("🔄 Starting confirm_registration...")

    if 'student_registration' not in session:
        print("❌ No student_registration in session")
        return redirect(url_for('student_register'))

    selected_electives = request.form.getlist('modules')
    print(f"📋 Received elective modules from form: {selected_electives}")

    # Get course year ID from session
    course_year_id = session['student_registration']['course_year_id']

    # Get all core modules for this course year (auto-enroll)
    conn = get_db_connection()
    core_modules = conn.execute(
        'SELECT module_id FROM modules WHERE course_year_id = ? AND is_elective = 0',
        (course_year_id,)
    ).fetchall()
    core_module_ids = [str(module['module_id']) for module in core_modules]

    # Combine core modules (required) with selected electives
    all_selected_modules = core_module_ids + selected_electives
    print(f"📋 Core modules (auto-enrolled): {core_module_ids}")
    print(f"📋 Selected electives: {selected_electives}")
    print(f"📋 Total modules: {all_selected_modules}")

    conn.close()

    # Store all selected modules in session
    session['student_registration']['selected_modules'] = all_selected_modules
    session.modified = True  # Ensure session is saved

    print(f"✅ Stored {len(all_selected_modules)} modules in session: {all_selected_modules}")
    print(f"📋 Full session data: {session['student_registration']}")
    
    # Get summary data for confirmation
    conn = get_db_connection()

    # Debug: Check session data
    reg_data = session['student_registration']
    print("Session data keys:", reg_data.keys())
    print("Faculty ID:", reg_data.get('faculty_id'))
    print("Course ID:", reg_data.get('course_id'))
    print("Course Year ID:", reg_data.get('course_year_id'))
    print("Selected Modules:", reg_data.get('selected_modules'))

    # Check if all required data is present
    required_keys = ['faculty_id', 'course_id', 'course_year_id']
    missing_keys = [key for key in required_keys if key not in reg_data]

    if missing_keys:
        flash(f'Registration error: Missing data - {", ".join(missing_keys)}. Please start over.', 'error')
        return redirect(url_for('student_register'))

    # Get faculty, course, year info
    summary = conn.execute('''
        SELECT f.faculty_name, c.course_name, cy.year_name
        FROM faculties f
        JOIN courses c ON f.faculty_id = c.faculty_id
        JOIN course_years cy ON c.course_id = cy.course_id
        WHERE f.faculty_id = ? AND c.course_id = ? AND cy.course_year_id = ?
    ''', (
        reg_data['faculty_id'],
        reg_data['course_id'],
        reg_data['course_year_id']
    )).fetchone()
    
    # Get selected modules info
    all_modules = reg_data['selected_modules']
    module_placeholders = ','.join(['?' for _ in all_modules])
    modules = conn.execute(f'''
        SELECT module_code, module_name, credits, semester, is_elective
        FROM modules WHERE module_id IN ({module_placeholders})
        ORDER BY is_elective, semester, module_name
    ''', all_modules).fetchall()
    
    conn.close()
    
    return render_template('student/confirm_registration.html', 
                         registration=session['student_registration'],
                         summary=summary, modules=modules)

@app.route('/student/complete_registration', methods=['POST'])
def student_complete_registration():
    """Complete student registration and redirect to facial recognition setup"""
    print("🔄 Starting complete_registration...")

    if 'student_registration' not in session:
        print("❌ No student_registration in session")
        flash('Session expired. Please start registration again.', 'error')
        return redirect(url_for('student_register'))

    try:
        reg_data = session['student_registration']
        print(f"📋 Session data keys: {list(reg_data.keys())}")
        print(f"📋 Selected modules: {reg_data.get('selected_modules', 'NOT FOUND')}")

        # Validate required session data
        required_fields = ['faculty_id', 'course_id', 'course_year_id', 'selected_modules',
                          'student_number', 'email', 'password_hash', 'first_name', 'last_name']

        missing_fields = [field for field in required_fields if field not in reg_data]
        if missing_fields:
            print(f"❌ Missing fields: {missing_fields}")
            flash(f'Registration incomplete. Missing: {", ".join(missing_fields)}. Please start over.', 'error')
            return redirect(url_for('student_register'))

        if not reg_data['selected_modules']:
            flash('No modules selected. Please select at least one module.', 'error')
            return redirect(url_for('student_select_modules', course_year_id=reg_data['course_year_id']))

        conn = get_db_connection()

        # Check if student number already exists
        existing = conn.execute(
            'SELECT student_id FROM students WHERE student_number = ?',
            (reg_data['student_number'],)
        ).fetchone()

        if existing:
            flash('Student number already exists. Please use a different number.', 'error')
            conn.close()
            return redirect(url_for('student_register'))

        # Insert student record
        cursor = conn.execute('''
            INSERT INTO students (
                institution_id, faculty_id, student_number, email, password_hash,
                first_name, last_name, is_active
            ) VALUES (1, ?, ?, ?, ?, ?, ?, 1)
        ''', (
            reg_data['faculty_id'], reg_data['student_number'], reg_data['email'],
            reg_data['password_hash'], reg_data['first_name'], reg_data['last_name']
        ))

        student_id = cursor.lastrowid

        # Create enrollment record
        cursor = conn.execute('''
            INSERT INTO student_enrollments (
                student_id, course_year_id, academic_year, enrollment_date, status
            ) VALUES (?, ?, ?, ?, ?)
        ''', (
            student_id, reg_data['course_year_id'],
            '2024/2025', datetime.now().date(), 'active'
        ))

        enrollment_id = cursor.lastrowid

        # Batch insert module registrations for better performance
        module_data = [(enrollment_id, module_id, datetime.now().date())
                      for module_id in reg_data['selected_modules']]

        conn.executemany('''
            INSERT INTO module_registrations (
                enrollment_id, module_id, registration_date
            ) VALUES (?, ?, ?)
        ''', module_data)

        conn.commit()
        conn.close()
        
        # Clear registration session data
        session.pop('student_registration', None)

        # Log in the student (use student_id + 10000 to avoid conflicts with staff IDs)
        student_login_id = student_id + 10000
        print(f"🔑 Logging in student with ID: {student_login_id} (original student_id: {student_id})")
        user = User(student_login_id, reg_data['student_number'], reg_data['email'], 'student', True)
        login_user(user)
        print(f"🔑 Login successful, user.id: {user.id}, user.is_student: {user.is_student}")

        print(f"✅ Registration completed for student {student_id}")
        flash(f'Registration completed successfully! Enrolled in {len(reg_data["selected_modules"])} modules.', 'success')

        # Try facial setup, fallback to success page
        try:
            return redirect(url_for('student_facial_setup'))
        except:
            return redirect(url_for('registration_success'))

    except Exception as e:
        import traceback
        print(f"❌ Registration error: {str(e)}")
        print(f"❌ Full traceback: {traceback.format_exc()}")
        if 'conn' in locals():
            try:
                conn.rollback()
                conn.close()
            except:
                pass
        flash(f'Registration failed: {str(e)}', 'error')
        return redirect(url_for('student_register'))

# =====================================================
# STUDENT LOGIN
# =====================================================

@app.route('/student/login', methods=['GET', 'POST'])
def student_login():
    """Student login page"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        remember_me = 'remember_me' in request.form

        print(f"🔐 Student login attempt - Username: {username}")

        conn = get_db_connection()

        # Authenticate as student only
        student = conn.execute(
            'SELECT student_id, student_number, email, password_hash FROM students '
            'WHERE (student_number = ? OR email = ?) AND is_active = 1',
            (username, username)
        ).fetchone()

        if student:
            print(f"🔍 Student found - ID: {student['student_id']}, Number: {student['student_number']}")

            # Verify password
            if bcrypt.check_password_hash(student['password_hash'], password):
                # Use student_id + 10000 to avoid conflicts with staff IDs
                student_login_id = student['student_id'] + 10000
                user_obj = User(student_login_id, student['student_number'],
                              student['email'], 'student', True)
                login_user(user_obj, remember=remember_me)
                conn.close()
                flash('Welcome back!', 'success')
                return redirect(url_for('student_dashboard'))
            else:
                flash('Invalid password!', 'error')
        else:
            flash('Student not found!', 'error')

        conn.close()

    return render_template('student/login.html')

# =====================================================
# STAFF LOGIN
# =====================================================

@app.route('/staff/login', methods=['GET', 'POST'])
def staff_login():
    """Staff login page"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = get_db_connection()
        user = conn.execute(
            'SELECT u.user_id, u.username, u.email, u.password_hash, ur.role_name '
            'FROM users u JOIN user_roles ur ON u.role_id = ur.role_id '
            'WHERE u.username = ? AND u.is_active = 1', (username,)
        ).fetchone()
        conn.close()
        
        # For demo purposes, using simple hash comparison (use bcrypt in production)
        import hashlib
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        if user and user['password_hash'] == password_hash:
            user_obj = User(user['user_id'], user['username'], user['email'], user['role_name'], False)
            login_user(user_obj)
            
            # Login successful - no need to track last login for now

            flash('Login successful!', 'success')
            return redirect(url_for('staff_dashboard'))
        else:
            flash('Invalid username or password!', 'error')
    
    return render_template('staff/login.html')

# =====================================================
# PROTECTED ROUTES
# =====================================================

@app.route('/registration/success')
@login_required
def registration_success():
    """Registration success page"""
    if not current_user.is_student:
        return redirect(url_for('staff_dashboard'))

    return render_template('student/registration_success.html')

@app.route('/student/facial_setup')
@login_required
def student_facial_setup():
    """Facial recognition setup for students"""
    print(f"🔍 Facial setup - current_user.id: {current_user.id}")
    print(f"🔍 Facial setup - current_user.is_student: {current_user.is_student}")
    print(f"🔍 Facial setup - current_user.role: {current_user.role}")

    if not current_user.is_student:
        print("❌ User is not a student, redirecting to staff dashboard")
        return redirect(url_for('staff_dashboard'))

    try:
        return render_template('student/facial_setup.html')
    except Exception as e:
        print(f"❌ Error loading facial setup template: {e}")
        flash('Facial recognition setup is temporarily unavailable. You can access it later from your dashboard.', 'warning')
        return redirect(url_for('registration_success'))

@app.route('/student/facial_setup_complete')
@login_required
def student_facial_setup_complete():
    """Complete facial recognition setup"""
    if not current_user.is_student:
        return redirect(url_for('staff_dashboard'))

    flash('Facial recognition setup completed! You can now use the attendance system.', 'success')
    return redirect(url_for('student_dashboard'))

@app.route('/student/dashboard')
@login_required
def student_dashboard():
    """Student dashboard"""
    if not current_user.is_student:
        return redirect(url_for('staff_dashboard'))

    conn = get_db_connection()

    # Get student ID (current_user.id is student_id + 10000 for students)
    student_id = current_user.id - 10000

    # Get student's enrolled modules
    modules = conn.execute('''
        SELECT DISTINCT m.module_id, m.module_code, m.module_name, m.semester,
               c.course_name, cy.year_level
        FROM modules m
        JOIN course_years cy ON m.course_year_id = cy.course_year_id
        JOIN courses c ON cy.course_id = c.course_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        WHERE mr.student_id = ? AND mr.registration_status = 'active'
        ORDER BY c.course_name, cy.year_level, m.module_name
    ''', (student_id,)).fetchall()

    # Get active attendance sessions for student's modules
    from datetime import datetime

    # Get all sessions first, then filter in Python to avoid SQLite datetime conversion issues
    all_sessions = conn.execute('''
        SELECT as_.session_id, as_.session_name, as_.session_date,
               as_.attendance_window_start, as_.attendance_window_end,
               as_.is_active, m.module_code, m.module_name,
               CASE WHEN ar.student_id IS NOT NULL THEN 1 ELSE 0 END as already_attended
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        LEFT JOIN attendance_records ar ON as_.session_id = ar.session_id AND ar.student_id = ?
        WHERE mr.student_id = ? AND mr.registration_status = 'active'
        AND as_.is_active = 1 AND as_.is_completed = 0
        AND as_.attendance_window_start IS NOT NULL
        AND as_.attendance_window_end IS NOT NULL
        ORDER BY as_.session_date DESC, as_.scheduled_start_time DESC
    ''', (student_id, student_id)).fetchall()

    # Filter sessions with open attendance windows in Python
    active_sessions = []
    for session in all_sessions:
        try:
            window_start = datetime.fromisoformat(session['attendance_window_start'])
            window_end = datetime.fromisoformat(session['attendance_window_end'])
            now = datetime.now()

            if window_start <= now <= window_end:
                active_sessions.append(session)
        except (ValueError, TypeError) as e:
            print(f"Error parsing session {session['session_id']} time window: {e}")
            continue

    # Limit to 10 sessions
    active_sessions = active_sessions[:10]

    # Get recent attendance records
    recent_attendance = conn.execute('''
        SELECT ar.attendance_time, ar.attendance_status, m.module_code, m.module_name,
               as_.session_name
        FROM attendance_records ar
        JOIN attendance_sessions as_ ON ar.session_id = as_.session_id
        JOIN modules m ON as_.module_id = m.module_id
        WHERE ar.student_id = ?
        ORDER BY ar.attendance_time DESC
        LIMIT 5
    ''', (student_id,)).fetchall()

    conn.close()

    return render_template('student/dashboard.html',
                         modules=modules,
                         active_sessions=active_sessions,
                         recent_attendance=recent_attendance)

# Facial Recognition Routes
@app.route('/student/facial_capture')
@login_required
def student_facial_capture():
    """Facial recognition capture page"""
    if not current_user.is_student:
        return redirect(url_for('staff_dashboard'))

    return render_template('student/facial_capture.html')

@app.route('/student/facial_test')
@login_required
def student_facial_test():
    """Facial recognition test page"""
    if not current_user.is_student:
        return redirect(url_for('staff_dashboard'))

    return render_template('student/facial_test.html')

@app.route('/api/capture_face', methods=['POST'])
@login_required
def capture_face():
    """Capture and process facial data"""
    if not current_user.is_student:
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Get image data from request
        data = request.get_json()
        image_data = data.get('image')

        if not image_data:
            return jsonify({'error': 'No image data provided'}), 400

        # Remove data URL prefix
        image_data = image_data.split(',')[1]

        # Decode base64 image
        image_bytes = base64.b64decode(image_data)
        image = Image.open(io.BytesIO(image_bytes))

        if OPENCV_AVAILABLE:
            # Convert PIL image to OpenCV format
            image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

            # Load face detection classifier
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

            # Convert to grayscale for face detection
            gray = cv2.cvtColor(image_cv, cv2.COLOR_BGR2GRAY)

            # Detect faces
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)

            if len(faces) == 0:
                return jsonify({'error': 'No face detected in image'}), 400

            if len(faces) > 1:
                return jsonify({'error': 'Multiple faces detected. Please ensure only one face is visible'}), 400

            # Get the face region
            try:
                x, y, w, h = faces[0]
            except ValueError as e:
                return jsonify({'error': f'Face detection error: {str(e)}. Expected 4 coordinates but got {len(faces[0]) if faces[0] else 0}'}), 400
            face_region = image_cv[y:y+h, x:x+w]

            # Create a simple "encoding" by resizing face to standard size and flattening
            face_resized = cv2.resize(face_region, (100, 100))
            # Ensure grayscale for consistent encoding size
            if len(face_resized.shape) == 3:  # Color image
                face_resized = cv2.cvtColor(face_resized, cv2.COLOR_BGR2GRAY)
            face_encoding = face_resized.flatten().tolist()

        else:
            # Simulate face detection for testing
            print("🔄 Simulating face detection (OpenCV not available)")
            face_encoding = [0.1] * 10000  # Match 100x100 = 10,000 size
            faces = [(100, 100, 200, 200)]  # Dummy face location

        # Store face encoding in database
        conn = get_db_connection()

        # Get student's actual ID (subtract the offset we added for login)
        actual_student_id = current_user.id - 10000

        # Convert face encoding to JSON string for storage
        encoding_json = json.dumps(face_encoding)

        # Store or update face encoding
        conn.execute('''
            INSERT OR REPLACE INTO student_face_encodings
            (student_id, face_encoding, created_at, updated_at)
            VALUES (?, ?, ?, ?)
        ''', (actual_student_id, encoding_json, datetime.now(), datetime.now()))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'Face captured and encoded successfully!',
            'face_location': faces[0].tolist() if OPENCV_AVAILABLE else [100, 100, 200, 200],
            'opencv_available': OPENCV_AVAILABLE
        })

    except Exception as e:
        print(f"❌ Error capturing face: {e}")
        return jsonify({'error': f'Failed to process image: {str(e)}'}), 500

@app.route('/api/recognize_face', methods=['POST'])
@login_required
def recognize_face():
    """Recognize face for attendance"""
    try:
        # Get image data from request
        data = request.get_json()
        image_data = data.get('image')

        if not image_data:
            return jsonify({'error': 'No image data provided'}), 400

        # Remove data URL prefix
        image_data = image_data.split(',')[1]

        # Decode base64 image
        image_bytes = base64.b64decode(image_data)
        image = Image.open(io.BytesIO(image_bytes))

        if OPENCV_AVAILABLE:
            # Convert PIL image to OpenCV format
            image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

            # Load face detection classifier
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

            # Convert to grayscale for face detection
            gray = cv2.cvtColor(image_cv, cv2.COLOR_BGR2GRAY)

            # Detect faces
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)

            if len(faces) == 0:
                return jsonify({'error': 'No face detected'}), 400

            # Get the face region
            try:
                x, y, w, h = faces[0]
            except ValueError as e:
                return jsonify({'error': f'Face detection error: {str(e)}. Expected 4 coordinates but got {len(faces[0]) if faces[0] else 0}'}), 400
            face_region = image_cv[y:y+h, x:x+w]

            # Create encoding - ensure consistent size
            face_resized = cv2.resize(face_region, (100, 100))
            if len(face_resized.shape) == 3:  # Color image
                face_resized = cv2.cvtColor(face_resized, cv2.COLOR_BGR2GRAY)
            current_encoding = face_resized.flatten()

        else:
            # Simulate face detection for testing
            print("🔄 Simulating face recognition (OpenCV not available)")
            current_encoding = [0.1] * 10000  # Match the 100x100 = 10,000 size

        # Get all stored face encodings from database
        conn = get_db_connection()
        stored_faces = conn.execute('''
            SELECT sfe.student_id, sfe.face_encoding, s.student_number, s.first_name, s.last_name
            FROM student_face_encodings sfe
            JOIN students s ON sfe.student_id = s.student_id
            WHERE s.is_active = 1
        ''').fetchall()

        conn.close()

        if not stored_faces:
            return jsonify({'error': 'No registered faces found'}), 404

        # Compare with stored faces
        best_match = None
        best_similarity = 0

        for stored_face in stored_faces:
            stored_encoding = np.array(json.loads(stored_face['face_encoding']))

            if OPENCV_AVAILABLE:
                # Ensure both encodings are the right size (10,000 elements for 100x100)
                if len(current_encoding) != 10000 or len(stored_encoding) != 10000:
                    print(f"⚠️ Encoding size mismatch: current={len(current_encoding)}, stored={len(stored_encoding)}")
                    continue

                # Calculate similarity using normalized correlation
                similarity = float(cv2.matchTemplate(
                    current_encoding.reshape(100, 100).astype(np.float32),
                    stored_encoding.reshape(100, 100).astype(np.float32),
                    cv2.TM_CCOEFF_NORMED
                )[0][0])
            else:
                # Simulate recognition for testing - always match the first stored face
                similarity = 0.9 if stored_face == stored_faces[0] else 0.1

            if similarity > best_similarity and similarity > 0.7:  # Threshold for recognition
                best_similarity = similarity
                best_match = stored_face

        if best_match:
            # Face recognized!
            return jsonify({
                'success': True,
                'student_id': best_match['student_id'],
                'student_number': best_match['student_number'],
                'name': f"{best_match['first_name']} {best_match['last_name']}",
                'confidence': float(best_similarity),
                'message': 'Face recognized successfully!',
                'opencv_available': OPENCV_AVAILABLE
            })

        return jsonify({'error': 'Face not recognized'}), 404

    except Exception as e:
        print(f"❌ Error recognizing face: {e}")
        return jsonify({'error': f'Failed to recognize face: {str(e)}'}), 500

@app.route('/api/opencv_status')
def opencv_status():
    """Get OpenCV availability status"""
    return jsonify({
        'opencv_available': OPENCV_AVAILABLE,
        'message': 'OpenCV is available' if OPENCV_AVAILABLE else 'OpenCV not available - using simulation mode'
    })

# =====================================================
# ADMIN HELPER FUNCTIONS
# =====================================================

def get_foreign_key_display(conn, table_name, column_name, value):
    """Get display value for foreign key relationships"""
    if not value:
        return None

    try:
        # Define foreign key mappings
        fk_mappings = {
            'faculty_id': ('faculties', 'faculty_name'),
            'course_id': ('courses', 'course_name'),
            'course_year_id': ('course_years', 'year_name'),
            'module_id': ('modules', 'module_name'),
            'student_id': ('students', 'student_number'),
            'lecturer_id': ('users', 'username'),
            'user_id': ('users', 'username'),
            'role_id': ('user_roles', 'role_name'),
            'institution_id': ('institutions', 'institution_name'),
            'enrollment_id': ('student_enrollments', 'enrollment_id'),
            'session_id': ('attendance_sessions', 'session_name'),
        }

        if column_name in fk_mappings:
            ref_table, ref_column = fk_mappings[column_name]
            result = conn.execute(f'SELECT {ref_column} FROM {ref_table} WHERE {column_name} = ?', (value,)).fetchone()
            if result:
                return result[0]

        return value
    except Exception:
        return value

# =====================================================
# ADMIN ROUTES
# =====================================================

@app.route('/admin')
@login_required
def admin_dashboard():
    """Admin dashboard - overview of system"""
    if current_user.role != 'super_admin' and current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('index'))

    conn = get_db_connection()

    # Get system statistics
    stats = {}
    stats['total_students'] = conn.execute('SELECT COUNT(*) as count FROM students').fetchone()['count']
    stats['total_staff'] = conn.execute('SELECT COUNT(*) as count FROM users').fetchone()['count']
    stats['total_modules'] = conn.execute('SELECT COUNT(*) as count FROM modules').fetchone()['count']
    stats['total_sessions'] = conn.execute('SELECT COUNT(*) as count FROM attendance_sessions').fetchone()['count']
    stats['total_attendance'] = conn.execute('SELECT COUNT(*) as count FROM student_attendance').fetchone()['count']

    # Recent activity
    recent_students = conn.execute('''
        SELECT student_id, student_number, first_name, last_name, created_at
        FROM students ORDER BY created_at DESC LIMIT 5
    ''').fetchall()

    recent_attendance = conn.execute('''
        SELECT sa.attendance_time, s.student_number, s.first_name, s.last_name,
               m.module_code, ats.session_name
        FROM student_attendance sa
        JOIN students s ON sa.student_id = s.student_id
        JOIN attendance_sessions ats ON sa.session_id = ats.session_id
        JOIN modules m ON ats.module_id = m.module_id
        ORDER BY sa.attendance_time DESC LIMIT 10
    ''').fetchall()

    conn.close()

    return render_template('admin/dashboard.html',
                         stats=stats,
                         recent_students=recent_students,
                         recent_attendance=recent_attendance)

@app.route('/admin/tables')
@login_required
def admin_tables():
    """Admin tables overview"""
    if current_user.role != 'super_admin' and current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('index'))

    # List of all tables with descriptions (matching current database schema)
    tables = [
        {'name': 'institutions', 'description': 'Educational institutions', 'icon': 'university'},
        {'name': 'faculties', 'description': 'Academic faculties', 'icon': 'building'},
        {'name': 'user_roles', 'description': 'User roles and permissions', 'icon': 'users-cog'},
        {'name': 'users', 'description': 'Staff and lecturers', 'icon': 'user-tie'},
        {'name': 'students', 'description': 'Student records', 'icon': 'user-graduate'},
        {'name': 'courses', 'description': 'Academic courses', 'icon': 'book'},
        {'name': 'course_years', 'description': 'Course year levels', 'icon': 'layer-group'},
        {'name': 'modules', 'description': 'Course modules', 'icon': 'books'},
        {'name': 'student_enrollments', 'description': 'Student course enrollments', 'icon': 'user-plus'},
        {'name': 'module_registrations', 'description': 'Student module registrations', 'icon': 'clipboard-list'},
        {'name': 'student_face_encodings', 'description': 'Facial recognition data', 'icon': 'face-smile'},
        {'name': 'module_lecturers', 'description': 'Module lecturer assignments', 'icon': 'chalkboard-teacher'},
        {'name': 'attendance_sessions', 'description': 'Attendance sessions', 'icon': 'calendar-check'},
        {'name': 'student_attendance', 'description': 'Live attendance records', 'icon': 'check-circle'},
    ]

    return render_template('admin/tables.html', tables=tables)

@app.route('/admin/table/<table_name>')
@login_required
def admin_view_table(table_name):
    """View and manage specific table data"""
    if current_user.role != 'super_admin' and current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('index'))

    # Security: Only allow predefined tables (matching current database schema)
    allowed_tables = [
        'institutions', 'faculties', 'departments', 'user_roles', 'users', 'students',
        'courses', 'course_years', 'modules', 'student_enrollments', 'module_registrations',
        'student_face_encodings', 'module_lecturers', 'attendance_sessions',
        'student_attendance', 'attendance_marks', 'attendance_records'
    ]

    if table_name not in allowed_tables:
        flash('Invalid table name.', 'error')
        return redirect(url_for('admin_tables'))

    conn = get_db_connection()

    # Get sorting parameters
    sort_by = request.args.get('sort', '')
    sort_order = request.args.get('order', 'ASC')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 50))
    search = request.args.get('search', '')

    # Get table schema
    schema = conn.execute(f"PRAGMA table_info({table_name})").fetchall()
    columns = [col['name'] for col in schema]

    # Build query with search and sorting
    base_query = f"SELECT * FROM {table_name}"
    count_query = f"SELECT COUNT(*) as total FROM {table_name}"

    params = []
    where_clause = ""

    if search:
        # Create search conditions for text columns
        search_conditions = []
        for col in schema:
            if col['type'] in ['TEXT', 'VARCHAR(255)', 'VARCHAR(100)', 'VARCHAR(50)', 'VARCHAR(20)']:
                search_conditions.append(f"{col['name']} LIKE ?")
                params.append(f"%{search}%")

        if search_conditions:
            where_clause = f" WHERE {' OR '.join(search_conditions)}"

    # Add WHERE clause to queries
    if where_clause:
        base_query += where_clause
        count_query += where_clause

    # Add sorting
    if sort_by and sort_by in columns:
        base_query += f" ORDER BY {sort_by} {sort_order}"

    # Add pagination
    offset = (page - 1) * per_page
    base_query += f" LIMIT {per_page} OFFSET {offset}"

    # Execute queries
    total_records = conn.execute(count_query, params).fetchone()['total']
    records = conn.execute(base_query, params).fetchall()

    # Convert records to dictionaries for better template handling
    records_list = []
    for record in records:
        record_dict = {}
        for i, column in enumerate(columns):
            value = record[i] if i < len(record) else record[column]
            # Handle foreign key display
            if column.endswith('_id') and column != schema[0]['name']:  # Not primary key
                record_dict[column] = value
                # Add display name for foreign keys
                record_dict[f"{column}_display"] = get_foreign_key_display(conn, table_name, column, value)
            else:
                record_dict[column] = value
        records_list.append(record_dict)

    conn.close()

    # Calculate pagination info
    total_pages = (total_records + per_page - 1) // per_page

    return render_template('admin/table_view.html',
                         table_name=table_name,
                         columns=columns,
                         records=records_list,
                         schema=schema,
                         current_page=page,
                         total_pages=total_pages,
                         total_records=total_records,
                         per_page=per_page,
                         sort_by=sort_by,
                         sort_order=sort_order,
                         search=search)

@app.route('/admin/table/<table_name>/add', methods=['GET', 'POST'])
@login_required
def admin_add_record(table_name):
    """Add new record to table"""
    if current_user.role != 'super_admin' and current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('index'))

    # Security check (tables that can be modified)
    allowed_tables = [
        'institutions', 'faculties', 'departments', 'user_roles', 'users', 'students',
        'courses', 'course_years', 'modules', 'student_enrollments', 'module_registrations',
        'module_lecturers', 'attendance_sessions'
    ]

    if table_name not in allowed_tables:
        flash('Cannot add records to this table.', 'error')
        return redirect(url_for('admin_tables'))

    conn = get_db_connection()

    if request.method == 'POST':
        try:
            # Get table schema
            schema = conn.execute(f"PRAGMA table_info({table_name})").fetchall()

            # Build INSERT query
            columns = []
            values = []
            placeholders = []

            for col in schema:
                col_name = col['name']
                # Skip auto-increment primary keys and timestamp fields
                if col['pk'] and 'AUTOINCREMENT' in str(col):
                    continue
                if col_name in ['created_at', 'updated_at']:
                    continue

                if col_name in request.form:
                    value = request.form[col_name]
                    if value:  # Only include non-empty values
                        columns.append(col_name)
                        # Hash password if it's a password field
                        if col_name == 'password_hash':
                            import hashlib
                            values.append(hashlib.sha256(value.encode()).hexdigest())
                        else:
                            values.append(value)
                        placeholders.append('?')

            if columns:
                query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
                conn.execute(query, values)
                conn.commit()
                flash(f'Record added successfully to {table_name}!', 'success')
            else:
                flash('No valid data provided.', 'error')

        except Exception as e:
            conn.rollback()
            flash(f'Error adding record: {str(e)}', 'error')
        finally:
            conn.close()

        return redirect(url_for('admin_view_table', table_name=table_name))

    # GET request - show form
    schema = conn.execute(f"PRAGMA table_info({table_name})").fetchall()

    # Get foreign key relationships
    foreign_keys = conn.execute(f"PRAGMA foreign_key_list({table_name})").fetchall()
    fk_data = {}

    for fk in foreign_keys:
        ref_table = fk['table']
        ref_column = fk['to']
        local_column = fk['from']

        # Get options for foreign key dropdown
        try:
            if ref_table == 'institutions':
                options = conn.execute('SELECT institution_id, institution_name FROM institutions ORDER BY institution_name').fetchall()
            elif ref_table == 'faculties':
                options = conn.execute('SELECT faculty_id, faculty_name FROM faculties ORDER BY faculty_name').fetchall()
            elif ref_table == 'departments':
                options = conn.execute('SELECT department_id, department_name FROM departments ORDER BY department_name').fetchall()
            elif ref_table == 'user_roles':
                options = conn.execute('SELECT role_id, role_name FROM user_roles ORDER BY role_name').fetchall()
            elif ref_table == 'courses':
                options = conn.execute('SELECT course_id, course_name FROM courses ORDER BY course_name').fetchall()
            elif ref_table == 'course_years':
                options = conn.execute('SELECT course_year_id, COALESCE(year_name, "Year " || year_level) as display_name FROM course_years ORDER BY year_level').fetchall()
            elif ref_table == 'modules':
                options = conn.execute('SELECT module_id, module_name FROM modules ORDER BY module_name').fetchall()
            elif ref_table == 'students':
                options = conn.execute('SELECT student_id, (first_name || " " || last_name) as name FROM students ORDER BY first_name').fetchall()
            elif ref_table == 'users':
                options = conn.execute('SELECT user_id, (first_name || " " || last_name) as name FROM users ORDER BY first_name').fetchall()
            else:
                # Fallback: try to get two columns, or duplicate the first column
                try:
                    options = conn.execute(f'SELECT {ref_column}, {ref_column} FROM {ref_table} LIMIT 1').fetchall()
                    if options:  # If successful, get all records
                        options = conn.execute(f'SELECT {ref_column}, {ref_column} FROM {ref_table}').fetchall()
                except:
                    # If that fails, just get the primary key column
                    options = conn.execute(f'SELECT {ref_column} FROM {ref_table}').fetchall()
                    # Convert single-column results to tuple format
                    options = [(row[0], str(row[0])) for row in options]

            # Ensure all options are in the correct format (id, display_name)
            formatted_options = []
            for option in options:
                try:
                    # Handle SQLite Row objects
                    if hasattr(option, 'keys'):  # SQLite Row object
                        keys = list(option.keys())
                        if len(keys) >= 2:
                            formatted_options.append((option[keys[0]], option[keys[1]]))
                        else:
                            formatted_options.append((option[keys[0]], str(option[keys[0]])))
                    elif isinstance(option, (list, tuple)) and len(option) >= 2:
                        formatted_options.append((option[0], option[1]))
                    elif isinstance(option, (list, tuple)) and len(option) == 1:
                        formatted_options.append((option[0], str(option[0])))
                    else:
                        formatted_options.append((option, str(option)))
                except Exception as e:
                    print(f"Error formatting option {option}: {e}")
                    formatted_options.append((str(option), str(option)))

            fk_data[local_column] = {
                'table': ref_table,
                'options': formatted_options
            }
        except Exception as e:
            # Log the error but continue
            print(f"Error loading foreign key options for {ref_table}: {e}")
            fk_data[local_column] = {
                'table': ref_table,
                'options': []
            }

    conn.close()

    return render_template('admin/add_record.html',
                         table_name=table_name,
                         schema=schema,
                         foreign_keys=fk_data)

@app.route('/admin/table/<table_name>/edit/<int:record_id>', methods=['GET', 'POST'])
@login_required
def admin_edit_record(table_name, record_id):
    """Edit existing record"""
    if current_user.role != 'super_admin' and current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('index'))

    # Security check
    allowed_tables = [
        'institutions', 'faculties', 'departments', 'user_roles', 'users', 'students',
        'courses', 'course_years', 'modules', 'student_enrollments', 'module_registrations',
        'module_lecturers', 'attendance_sessions'
    ]

    if table_name not in allowed_tables:
        flash('Cannot edit records in this table.', 'error')
        return redirect(url_for('admin_tables'))

    conn = get_db_connection()

    # Get table schema to find primary key
    schema = conn.execute(f"PRAGMA table_info({table_name})").fetchall()
    primary_key = None
    for col in schema:
        if col['pk']:
            primary_key = col['name']
            break

    if not primary_key:
        flash('Cannot edit records without primary key.', 'error')
        conn.close()
        return redirect(url_for('admin_view_table', table_name=table_name))

    if request.method == 'POST':
        try:
            # Build UPDATE query
            set_clauses = []
            values = []

            for col in schema:
                col_name = col['name']
                # Skip primary key and auto-timestamp fields
                if col['pk'] or col_name in ['created_at']:
                    continue

                if col_name in request.form:
                    value = request.form[col_name]
                    if col_name == 'password_hash' and value:
                        # Hash password if provided
                        import hashlib
                        set_clauses.append(f"{col_name} = ?")
                        values.append(hashlib.sha256(value.encode()).hexdigest())
                    elif col_name != 'password_hash' or value:
                        # For non-password fields, or password field with value
                        set_clauses.append(f"{col_name} = ?")
                        values.append(value if value else None)

            # Add updated_at if it exists
            if any(col['name'] == 'updated_at' for col in schema):
                set_clauses.append("updated_at = CURRENT_TIMESTAMP")

            if set_clauses:
                query = f"UPDATE {table_name} SET {', '.join(set_clauses)} WHERE {primary_key} = ?"
                values.append(record_id)
                conn.execute(query, values)
                conn.commit()
                flash(f'Record updated successfully in {table_name}!', 'success')
            else:
                flash('No valid data provided.', 'error')

        except Exception as e:
            conn.rollback()
            flash(f'Error updating record: {str(e)}', 'error')
        finally:
            conn.close()

        return redirect(url_for('admin_view_table', table_name=table_name))

    # GET request - show form with current data
    current_record = conn.execute(f"SELECT * FROM {table_name} WHERE {primary_key} = ?", (record_id,)).fetchone()

    if not current_record:
        flash('Record not found.', 'error')
        conn.close()
        return redirect(url_for('admin_view_table', table_name=table_name))

    # Get foreign key relationships (same as add form)
    foreign_keys = conn.execute(f"PRAGMA foreign_key_list({table_name})").fetchall()
    fk_data = {}

    for fk in foreign_keys:
        ref_table = fk['table']
        ref_column = fk['to']
        local_column = fk['from']

        try:
            if ref_table == 'institutions':
                options = conn.execute('SELECT institution_id, institution_name FROM institutions ORDER BY institution_name').fetchall()
            elif ref_table == 'faculties':
                options = conn.execute('SELECT faculty_id, faculty_name FROM faculties ORDER BY faculty_name').fetchall()
            elif ref_table == 'departments':
                options = conn.execute('SELECT department_id, department_name FROM departments ORDER BY department_name').fetchall()
            elif ref_table == 'user_roles':
                options = conn.execute('SELECT role_id, role_name FROM user_roles ORDER BY role_name').fetchall()
            elif ref_table == 'courses':
                options = conn.execute('SELECT course_id, course_name FROM courses ORDER BY course_name').fetchall()
            elif ref_table == 'course_years':
                options = conn.execute('SELECT course_year_id, COALESCE(year_name, "Year " || year_level) as display_name FROM course_years ORDER BY year_level').fetchall()
            elif ref_table == 'modules':
                options = conn.execute('SELECT module_id, module_name FROM modules ORDER BY module_name').fetchall()
            elif ref_table == 'students':
                options = conn.execute('SELECT student_id, (first_name || " " || last_name) as name FROM students ORDER BY first_name').fetchall()
            elif ref_table == 'users':
                options = conn.execute('SELECT user_id, (first_name || " " || last_name) as name FROM users ORDER BY first_name').fetchall()
            else:
                # Fallback: try to get two columns, or duplicate the first column
                try:
                    options = conn.execute(f'SELECT {ref_column}, {ref_column} FROM {ref_table} LIMIT 1').fetchall()
                    if options:  # If successful, get all records
                        options = conn.execute(f'SELECT {ref_column}, {ref_column} FROM {ref_table}').fetchall()
                except:
                    # If that fails, just get the primary key column
                    options = conn.execute(f'SELECT {ref_column} FROM {ref_table}').fetchall()
                    # Convert single-column results to tuple format
                    options = [(row[0], str(row[0])) for row in options]

            # Ensure all options are in the correct format (id, display_name)
            formatted_options = []
            for option in options:
                try:
                    # Handle SQLite Row objects
                    if hasattr(option, 'keys'):  # SQLite Row object
                        keys = list(option.keys())
                        if len(keys) >= 2:
                            formatted_options.append((option[keys[0]], option[keys[1]]))
                        else:
                            formatted_options.append((option[keys[0]], str(option[keys[0]])))
                    elif isinstance(option, (list, tuple)) and len(option) >= 2:
                        formatted_options.append((option[0], option[1]))
                    elif isinstance(option, (list, tuple)) and len(option) == 1:
                        formatted_options.append((option[0], str(option[0])))
                    else:
                        formatted_options.append((option, str(option)))
                except Exception as e:
                    print(f"Error formatting option {option}: {e}")
                    formatted_options.append((str(option), str(option)))

            fk_data[local_column] = {
                'table': ref_table,
                'options': formatted_options
            }
        except Exception as e:
            # Log the error but continue
            print(f"Error loading foreign key options for {ref_table}: {e}")
            fk_data[local_column] = {
                'table': ref_table,
                'options': []
            }

    conn.close()

    return render_template('admin/edit_record.html',
                         table_name=table_name,
                         schema=schema,
                         record=current_record,
                         record_id=record_id,
                         foreign_keys=fk_data)

@app.route('/admin/table/<table_name>/delete/<int:record_id>', methods=['POST'])
@login_required
def admin_delete_record(table_name, record_id):
    """Delete record from table"""
    if current_user.role != 'super_admin':
        flash('Access denied. Super admin privileges required for deletion.', 'error')
        return redirect(url_for('index'))

    # Security check
    allowed_tables = [
        'institutions', 'faculties', 'departments', 'user_roles', 'users', 'students',
        'courses', 'course_years', 'modules', 'student_enrollments', 'module_registrations',
        'module_lecturers', 'attendance_sessions'
    ]

    if table_name not in allowed_tables:
        flash('Cannot delete records from this table.', 'error')
        return redirect(url_for('admin_tables'))

    conn = get_db_connection()

    try:
        # Get table schema to find primary key
        schema = conn.execute(f"PRAGMA table_info({table_name})").fetchall()
        primary_key = None
        for col in schema:
            if col['pk']:
                primary_key = col['name']
                break

        if not primary_key:
            flash('Cannot delete records without primary key.', 'error')
        else:
            conn.execute(f"DELETE FROM {table_name} WHERE {primary_key} = ?", (record_id,))
            conn.commit()
            flash(f'Record deleted successfully from {table_name}!', 'success')

    except Exception as e:
        conn.rollback()
        flash(f'Error deleting record: {str(e)}', 'error')
    finally:
        conn.close()

    return redirect(url_for('admin_view_table', table_name=table_name))

@app.route('/admin/table/<table_name>/export')
@login_required
def admin_export_table(table_name):
    """Export table data as CSV"""
    if current_user.role != 'super_admin' and current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('index'))

    # Security check
    allowed_tables = [
        'institutions', 'faculties', 'departments', 'user_roles', 'users', 'students',
        'courses', 'course_years', 'modules', 'student_enrollments', 'module_registrations',
        'module_lecturers', 'attendance_sessions', 'student_attendance', 'attendance_marks'
    ]

    if table_name not in allowed_tables:
        flash('Cannot export this table.', 'error')
        return redirect(url_for('admin_tables'))

    conn = get_db_connection()

    try:
        # Get all records
        records = conn.execute(f"SELECT * FROM {table_name}").fetchall()

        if not records:
            flash('No data to export.', 'warning')
            return redirect(url_for('admin_view_table', table_name=table_name))

        # Get column names
        columns = [description[0] for description in conn.execute(f"SELECT * FROM {table_name} LIMIT 1").description]

        # Create CSV content
        import io
        import csv

        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow(columns)

        # Write data
        for record in records:
            row = []
            for value in record:
                if value is None:
                    row.append('')
                elif isinstance(value, (bytes, bytearray)):
                    row.append('[Binary Data]')
                else:
                    row.append(str(value))
            writer.writerow(row)

        # Prepare response
        from flask import make_response
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = f'attachment; filename={table_name}_export.csv'

        return response

    except Exception as e:
        flash(f'Error exporting data: {str(e)}', 'error')
        return redirect(url_for('admin_view_table', table_name=table_name))
    finally:
        conn.close()

@app.route('/api/mark_attendance_simple', methods=['POST'])
@login_required
def mark_attendance_simple():
    """Simple attendance marking without facial recognition"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')

        if not session_id:
            return jsonify({'error': 'Session ID required'}), 400

        # Get student ID
        if hasattr(current_user, 'is_student') and current_user.is_student:
            student_id = current_user.id - 10000
        else:
            return jsonify({'error': 'Only students can mark attendance'}), 403

        conn = get_db_connection()

        # Check if already marked
        existing = conn.execute('''
            SELECT attendance_id FROM student_attendance
            WHERE session_id = ? AND student_id = ?
        ''', (session_id, student_id)).fetchone()

        if existing:
            conn.close()
            return jsonify({'error': 'Attendance already marked'}), 409

        # Mark attendance
        conn.execute('''
            INSERT INTO student_attendance (
                session_id, student_id, confidence_score, attendance_method, attendance_time
            ) VALUES (?, ?, ?, ?, ?)
        ''', (session_id, student_id, 0.95, 'simple_mark', datetime.now().isoformat()))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'Attendance marked successfully!'
        })

    except Exception as e:
        return jsonify({'error': f'Failed to mark attendance: {str(e)}'}), 500

@app.route('/api/mark_attendance', methods=['POST'])
@login_required
def mark_attendance():
    """Mark attendance for an active session using facial recognition"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        image_data = data.get('image')

        if not session_id or not image_data:
            return jsonify({'error': 'Session ID and image data required'}), 400

        # Check if session exists and get session data
        conn = get_db_connection()
        session_data = conn.execute('''
            SELECT as_.*, m.module_code, m.module_name
            FROM attendance_sessions as_
            JOIN modules m ON as_.module_id = m.module_id
            WHERE as_.session_id = ? AND as_.is_active = 1
        ''', (session_id,)).fetchone()

        if not session_data:
            conn.close()
            return jsonify({'error': 'Session not found or not active'}), 404

        # Check if attendance window is open
        from datetime import datetime
        try:
            now = datetime.now()
            window_start = datetime.fromisoformat(session_data['attendance_window_start'])
            window_end = datetime.fromisoformat(session_data['attendance_window_end'])

            if now < window_start or now > window_end:
                conn.close()
                return jsonify({'error': 'Attendance window is not currently open for this session'}), 403
        except Exception:
            conn.close()
            return jsonify({'error': 'Error checking attendance window'}), 500

        # Get student ID (for students, current_user.id is student_id + 10000)
        if hasattr(current_user, 'is_student') and current_user.is_student:
            student_id = current_user.id - 10000
        else:
            conn.close()
            return jsonify({'error': 'Only students can mark attendance'}), 403

        # Check if student is enrolled in this module
        enrollment_check = conn.execute('''
            SELECT mr.registration_id, mr.student_id, mr.module_id, mr.registration_status
            FROM module_registrations mr
            WHERE mr.student_id = ? AND mr.module_id = ? AND mr.registration_status = 'active'
        ''', (student_id, session_data['module_id'])).fetchone()

        if not enrollment_check:
            conn.close()
            return jsonify({'error': 'You are not enrolled in this module.'}), 403

        # Check if already marked attendance
        existing_attendance = conn.execute('''
            SELECT attendance_id FROM student_attendance
            WHERE session_id = ? AND student_id = ?
        ''', (session_id, student_id)).fetchone()

        if existing_attendance:
            conn.close()
            return jsonify({'error': 'Attendance already marked for this session'}), 409

        # Perform facial recognition (reuse existing logic)
        # Remove data URL prefix
        image_data = image_data.split(',')[1]

        # Decode base64 image
        image_bytes = base64.b64decode(image_data)
        image = Image.open(io.BytesIO(image_bytes))

        confidence = 0.9  # Default confidence

        if OPENCV_AVAILABLE:
            try:
                # Convert PIL image to OpenCV format
                image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

                # Load face detection classifier
                face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

                # Check if cascade loaded properly
                if face_cascade.empty():
                    conn.close()
                    return jsonify({'error': 'Face detection classifier failed to load'}), 500

                # Convert to grayscale for face detection
                gray = cv2.cvtColor(image_cv, cv2.COLOR_BGR2GRAY)

                # Detect faces
                faces = face_cascade.detectMultiScale(gray, 1.1, 4)
                print(f"🔍 Face detection: Found {len(faces)} faces")

                if len(faces) == 0:
                    conn.close()
                    return jsonify({'error': 'No face detected in the image. Please ensure your face is clearly visible.'}), 400
                else:
                    # Get the face region
                    try:
                        face_data = faces[0]
                        if len(face_data) != 4:
                            conn.close()
                            return jsonify({'error': f'Invalid face detection data. Expected 4 coordinates, got {len(face_data)}: {face_data}'}), 400

                        x, y, w, h = face_data
                    except (ValueError, IndexError) as e:
                        conn.close()
                        return jsonify({'error': f'Face detection unpacking error: {str(e)}. Face data: {faces[0] if len(faces) > 0 else "None"}'}), 400

                    face_region = image_cv[y:y+h, x:x+w]

                    # Create encoding - ensure consistent size (grayscale)
                    face_resized = cv2.resize(face_region, (100, 100))
                    if len(face_resized.shape) == 3:  # Color image
                        face_resized = cv2.cvtColor(face_resized, cv2.COLOR_BGR2GRAY)
                    current_encoding = face_resized.flatten()

                    print(f"🔍 Current encoding: {len(current_encoding)} elements, min: {current_encoding.min():.2f}, max: {current_encoding.max():.2f}, mean: {current_encoding.mean():.2f}")

                    # Get all stored face encodings for this student (use most recent ones)
                    stored_faces = conn.execute('''
                        SELECT face_encoding FROM student_face_encodings
                        WHERE student_id = ?
                        ORDER BY created_at DESC
                        LIMIT 3
                    ''', (student_id,)).fetchall()

                    if stored_faces:
                        print(f"🔍 Comparing against {len(stored_faces)} stored face encodings")

                        best_confidence = 0.0
                        confidences = []

                        for i, stored_face in enumerate(stored_faces):
                            try:
                                stored_encoding = np.array(json.loads(stored_face['face_encoding']))

                                # Validate encoding dimensions
                                if len(current_encoding) != len(stored_encoding):
                                    print(f"   ⚠️  Encoding {i+1}: Dimension mismatch - Current: {len(current_encoding)}, Stored: {len(stored_encoding)}")
                                    continue

                                if len(current_encoding) != 10000:  # 100x100 = 10,000
                                    print(f"   ⚠️  Encoding {i+1}: Invalid size: {len(current_encoding)}")
                                    continue

                                # Calculate similarity
                                similarity = cv2.matchTemplate(
                                    current_encoding.reshape(100, 100).astype(np.float32),
                                    stored_encoding.reshape(100, 100).astype(np.float32),
                                    cv2.TM_CCOEFF_NORMED
                                )[0][0]

                                confidence = float(similarity)
                                confidences.append(confidence)

                                print(f"   📊 Encoding {i+1}: confidence = {confidence:.4f}")

                                if confidence > best_confidence:
                                    best_confidence = confidence

                            except Exception as e:
                                print(f"   ❌ Error comparing encoding {i+1}: {e}")
                                continue

                        if not confidences:
                            conn.close()
                            return jsonify({'error': 'No valid face encodings found for comparison. Please re-register your face.'}), 400

                        # Use the best confidence score
                        confidence = best_confidence
                        avg_confidence = sum(confidences) / len(confidences)

                        print(f"🔍 Best confidence: {confidence:.4f}, Average: {avg_confidence:.4f}")

                        if confidence < 0.4:  # Even lower threshold for testing
                            conn.close()
                            return jsonify({
                                'error': f'Face recognition failed - best confidence too low: {confidence:.4f}. Minimum required: 0.4. All scores: {[f"{c:.3f}" for c in confidences]}'
                            }), 400
                    else:
                        # No stored face encoding found
                        conn.close()
                        return jsonify({'error': 'No face encoding found for this student. Please complete facial setup first.'}), 400

            except Exception as e:
                conn.close()
                return jsonify({'error': f'OpenCV processing error: {str(e)}'}), 500


        else:
            # Fallback: simulate face detection for testing when OpenCV is not available
            confidence = 0.85  # Simulated confidence

        # Mark attendance
        conn.execute('''
            INSERT INTO student_attendance (
                session_id, student_id, confidence_score, attendance_method
            ) VALUES (?, ?, ?, ?)
        ''', (session_id, student_id, confidence, 'facial_recognition'))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'Attendance marked successfully!',
            'session_name': session_data['session_name'],
            'module': f"{session_data['module_code']} - {session_data['module_name']}",
            'confidence': confidence
        })

    except Exception as e:
        print(f"❌ Error marking attendance: {e}")
        return jsonify({'error': f'Failed to mark attendance: {str(e)}'}), 500

@app.route('/staff/dashboard')
@login_required
def staff_dashboard():
    """Enhanced staff dashboard with real-time data"""
    if current_user.is_student:
        return redirect(url_for('student_facial_setup'))

    from datetime import datetime, date, timedelta

    conn = get_db_connection()

    # Get lecturer's modules with enrollment counts
    modules = conn.execute('''
        SELECT m.module_id, m.module_code, m.module_name, m.semester,
               c.course_name, cy.year_level,
               COUNT(DISTINCT mr.student_id) as enrolled_count
        FROM modules m
        JOIN course_years cy ON m.course_year_id = cy.course_year_id
        JOIN courses c ON cy.course_id = c.course_id
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        LEFT JOIN module_registrations mr ON m.module_id = mr.module_id AND mr.registration_status = 'active'
        WHERE ml.lecturer_id = ?
        GROUP BY m.module_id, m.module_code, m.module_name, m.semester, c.course_name, cy.year_level
        ORDER BY c.course_name, cy.year_level, m.module_name
    ''', (current_user.id,)).fetchall()

    # Get active attendance sessions with real-time status
    active_sessions = conn.execute('''
        SELECT as_.session_id, as_.session_name, as_.session_date,
               as_.attendance_window_start, as_.attendance_window_end,
               as_.is_active, as_.is_completed, m.module_code, m.module_name,
               COUNT(DISTINCT sa.student_id) as attendance_count
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        LEFT JOIN student_attendance sa ON as_.session_id = sa.session_id
        WHERE as_.lecturer_id = ? AND as_.is_completed = 0
        GROUP BY as_.session_id, as_.session_name, as_.session_date, as_.attendance_window_start,
                 as_.attendance_window_end, as_.is_active, as_.is_completed, m.module_code, m.module_name
        ORDER BY as_.session_date DESC, as_.scheduled_start_time DESC
        LIMIT 5
    ''', (current_user.id,)).fetchall()

    # Get today's schedule with session status
    today = date.today()
    todays_schedule = conn.execute('''
        SELECT as_.session_id, as_.session_name, as_.session_date, as_.scheduled_start_time,
               as_.scheduled_end_time, as_.is_active, as_.is_completed, as_.location,
               m.module_code, m.module_name, c.course_name, cy.year_level,
               COUNT(DISTINCT mr.student_id) as enrolled_count,
               COUNT(DISTINCT sa.student_id) as attendance_count
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN course_years cy ON m.course_year_id = cy.course_year_id
        JOIN courses c ON cy.course_id = c.course_id
        LEFT JOIN module_registrations mr ON m.module_id = mr.module_id AND mr.registration_status = 'active'
        LEFT JOIN student_attendance sa ON as_.session_id = sa.session_id
        WHERE as_.lecturer_id = ? AND as_.session_date = ?
        GROUP BY as_.session_id, as_.session_name, as_.session_date, as_.scheduled_start_time,
                 as_.scheduled_end_time, as_.is_active, as_.is_completed, as_.location,
                 m.module_code, m.module_name, c.course_name, cy.year_level
        ORDER BY as_.scheduled_start_time
    ''', (current_user.id, today.isoformat())).fetchall()

    conn.close()

    return render_template('staff/dashboard.html',
                         modules=modules,
                         active_sessions=active_sessions,
                         todays_schedule=todays_schedule)

@app.route('/api/staff/dashboard_stats')
@login_required
def staff_dashboard_stats():
    """API endpoint for real-time dashboard statistics"""
    if current_user.is_student:
        return jsonify({'error': 'Access denied'}), 403

    from datetime import datetime, date

    conn = get_db_connection()

    # Get total students in lecturer's modules
    total_students = conn.execute('''
        SELECT COUNT(DISTINCT mr.student_id) as count
        FROM module_registrations mr
        JOIN modules m ON mr.module_id = m.module_id
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        WHERE ml.lecturer_id = ? AND mr.registration_status = 'active'
    ''', (current_user.id,)).fetchone()['count']

    # Get today's attendance count
    today = date.today()
    todays_attendance = conn.execute('''
        SELECT COUNT(DISTINCT sa.student_id) as count
        FROM student_attendance sa
        JOIN attendance_sessions as_ ON sa.session_id = as_.session_id
        WHERE as_.lecturer_id = ? AND DATE(sa.attendance_time) = ?
    ''', (current_user.id, today.isoformat())).fetchone()['count']

    # Get active sessions count
    active_sessions_count = conn.execute('''
        SELECT COUNT(*) as count
        FROM attendance_sessions as_
        WHERE as_.lecturer_id = ? AND as_.is_active = 1 AND as_.is_completed = 0
    ''', (current_user.id,)).fetchone()['count']

    # Calculate attendance rate for lecturer's modules (last 30 days)
    thirty_days_ago = (datetime.now() - timedelta(days=30)).date()
    attendance_stats = conn.execute('''
        SELECT
            COUNT(DISTINCT sa.student_id || '-' || sa.session_id) as attended,
            COUNT(DISTINCT mr.student_id || '-' || as_.session_id) as total_possible
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        JOIN module_registrations mr ON m.module_id = mr.module_id AND mr.registration_status = 'active'
        LEFT JOIN student_attendance sa ON as_.session_id = sa.session_id AND mr.student_id = sa.student_id
        WHERE ml.lecturer_id = ? AND as_.session_date >= ? AND as_.is_completed = 1
    ''', (current_user.id, thirty_days_ago.isoformat())).fetchone()

    attendance_rate = 0
    if attendance_stats['total_possible'] > 0:
        attendance_rate = round((attendance_stats['attended'] / attendance_stats['total_possible']) * 100, 1)

    conn.close()

    return jsonify({
        'total_students': total_students,
        'todays_attendance': todays_attendance,
        'active_sessions': active_sessions_count,
        'attendance_rate': attendance_rate,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/staff/recent_activity')
@login_required
def staff_recent_activity():
    """API endpoint for recent activity feed"""
    if current_user.is_student:
        return jsonify({'error': 'Access denied'}), 403

    from datetime import datetime, timedelta

    conn = get_db_connection()

    # Get recent activities (last 24 hours)
    yesterday = datetime.now() - timedelta(hours=24)

    activities = []

    # Recent attendance sessions
    recent_sessions = conn.execute('''
        SELECT as_.session_name, as_.session_date, as_.created_at, as_.is_completed,
               m.module_code, m.module_name,
               COUNT(DISTINCT sa.student_id) as attendance_count
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        LEFT JOIN student_attendance sa ON as_.session_id = sa.session_id
        WHERE as_.lecturer_id = ? AND as_.created_at >= ?
        GROUP BY as_.session_id, as_.session_name, as_.session_date, as_.created_at,
                 as_.is_completed, m.module_code, m.module_name
        ORDER BY as_.created_at DESC
        LIMIT 5
    ''', (current_user.id, yesterday.isoformat())).fetchall()

    for session in recent_sessions:
        status = "Completed" if session['is_completed'] else "Active"
        badge_class = "success" if session['is_completed'] else "warning"

        activities.append({
            'type': 'session',
            'title': f"{session['module_code']} - {session['session_name']}",
            'description': f"Attendance recorded for {session['attendance_count']} students",
            'time': session['created_at'],
            'status': status,
            'badge_class': badge_class
        })

    # Recent student registrations in lecturer's modules
    recent_registrations = conn.execute('''
        SELECT s.first_name, s.last_name, s.student_number, mr.registration_date,
               m.module_code, m.module_name
        FROM module_registrations mr
        JOIN students s ON mr.student_id = s.student_id
        JOIN modules m ON mr.module_id = m.module_id
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        WHERE ml.lecturer_id = ? AND mr.created_at >= ?
        ORDER BY mr.created_at DESC
        LIMIT 3
    ''', (current_user.id, yesterday.isoformat())).fetchall()

    for reg in recent_registrations:
        activities.append({
            'type': 'registration',
            'title': f"New Student Registration",
            'description': f"{reg['first_name']} {reg['last_name']} enrolled in {reg['module_code']}",
            'time': reg['registration_date'],
            'status': "New",
            'badge_class': "primary"
        })

    # Sort all activities by time
    activities.sort(key=lambda x: x['time'], reverse=True)

    conn.close()

    return jsonify({
        'activities': activities[:10],  # Return top 10 most recent
        'timestamp': datetime.now().isoformat()
    })

@app.route('/staff/reports')
@login_required
def staff_reports():
    """Staff reports page"""
    if current_user.is_student:
        return redirect(url_for('student_dashboard'))

    from datetime import datetime, date, timedelta

    conn = get_db_connection()

    # Get lecturer's modules for filter dropdown
    modules = conn.execute('''
        SELECT m.module_id, m.module_code, m.module_name
        FROM modules m
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        WHERE ml.lecturer_id = ?
        ORDER BY m.module_code
    ''', (current_user.id,)).fetchall()

    # Get summary statistics
    today = date.today()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # Weekly stats
    weekly_stats = conn.execute('''
        SELECT
            COUNT(DISTINCT as_.session_id) as total_sessions,
            COUNT(DISTINCT sa.student_id) as unique_students,
            COUNT(sa.attendance_id) as total_attendance
        FROM attendance_sessions as_
        LEFT JOIN student_attendance sa ON as_.session_id = sa.session_id
        WHERE as_.lecturer_id = ? AND as_.session_date >= ?
    ''', (current_user.id, week_ago.isoformat())).fetchone()

    # Monthly stats
    monthly_stats = conn.execute('''
        SELECT
            COUNT(DISTINCT as_.session_id) as total_sessions,
            COUNT(DISTINCT sa.student_id) as unique_students,
            COUNT(sa.attendance_id) as total_attendance
        FROM attendance_sessions as_
        LEFT JOIN student_attendance sa ON as_.session_id = sa.session_id
        WHERE as_.lecturer_id = ? AND as_.session_date >= ?
    ''', (current_user.id, month_ago.isoformat())).fetchone()

    conn.close()

    return render_template('staff/reports.html',
                         modules=modules,
                         weekly_stats=weekly_stats,
                         monthly_stats=monthly_stats)

@app.route('/api/staff/generate_report')
@login_required
def generate_report():
    """Generate attendance report based on filters"""
    if current_user.is_student:
        return jsonify({'error': 'Access denied'}), 403

    from datetime import datetime, date

    # Get query parameters
    module_id = request.args.get('module_id')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    report_type = request.args.get('report_type', 'summary')

    conn = get_db_connection()

    # Build base query
    base_query = '''
        SELECT as_.session_id, as_.session_name, as_.session_date, as_.is_completed,
               m.module_code, m.module_name,
               COUNT(DISTINCT mr.student_id) as enrolled_count,
               COUNT(DISTINCT sa.student_id) as attendance_count,
               CASE
                   WHEN COUNT(DISTINCT mr.student_id) > 0
                   THEN ROUND((COUNT(DISTINCT sa.student_id) * 100.0 / COUNT(DISTINCT mr.student_id)), 1)
                   ELSE 0
               END as attendance_rate
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        LEFT JOIN module_registrations mr ON m.module_id = mr.module_id AND mr.registration_status = 'active'
        LEFT JOIN student_attendance sa ON as_.session_id = sa.session_id AND mr.student_id = sa.student_id
        WHERE ml.lecturer_id = ?
    '''

    params = [current_user.id]

    # Add filters
    if module_id:
        base_query += ' AND m.module_id = ?'
        params.append(module_id)

    if start_date:
        base_query += ' AND as_.session_date >= ?'
        params.append(start_date)

    if end_date:
        base_query += ' AND as_.session_date <= ?'
        params.append(end_date)

    base_query += '''
        GROUP BY as_.session_id, as_.session_name, as_.session_date, as_.is_completed,
                 m.module_code, m.module_name
        ORDER BY as_.session_date DESC, as_.scheduled_start_time DESC
    '''

    sessions = conn.execute(base_query, params).fetchall()

    conn.close()

    # Convert to list of dictionaries
    sessions_data = []
    for session in sessions:
        sessions_data.append({
            'session_id': session['session_id'],
            'session_name': session['session_name'],
            'session_date': session['session_date'],
            'module_code': session['module_code'],
            'module_name': session['module_name'],
            'enrolled_count': session['enrolled_count'],
            'attendance_count': session['attendance_count'],
            'attendance_rate': session['attendance_rate'],
            'is_completed': session['is_completed']
        })

    return jsonify({
        'type': report_type,
        'sessions': sessions_data,
        'total_sessions': len(sessions_data),
        'filters': {
            'module_id': module_id,
            'start_date': start_date,
            'end_date': end_date,
            'report_type': report_type
        },
        'generated_at': datetime.now().isoformat()
    })

@app.route('/staff/session/<int:session_id>/monitor')
@login_required
def monitor_attendance_session(session_id):
    """Monitor an active attendance session"""
    if current_user.is_student:
        return redirect(url_for('student_dashboard'))

    # Placeholder for session monitoring
    flash('Session monitoring feature coming soon!', 'info')
    return redirect(url_for('staff_dashboard'))

@app.route('/staff/session/<int:session_id>/start')
@login_required
def start_attendance_session(session_id):
    """Start an attendance session"""
    if current_user.is_student:
        return redirect(url_for('student_dashboard'))

    from datetime import datetime, timedelta

    conn = get_db_connection()

    # Update session to active with attendance window
    now = datetime.now()
    window_end = now + timedelta(minutes=2)  # 2-minute window

    conn.execute('''
        UPDATE attendance_sessions
        SET is_active = 1,
            attendance_window_start = ?,
            attendance_window_end = ?
        WHERE session_id = ? AND lecturer_id = ?
    ''', (now.isoformat(), window_end.isoformat(), session_id, current_user.id))

    conn.commit()
    conn.close()

    flash('Attendance session started! Students have 2 minutes to mark attendance.', 'success')
    return redirect(url_for('staff_dashboard'))

@app.route('/staff/session/<int:session_id>/report')
@login_required
def view_session_report(session_id):
    """View detailed report for a specific session"""
    if current_user.is_student:
        return redirect(url_for('student_dashboard'))

    # Placeholder for session report
    flash('Session report feature coming soon!', 'info')
    return redirect(url_for('staff_dashboard'))

@app.route('/staff/students')
@login_required
def manage_students():
    """Manage students - view enrollments and attendance"""
    if current_user.is_student:
        return redirect(url_for('student_dashboard'))

    conn = get_db_connection()

    # Get students enrolled in lecturer's modules
    students = conn.execute('''
        SELECT DISTINCT s.student_id, s.student_number, s.first_name, s.last_name, s.email,
               COUNT(DISTINCT mr.module_id) as enrolled_modules,
               COUNT(DISTINCT ar.record_id) as total_attendance
        FROM students s
        JOIN module_registrations mr ON s.student_id = mr.student_id
        JOIN modules m ON mr.module_id = m.module_id
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        LEFT JOIN attendance_records ar ON s.student_id = ar.student_id
        WHERE ml.lecturer_id = ? AND mr.registration_status = 'active'
        GROUP BY s.student_id, s.student_number, s.first_name, s.last_name, s.email
        ORDER BY s.last_name, s.first_name
    ''', (current_user.id,)).fetchall()

    # Get lecturer's modules for filtering
    modules = conn.execute('''
        SELECT m.module_id, m.module_code, m.module_name
        FROM modules m
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        WHERE ml.lecturer_id = ?
        ORDER BY m.module_code
    ''', (current_user.id,)).fetchall()

    conn.close()

    return render_template('staff/manage_students.html', students=students, modules=modules)

@app.route('/staff/students/<int:student_id>')
@login_required
def view_student_details(student_id):
    """View detailed student information and attendance"""
    if current_user.is_student:
        return redirect(url_for('student_dashboard'))

    conn = get_db_connection()

    # Get student basic info
    student = conn.execute('''
        SELECT s.student_id, s.student_number, s.first_name, s.last_name, s.email,
               s.phone_number, s.date_of_birth, s.created_at
        FROM students s
        WHERE s.student_id = ?
    ''', (student_id,)).fetchone()

    if not student:
        flash('Student not found.', 'error')
        return redirect(url_for('manage_students'))

    # Get student's module enrollments (only for lecturer's modules)
    enrollments = conn.execute('''
        SELECT m.module_code, m.module_name, mr.registration_date, mr.registration_status,
               COUNT(DISTINCT ar.record_id) as attendance_count,
               COUNT(DISTINCT as_.session_id) as total_sessions
        FROM module_registrations mr
        JOIN modules m ON mr.module_id = m.module_id
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        LEFT JOIN attendance_records ar ON mr.student_id = ar.student_id
            AND ar.session_id IN (
                SELECT session_id FROM attendance_sessions WHERE module_id = m.module_id
            )
        LEFT JOIN attendance_sessions as_ ON m.module_id = as_.module_id
        WHERE mr.student_id = ? AND ml.lecturer_id = ?
        GROUP BY m.module_id, m.module_code, m.module_name, mr.registration_date, mr.registration_status
        ORDER BY m.module_code
    ''', (student_id, current_user.id)).fetchall()

    # Get recent attendance records
    recent_attendance = conn.execute('''
        SELECT ar.attendance_time, ar.attendance_status, m.module_code, as_.session_name
        FROM attendance_records ar
        JOIN attendance_sessions as_ ON ar.session_id = as_.session_id
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        WHERE ar.student_id = ? AND ml.lecturer_id = ?
        ORDER BY ar.attendance_time DESC
        LIMIT 10
    ''', (student_id, current_user.id)).fetchall()

    conn.close()

    return render_template('staff/student_details.html',
                         student=student,
                         enrollments=enrollments,
                         recent_attendance=recent_attendance)

@app.route('/staff/attendance/create', methods=['GET', 'POST'])
@login_required
def create_attendance_session():
    """Create a new attendance session"""
    if current_user.is_student:
        return redirect(url_for('student_dashboard'))

    if request.method == 'POST':
        try:
            module_id = request.form['module_id']
            session_name = request.form['session_name']
            session_type = request.form['session_type']
            session_date = request.form['session_date']
            start_time = request.form['start_time']
            end_time = request.form['end_time']
            location = request.form.get('location', '')

            conn = get_db_connection()

            # Create the attendance session
            cursor = conn.execute('''
                INSERT INTO attendance_sessions (
                    module_id, lecturer_id, session_name, session_type,
                    session_date, scheduled_start_time, scheduled_end_time, location
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (module_id, current_user.id, session_name, session_type,
                  session_date, start_time, end_time, location))

            session_id = cursor.lastrowid
            conn.commit()
            conn.close()

            flash('Attendance session created successfully!', 'success')
            return redirect(url_for('staff_dashboard'))

        except Exception as e:
            flash(f'Error creating session: {str(e)}', 'error')

    # Get lecturer's modules for the form
    conn = get_db_connection()
    modules = conn.execute('''
        SELECT m.module_id, m.module_code, m.module_name, c.course_name
        FROM modules m
        JOIN course_years cy ON m.course_year_id = cy.course_year_id
        JOIN courses c ON cy.course_id = c.course_id
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        WHERE ml.lecturer_id = ?
        ORDER BY c.course_name, m.module_name
    ''', (current_user.id,)).fetchall()
    conn.close()

    return render_template('staff/create_session.html', modules=modules)





@app.route('/staff/attendance/stop/<int:session_id>')
@login_required
def stop_attendance_session(session_id):
    """Stop an attendance session"""
    if current_user.is_student:
        return redirect(url_for('student_dashboard'))

    try:
        conn = get_db_connection()

        # Verify lecturer owns this session
        session_data = conn.execute('''
            SELECT session_id FROM attendance_sessions
            WHERE session_id = ? AND lecturer_id = ?
        ''', (session_id, current_user.id)).fetchone()

        if not session_data:
            flash('Session not found or access denied.', 'error')
            return redirect(url_for('staff_dashboard'))

        # Stop the session
        conn.execute('''
            UPDATE attendance_sessions
            SET is_active = 0, is_completed = 1, attendance_window_end = CURRENT_TIMESTAMP
            WHERE session_id = ?
        ''', (session_id,))

        conn.commit()
        conn.close()

        flash('Attendance session stopped successfully.', 'success')
        return redirect(url_for('staff_dashboard'))

    except Exception as e:
        flash(f'Error stopping session: {str(e)}', 'error')
        return redirect(url_for('staff_dashboard'))

@app.route('/student/attendance')
@login_required
def student_attendance():
    """Student attendance page - view active sessions and mark attendance"""
    if not current_user.is_student:
        return redirect(url_for('staff_dashboard'))

    student_id = current_user.id - 10000  # Convert back from login ID

    conn = get_db_connection()

    # Get active attendance sessions for student's enrolled modules
    from datetime import datetime
    current_time = datetime.now().isoformat()

    # Get all sessions first, then filter in Python to avoid SQLite datetime conversion issues
    all_sessions = conn.execute('''
        SELECT DISTINCT as_.session_id, as_.session_name, as_.session_date,
               as_.attendance_window_start, as_.attendance_window_end,
               as_.is_active, as_.location, m.module_code, m.module_name,
               CASE WHEN sa.attendance_id IS NOT NULL THEN 1 ELSE 0 END as already_marked
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        JOIN student_enrollments se ON mr.enrollment_id = se.enrollment_id
        LEFT JOIN student_attendance sa ON as_.session_id = sa.session_id AND sa.student_id = ?
        WHERE se.student_id = ? AND mr.registration_status = 'active'
        AND as_.is_active = 1
        AND as_.attendance_window_start IS NOT NULL
        AND as_.attendance_window_end IS NOT NULL
        ORDER BY as_.attendance_window_end ASC
    ''', (student_id, student_id)).fetchall()

    # Filter sessions with open attendance windows in Python
    active_sessions = []
    for session in all_sessions:
        try:
            window_start = datetime.fromisoformat(session['attendance_window_start'])
            window_end = datetime.fromisoformat(session['attendance_window_end'])
            now = datetime.now()

            if window_start <= now <= window_end:
                active_sessions.append(session)
        except (ValueError, TypeError) as e:
            print(f"Error parsing session {session['session_id']} time window: {e}")
            continue

    # Get recent attendance history
    recent_attendance = conn.execute('''
        SELECT as_.session_name, as_.session_date, m.module_code, m.module_name,
               sa.attendance_time, sa.confidence_score
        FROM student_attendance sa
        JOIN attendance_sessions as_ ON sa.session_id = as_.session_id
        JOIN modules m ON as_.module_id = m.module_id
        WHERE sa.student_id = ?
        ORDER BY sa.attendance_time DESC
        LIMIT 10
    ''', (student_id,)).fetchall()

    conn.close()

    return render_template('student/attendance.html',
                         active_sessions=active_sessions,
                         recent_attendance=recent_attendance)

@app.route('/student/attendance/mark/<int:session_id>')
@login_required
def mark_attendance_page(session_id):
    """Page for marking attendance with facial recognition"""
    if not current_user.is_student:
        return redirect(url_for('staff_dashboard'))

    student_id = current_user.id - 10000  # Convert back from login ID

    conn = get_db_connection()

    # Verify session is active and student is enrolled
    session_data = conn.execute('''
        SELECT as_.*, m.module_code, m.module_name
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        WHERE as_.session_id = ? AND mr.student_id = ? AND mr.registration_status = 'active'
        AND as_.is_active = 1
    ''', (session_id, student_id)).fetchone()

    if not session_data:
        flash('Session not found or you are not enrolled in this module.', 'error')
        return redirect(url_for('student_attendance'))

    # Check if attendance window is open
    from datetime import datetime
    try:
        now = datetime.now()
        window_start = datetime.fromisoformat(session_data['attendance_window_start'])
        window_end = datetime.fromisoformat(session_data['attendance_window_end'])

        if now < window_start or now > window_end:
            flash('Attendance window is not currently open for this session.', 'error')
            return redirect(url_for('student_attendance'))
    except Exception as e:
        flash('Error checking attendance window. Please try again.', 'error')
        return redirect(url_for('student_attendance'))

    # Check if already marked
    existing_attendance = conn.execute('''
        SELECT attendance_id FROM student_attendance
        WHERE session_id = ? AND student_id = ?
    ''', (session_id, student_id)).fetchone()

    conn.close()

    if existing_attendance:
        flash('You have already marked attendance for this session.', 'info')
        return redirect(url_for('student_attendance'))

    return render_template('student/mark_attendance.html', session=session_data)

@app.route('/student/modules')
@login_required
def student_view_modules():
    """View student's enrolled modules"""
    if not current_user.is_student:
        return redirect(url_for('staff_dashboard'))

    student_id = current_user.id - 10000  # Convert back from login ID

    conn = get_db_connection()

    # Get student's enrolled modules with detailed information
    enrolled_modules = conn.execute('''
        SELECT DISTINCT m.module_id, m.module_code, m.module_name, m.description,
               m.credits, m.semester, m.is_elective,
               c.course_name, cy.year_name, cy.year_level,
               f.faculty_name,
               mr.registration_date, mr.registration_status
        FROM module_registrations mr
        JOIN student_enrollments se ON mr.enrollment_id = se.enrollment_id
        JOIN modules m ON mr.module_id = m.module_id
        JOIN course_years cy ON m.course_year_id = cy.course_year_id
        JOIN courses c ON cy.course_id = c.course_id
        JOIN faculties f ON c.faculty_id = f.faculty_id
        WHERE se.student_id = ? AND mr.registration_status = 'active'
        ORDER BY m.semester, m.is_elective ASC, m.module_name
    ''', (student_id,)).fetchall()

    # Calculate summary statistics
    total_modules = len(enrolled_modules)
    total_credits = sum(module['credits'] for module in enrolled_modules)
    core_modules = [m for m in enrolled_modules if not m['is_elective']]
    elective_modules = [m for m in enrolled_modules if m['is_elective']]

    # Group modules by semester
    modules_by_semester = {}
    for module in enrolled_modules:
        semester = module['semester']
        if semester not in modules_by_semester:
            modules_by_semester[semester] = []
        modules_by_semester[semester].append(module)

    conn.close()

    return render_template('student/view_modules.html',
                         enrolled_modules=enrolled_modules,
                         modules_by_semester=modules_by_semester,
                         total_modules=total_modules,
                         total_credits=total_credits,
                         core_modules=core_modules,
                         elective_modules=elective_modules)

@app.route('/student/attendance/view')
@login_required
def view_attendance_records():
    """View detailed attendance records and marks per module"""
    if not current_user.is_student:
        return redirect(url_for('staff_dashboard'))

    student_id = current_user.id - 10000  # Convert back from login ID

    conn = get_db_connection()

    # Get attendance summary per module
    attendance_summary = conn.execute('''
        SELECT
            m.module_id,
            m.module_code,
            m.module_name,
            m.semester,
            c.course_name,
            cy.year_level,
            COUNT(DISTINCT as_.session_id) as total_sessions,
            COUNT(DISTINCT sa.session_id) as attended_sessions,
            CASE
                WHEN COUNT(DISTINCT as_.session_id) > 0
                THEN ROUND((COUNT(DISTINCT sa.session_id) * 100.0 / COUNT(DISTINCT as_.session_id)), 2)
                ELSE 0
            END as attendance_percentage,
            CASE
                WHEN COUNT(DISTINCT as_.session_id) > 0
                THEN ROUND((COUNT(DISTINCT sa.session_id) * 100.0 / COUNT(DISTINCT as_.session_id)) * 0.1, 2)
                ELSE 0
            END as attendance_contribution
        FROM modules m
        JOIN course_years cy ON m.course_year_id = cy.course_year_id
        JOIN courses c ON cy.course_id = c.course_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        JOIN student_enrollments se ON mr.enrollment_id = se.enrollment_id
        LEFT JOIN attendance_sessions as_ ON m.module_id = as_.module_id AND as_.is_completed = 1
        LEFT JOIN student_attendance sa ON as_.session_id = sa.session_id AND sa.student_id = ?
        WHERE se.student_id = ? AND mr.registration_status = 'active'
        GROUP BY m.module_id, m.module_code, m.module_name, m.semester, c.course_name, cy.year_level
        ORDER BY c.course_name, cy.year_level, m.module_name
    ''', (student_id, student_id)).fetchall()

    # Get detailed attendance records
    detailed_records = conn.execute('''
        SELECT
            as_.session_name,
            as_.session_date,
            as_.session_type,
            as_.location,
            m.module_code,
            m.module_name,
            sa.attendance_time,
            sa.confidence_score,
            CASE WHEN sa.attendance_id IS NOT NULL THEN 100.0 ELSE 0.0 END as session_mark
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        JOIN student_enrollments se ON mr.enrollment_id = se.enrollment_id
        LEFT JOIN student_attendance sa ON as_.session_id = sa.session_id AND sa.student_id = ?
        WHERE se.student_id = ? AND mr.registration_status = 'active' AND as_.is_completed = 1
        ORDER BY as_.session_date DESC, as_.scheduled_start_time DESC
    ''', (student_id, student_id)).fetchall()

    # Calculate overall statistics
    total_sessions = len([r for r in detailed_records])
    attended_sessions = len([r for r in detailed_records if r['attendance_time']])
    overall_percentage = (attended_sessions / total_sessions * 100) if total_sessions > 0 else 0
    overall_contribution = overall_percentage * 0.1

    conn.close()

    return render_template('student/view_attendance.html',
                         attendance_summary=attendance_summary,
                         detailed_records=detailed_records,
                         total_sessions=total_sessions,
                         attended_sessions=attended_sessions,
                         overall_percentage=overall_percentage,
                         overall_contribution=overall_contribution)

@app.route('/staff/attendance/calculate', methods=['GET', 'POST'])
@login_required
def calculate_attendance_marks():
    """Calculate and finalize attendance marks for modules"""
    if current_user.is_student:
        return redirect(url_for('student_dashboard'))

    if request.method == 'POST':
        try:
            module_id = request.form.get('module_id')
            academic_year = request.form.get('academic_year', '2024/2025')

            conn = get_db_connection()

            if module_id:
                # Calculate for specific module
                calculate_module_attendance(conn, module_id, academic_year, current_user.id)
                flash(f'Attendance marks calculated for selected module.', 'success')
            else:
                # Calculate for all lecturer's modules
                modules = conn.execute('''
                    SELECT m.module_id FROM modules m
                    JOIN module_lecturers ml ON m.module_id = ml.module_id
                    WHERE ml.lecturer_id = ?
                ''', (current_user.id,)).fetchall()

                for module in modules:
                    calculate_module_attendance(conn, module['module_id'], academic_year, current_user.id)

                flash(f'Attendance marks calculated for all your modules.', 'success')

            conn.close()
            return redirect(url_for('calculate_attendance_marks'))

        except Exception as e:
            flash(f'Error calculating marks: {str(e)}', 'error')

    # Get lecturer's modules for the form
    conn = get_db_connection()
    modules = conn.execute('''
        SELECT m.module_id, m.module_code, m.module_name, c.course_name
        FROM modules m
        JOIN course_years cy ON m.course_year_id = cy.course_year_id
        JOIN courses c ON cy.course_id = c.course_id
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        WHERE ml.lecturer_id = ?
        ORDER BY c.course_name, m.module_name
    ''', (current_user.id,)).fetchall()

    # Get recent calculations
    recent_calculations = conn.execute('''
        SELECT am.*, m.module_code, m.module_name, s.student_number, s.first_name, s.last_name
        FROM attendance_marks am
        JOIN modules m ON am.module_id = m.module_id
        JOIN students s ON am.student_id = s.student_id
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        WHERE ml.lecturer_id = ?
        ORDER BY am.last_calculated DESC
        LIMIT 20
    ''', (current_user.id,)).fetchall()

    conn.close()

    return render_template('staff/calculate_marks.html',
                         modules=modules,
                         recent_calculations=recent_calculations)

def calculate_module_attendance(conn, module_id, academic_year, lecturer_id=None):
    """Calculate attendance marks for a specific module"""

    # Get all students enrolled in this module
    students = conn.execute('''
        SELECT DISTINCT s.student_id
        FROM students s
        JOIN module_registrations mr ON s.student_id = mr.student_id
        JOIN student_enrollments se ON mr.enrollment_id = se.enrollment_id
        WHERE mr.module_id = ? AND mr.registration_status = 'active'
    ''', (module_id,)).fetchall()

    for student in students:
        student_id = student['student_id']

        # Count total completed sessions for this module
        total_sessions = conn.execute('''
            SELECT COUNT(*) as count
            FROM attendance_sessions
            WHERE module_id = ? AND is_completed = 1
        ''', (module_id,)).fetchone()['count']

        # Count attended sessions for this student
        attended_sessions = conn.execute('''
            SELECT COUNT(*) as count
            FROM student_attendance sa
            JOIN attendance_sessions as_ ON sa.session_id = as_.session_id
            WHERE as_.module_id = ? AND sa.student_id = ?
        ''', (module_id, student_id)).fetchone()['count']

        # Calculate percentages
        attendance_percentage = (attended_sessions / total_sessions * 100) if total_sessions > 0 else 0
        attendance_mark = attendance_percentage  # Each session is worth 100% if attended
        final_contribution = attendance_percentage * 0.1  # 10% of total grade

        # Insert or update attendance marks
        conn.execute('''
            INSERT OR REPLACE INTO attendance_marks (
                student_id, module_id, academic_year, total_sessions, attended_sessions,
                attendance_percentage, attendance_mark, final_attendance_contribution,
                last_calculated
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (student_id, module_id, academic_year, total_sessions, attended_sessions,
              attendance_percentage, attendance_mark, final_contribution))

    conn.commit()

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page for students and staff"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        remember_me = 'remember_me' in request.form

        print(f"🔐 Login attempt - Username: {username}, Password length: {len(password)}")

        conn = get_db_connection()

        # Try to authenticate as student first
        student = conn.execute(
            'SELECT student_id, student_number, email, password_hash FROM students '
            'WHERE (student_number = ? OR email = ?) AND is_active = 1',
            (username, username)
        ).fetchone()

        print(f"🔍 Student found: {student is not None}")
        if student:
            print(f"🔍 Student details - ID: {student['student_id']}, Email: {student['email']}, Number: {student['student_number']}")
            print(f"🔍 Password hash from DB: {student['password_hash'][:50]}...")

            # Verify password for student
            password_valid = bcrypt.check_password_hash(student['password_hash'], password)
            print(f"🔍 Password verification result: {password_valid}")

            if password_valid:
                # Use student_id + 10000 to avoid conflicts with staff IDs
                student_login_id = student['student_id'] + 10000
                user_obj = User(student_login_id, student['student_number'],
                              student['email'], 'student', True)
                login_user(user_obj, remember=remember_me)
                conn.close()
                flash('Welcome back!', 'success')
                return redirect(url_for('student_facial_setup'))

        # Try to authenticate as staff
        staff = conn.execute(
            'SELECT u.user_id, u.username, u.email, u.password_hash, ur.role_name FROM users u '
            'JOIN user_roles ur ON u.role_id = ur.role_id '
            'WHERE (u.username = ? OR u.email = ?) AND u.is_active = 1',
            (username, username)
        ).fetchone()

        if staff:
            # For demo purposes, using bcrypt for staff too
            if bcrypt.check_password_hash(staff['password_hash'], password):
                user_obj = User(staff['user_id'], staff['username'],
                              staff['email'], staff['role_name'], False)
                login_user(user_obj, remember=remember_me)
                conn.close()
                flash('Welcome back!', 'success')
                return redirect(url_for('staff_dashboard'))

        conn.close()
        flash('Invalid username or password.', 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """Logout route"""
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('index'))

# Debug route to check session data
@app.route('/debug/session')
def debug_session():
    """Debug route to check session data"""
    if 'student_registration' in session:
        return f"<pre>Session data: {session['student_registration']}</pre>"
    else:
        return "<pre>No registration data in session</pre>"

# Debug route to test registration completion
@app.route('/debug/test_complete', methods=['GET', 'POST'])
def debug_test_complete():
    """Debug route to test registration completion"""
    if request.method == 'GET':
        return """
        <form method="POST">
            <button type="submit">Test Complete Registration</button>
        </form>
        """
    else:
        try:
            result = student_complete_registration()
            return f"<pre>Result: {result}</pre>"
        except Exception as e:
            return f"<pre>Error: {str(e)}</pre>"

if __name__ == '__main__':
    app.run(debug=True)
