import sqlite3
from flask_bcrypt import Bcrypt

def reset_student_password():
    """Reset password for student 22302925 to 'student123'"""
    conn = sqlite3.connect('attendance_fixed.db')
    
    # Hash the new password
    bcrypt = Bcrypt()
    new_password = 'student123'
    password_hash = bcrypt.generate_password_hash(new_password).decode('utf-8')
    
    # Update the student's password
    conn.execute('''
        UPDATE students
        SET password_hash = ?
        WHERE student_number = ?
    ''', (password_hash, '22302925'))
    
    conn.commit()
    
    # Verify the update
    student = conn.execute('''
        SELECT student_id, student_number, first_name, last_name, password_hash
        FROM students
        WHERE student_number = ?
    ''', ('22302925',)).fetchone()
    
    if student:
        # Test the new password
        if bcrypt.check_password_hash(student[4], new_password):
            print(f"✅ Password reset successful for {student[2]} {student[3]}")
            print(f"   Student Number: {student[1]}")
            print(f"   New Password: {new_password}")
        else:
            print("❌ Password verification failed")
    else:
        print("❌ Student not found")
    
    conn.close()

if __name__ == "__main__":
    reset_student_password()
