{% extends "base.html" %}

{% block title %}Manage Students - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="display-6">
                    <i class="fas fa-users me-2"></i>
                    Manage Students
                </h1>
                <a href="{{ url_for('staff_dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-4">
            <div class="card border-0 shadow-sm bg-primary text-white">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-user-graduate" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1">{{ students|length }}</h4>
                    <p class="mb-0 small">Total Students</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm bg-success text-white">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-book" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1">{{ modules|length }}</h4>
                    <p class="mb-0 small">Your Modules</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm bg-info text-white">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-chart-line" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1">{{ students|sum(attribute='total_attendance') }}</h4>
                    <p class="mb-0 small">Total Attendance Records</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Students Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list me-2"></i>
                                Students in Your Modules
                            </h5>
                        </div>
                        <div class="col-auto">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="Search students...">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if students %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="studentsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>Student Number</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Enrolled Modules</th>
                                    <th>Total Attendance</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for student in students %}
                                <tr>
                                    <td>
                                        <span class="fw-bold">{{ student.student_number }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                {{ student.first_name[0] }}{{ student.last_name[0] }}
                                            </div>
                                            <div>
                                                <div class="fw-medium">{{ student.first_name }} {{ student.last_name }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ student.email }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ student.enrolled_modules }} modules</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ student.total_attendance }} records</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('view_student_details', student_id=student.student_id) }}" 
                                               class="btn btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-info" title="Send Message" disabled>
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users text-muted" style="font-size: 4rem; opacity: 0.5;"></i>
                        <h5 class="text-muted mt-3">No Students Found</h5>
                        <p class="text-muted">No students are enrolled in your modules yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Module Filter (if needed) -->
    {% if modules %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>
                        Your Modules
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for module in modules %}
                        <div class="col-md-4 mb-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-book text-primary me-2"></i>
                                <span class="fw-medium">{{ module.module_code }}</span>
                                <span class="text-muted ms-2">{{ module.module_name }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
    font-weight: 600;
}
</style>

<script>
// Simple search functionality
document.getElementById('searchInput').addEventListener('keyup', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('#studentsTable tbody tr');
    
    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});
</script>
{% endblock %}
