{% extends "base.html" %}

{% block title %}Facial Recognition Test - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-lg border-0">
                <div class="card-header text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-user-check me-2"></i>
                        Facial Recognition Test
                    </h2>
                    <p class="mb-0 mt-2">Test the facial recognition system for attendance</p>
                </div>
                <div class="card-body p-5">
                    <div class="row">
                        <!-- Camera Section -->
                        <div class="col-md-8">
                            <div class="text-center">
                                <div class="camera-container mb-4">
                                    <video id="video" width="640" height="480" autoplay style="border: 3px solid #28a745; border-radius: 10px; background: #000;"></video>
                                    <canvas id="canvas" width="640" height="480" style="display: none;"></canvas>

                                    <!-- Face outline guide -->
                                    <div class="face-guide-overlay">
                                        <div class="face-outline">
                                            <div class="face-guide-text">Position your face here</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="camera-controls">
                                    <button id="startCamera" class="btn btn-success btn-lg me-3">
                                        <i class="fas fa-video me-2"></i>
                                        Start Camera
                                    </button>
                                    <button id="recognizeFace" class="btn btn-primary btn-lg me-3" disabled>
                                        <i class="fas fa-search me-2"></i>
                                        Recognize Face
                                    </button>
                                    <button id="markAttendance" class="btn btn-warning btn-lg" style="display: none;">
                                        <i class="fas fa-check me-2"></i>
                                        Mark Attendance
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Results Section -->
                        <div class="col-md-4">
                            <div class="results-panel">
                                <h5 class="mb-3">
                                    <i class="fas fa-clipboard-list me-2 text-success"></i>
                                    Recognition Results
                                </h5>
                                
                                <div id="recognitionStatus" class="alert alert-light">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Start camera and click "Recognize Face" to test the system
                                </div>
                                
                                <div id="studentInfo" style="display: none;">
                                    <div class="card border-success">
                                        <div class="card-body">
                                            <h6 class="card-title text-success">
                                                <i class="fas fa-user-check me-2"></i>
                                                Student Recognized
                                            </h6>
                                            <p class="card-text">
                                                <strong>Name:</strong> <span id="studentName"></span><br>
                                                <strong>Student Number:</strong> <span id="studentNumber"></span><br>
                                                <strong>Confidence:</strong> <span id="confidence"></span>%
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="system-info mt-4">
                                    <h6 class="mb-2">
                                        <i class="fas fa-cogs me-2 text-info"></i>
                                        System Status
                                    </h6>
                                    <ul class="list-unstyled small">
                                        <li id="cameraStatus" class="mb-1">
                                            <i class="fas fa-circle text-muted me-2"></i>
                                            Camera: Not started
                                        </li>
                                        <li id="opencvStatus" class="mb-1">
                                            <i class="fas fa-circle text-muted me-2"></i>
                                            OpenCV: Checking...
                                        </li>
                                        <li id="databaseStatus" class="mb-1">
                                            <i class="fas fa-circle text-success me-2"></i>
                                            Database: Connected
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            <a href="{{ url_for('student_dashboard') }}" class="btn btn-outline-secondary btn-lg me-3">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Dashboard
                            </a>
                            <a href="{{ url_for('student_facial_capture') }}" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-camera me-2"></i>
                                Setup Facial Data
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recognition Result Modal -->
<div class="modal fade" id="attendanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    Attendance Marked
                </h5>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-calendar-check text-success" style="font-size: 4rem;"></i>
                <h4 class="mt-3">Attendance Recorded!</h4>
                <p>Your attendance has been successfully recorded for today.</p>
                <div id="attendanceDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-check me-2"></i>
                    OK
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let video = document.getElementById('video');
let canvas = document.getElementById('canvas');
let context = canvas.getContext('2d');
let recognizedStudent = null;

// Camera controls
document.getElementById('startCamera').addEventListener('click', startCamera);
document.getElementById('recognizeFace').addEventListener('click', recognizeFace);
document.getElementById('markAttendance').addEventListener('click', markAttendance);

async function startCamera() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
            video: { 
                width: 640, 
                height: 480,
                facingMode: 'user'
            } 
        });
        
        video.srcObject = stream;
        document.getElementById('startCamera').disabled = true;
        document.getElementById('recognizeFace').disabled = false;
        
        // Update status
        updateStatus('cameraStatus', 'Camera: Active', 'success');
        
    } catch (err) {
        console.error('Error accessing camera:', err);
        alert('Could not access camera. Please ensure camera permissions are granted.');
        updateStatus('cameraStatus', 'Camera: Error', 'danger');
    }
}

async function recognizeFace() {
    // Capture current frame (flip back to normal orientation for processing)
    context.save();
    context.scale(-1, 1);
    context.drawImage(video, -640, 0, 640, 480);
    context.restore();
    const imageData = canvas.toDataURL('image/jpeg', 0.8);
    
    // Show processing status
    document.getElementById('recognitionStatus').innerHTML = `
        <i class="fas fa-spinner fa-spin me-2"></i>
        Processing facial recognition...
    `;
    document.getElementById('recognitionStatus').className = 'alert alert-info';
    
    try {
        const response = await fetch('/api/recognize_face', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                image: imageData
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Face recognized!
            recognizedStudent = result;
            
            document.getElementById('recognitionStatus').innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                Face recognized successfully!
            `;
            document.getElementById('recognitionStatus').className = 'alert alert-success';
            
            // Show student info
            document.getElementById('studentName').textContent = result.name;
            document.getElementById('studentNumber').textContent = result.student_number;
            document.getElementById('confidence').textContent = Math.round(result.confidence * 100);
            document.getElementById('studentInfo').style.display = 'block';
            document.getElementById('markAttendance').style.display = 'inline-block';
            
            // Update OpenCV status
            if (result.opencv_available) {
                updateStatus('opencvStatus', 'OpenCV: Available', 'success');
            } else {
                updateStatus('opencvStatus', 'OpenCV: Simulated', 'warning');
            }
            
        } else {
            document.getElementById('recognitionStatus').innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${result.error}
            `;
            document.getElementById('recognitionStatus').className = 'alert alert-warning';
            document.getElementById('studentInfo').style.display = 'none';
            document.getElementById('markAttendance').style.display = 'none';
        }
        
    } catch (error) {
        console.error('Error recognizing face:', error);
        document.getElementById('recognitionStatus').innerHTML = `
            <i class="fas fa-times-circle me-2"></i>
            Failed to recognize face. Please try again.
        `;
        document.getElementById('recognitionStatus').className = 'alert alert-danger';
    }
}

async function markAttendance() {
    if (!recognizedStudent) {
        alert('No student recognized. Please recognize a face first.');
        return;
    }
    
    // Simulate attendance marking
    const attendanceDetails = `
        <strong>Student:</strong> ${recognizedStudent.name}<br>
        <strong>Time:</strong> ${new Date().toLocaleString()}<br>
        <strong>Status:</strong> Present
    `;
    
    document.getElementById('attendanceDetails').innerHTML = attendanceDetails;
    
    // Show success modal
    const modal = new bootstrap.Modal(document.getElementById('attendanceModal'));
    modal.show();
    
    // Reset for next recognition
    document.getElementById('studentInfo').style.display = 'none';
    document.getElementById('markAttendance').style.display = 'none';
    recognizedStudent = null;
    
    document.getElementById('recognitionStatus').innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        Ready for next recognition
    `;
    document.getElementById('recognitionStatus').className = 'alert alert-light';
}

function updateStatus(elementId, text, status) {
    const element = document.getElementById(elementId);
    const icon = element.querySelector('i');
    
    element.innerHTML = `<i class="fas fa-circle text-${status} me-2"></i>${text}`;
}

// Check OpenCV status on page load
window.addEventListener('load', function() {
    // Check OpenCV status from server
    checkOpenCVStatus();
});

async function checkOpenCVStatus() {
    try {
        const response = await fetch('/api/opencv_status');
        const result = await response.json();

        if (result.opencv_available) {
            updateStatus('opencvStatus', 'OpenCV: Available', 'success');
        } else {
            updateStatus('opencvStatus', 'OpenCV: Simulated', 'warning');
        }
    } catch (error) {
        updateStatus('opencvStatus', 'OpenCV: Unknown', 'muted');
    }
}

// Cleanup camera stream when leaving page
window.addEventListener('beforeunload', function() {
    if (video.srcObject) {
        video.srcObject.getTracks().forEach(track => track.stop());
    }
});
</script>

<style>
.camera-container {
    position: relative;
    display: inline-block;
}

.face-guide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.face-outline {
    width: 280px;
    height: 350px;
    border: 3px solid rgba(40, 167, 69, 0.8);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    position: relative;
    background: rgba(40, 167, 69, 0.1);
    animation: pulse-guide 2s ease-in-out infinite;
}

.face-guide-text {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(40, 167, 69, 0.9);
    font-size: 14px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
    white-space: nowrap;
}

@keyframes pulse-guide {
    0% {
        border-color: rgba(40, 167, 69, 0.8);
        background: rgba(40, 167, 69, 0.1);
    }
    50% {
        border-color: rgba(40, 167, 69, 1);
        background: rgba(40, 167, 69, 0.2);
    }
    100% {
        border-color: rgba(40, 167, 69, 0.8);
        background: rgba(40, 167, 69, 0.1);
    }
}

.results-panel {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    height: fit-content;
}

.system-info {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
}

#video, #canvas {
    max-width: 100%;
    height: auto;
    transform: scaleX(-1); /* Flip the video horizontally to mirror the user */
}

@media (max-width: 768px) {
    #video, #canvas {
        width: 100%;
        height: auto;
    }
    
    .camera-controls .btn {
        margin-bottom: 10px;
    }
}
</style>
{% endblock %}
