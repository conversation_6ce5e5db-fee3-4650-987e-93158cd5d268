"""
Quick database check to verify structure and data
"""

import sqlite3

def check_database():
    """Check database structure and content"""
    
    conn = sqlite3.connect('attendance_new.db')
    conn.row_factory = sqlite3.Row
    
    print("Database Structure Check")
    print("=" * 50)
    
    # Check faculties
    print("1. FACULTIES:")
    faculties = conn.execute("SELECT * FROM faculties ORDER BY faculty_id").fetchall()
    for faculty in faculties:
        print(f"   ID: {faculty['faculty_id']}, Name: {faculty['faculty_name']}, Code: {faculty['faculty_code']}")
    
    # Check courses
    print("\n2. COURSES:")
    courses = conn.execute("SELECT * FROM courses ORDER BY course_id").fetchall()
    for course in courses:
        print(f"   ID: {course['course_id']}, Name: {course['course_name']}, Faculty: {course['faculty_id']}")
    
    # Check course years
    print("\n3. COURSE YEARS:")
    course_years = conn.execute("SELECT * FROM course_years ORDER BY course_year_id").fetchall()
    for year in course_years:
        print(f"   ID: {year['course_year_id']}, Course: {year['course_id']}, Year: {year['year_number']}")
    
    # Check modules for first course year
    print("\n4. MODULES (for course_year_id = 1):")
    modules = conn.execute("SELECT * FROM modules WHERE course_year_id = 1 ORDER BY module_id").fetchall()
    for module in modules:
        print(f"   ID: {module['module_id']}, Name: {module['module_name']}, Core: {module['is_core']}")
    
    # Check if there are any students
    print("\n5. STUDENTS:")
    students = conn.execute("SELECT * FROM students").fetchall()
    print(f"   Total students: {len(students)}")
    for student in students:
        print(f"   ID: {student['student_id']}, Number: {student['student_number']}, Name: {student['first_name']} {student['last_name']}")
    
    conn.close()

if __name__ == "__main__":
    check_database()
