import sqlite3
from datetime import datetime

def debug_attendance_issue():
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    print("🔍 Debugging attendance marking issue...")
    
    # Check current active sessions
    print("\n📊 Active sessions:")
    sessions = conn.execute('''
        SELECT as_.session_id, as_.session_name, as_.module_id, as_.lecturer_id,
               as_.attendance_window_start, as_.attendance_window_end, as_.is_active,
               m.module_code, m.module_name
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        WHERE as_.is_active = 1
        ORDER BY as_.session_id
    ''').fetchall()
    
    for session in sessions:
        print(f"   Session {session['session_id']}: {session['session_name']}")
        print(f"     Module: {session['module_code']} - {session['module_name']} (ID: {session['module_id']})")
        print(f"     Lecturer ID: {session['lecturer_id']}")
        print(f"     Window: {session['attendance_window_start']} to {session['attendance_window_end']}")
        
        # Check if window is open
        if session['attendance_window_start'] and session['attendance_window_end']:
            try:
                window_start = datetime.fromisoformat(session['attendance_window_start'])
                window_end = datetime.fromisoformat(session['attendance_window_end'])
                now = datetime.now()
                is_open = window_start <= now <= window_end
                print(f"     Window open: {is_open}")
            except Exception as e:
                print(f"     Window parsing error: {e}")
        print()
    
    # Check students and their IDs
    print("🎓 Students:")
    students = conn.execute('''
        SELECT student_id, student_number, first_name, last_name
        FROM students
        ORDER BY student_id
    ''').fetchall()
    
    for student in students:
        print(f"   Student ID {student['student_id']}: {student['student_number']} ({student['first_name']} {student['last_name']})")
    
    # Check module registrations
    print("\n📚 Module registrations:")
    registrations = conn.execute('''
        SELECT mr.registration_id, mr.student_id, mr.module_id, mr.registration_status,
               s.student_number, m.module_code
        FROM module_registrations mr
        JOIN students s ON mr.student_id = s.student_id
        JOIN modules m ON mr.module_id = m.module_id
        WHERE mr.registration_status = 'active'
        ORDER BY mr.student_id, mr.module_id
    ''').fetchall()
    
    for reg in registrations:
        print(f"   Student {reg['student_id']} ({reg['student_number']}) -> Module {reg['module_id']} ({reg['module_code']})")
    
    # Test specific enrollment check for student 1 and active sessions
    print("\n🔍 Testing enrollment for student ID 1:")
    student_id = 1
    
    for session in sessions:
        if session['is_active']:
            enrollment = conn.execute('''
                SELECT mr.registration_id
                FROM module_registrations mr
                WHERE mr.student_id = ? AND mr.module_id = ? AND mr.registration_status = 'active'
            ''', (student_id, session['module_id'])).fetchone()
            
            print(f"   Session {session['session_id']} (Module {session['module_id']}): {'✅ Enrolled' if enrollment else '❌ Not enrolled'}")
    
    # Check if there are any attendance records
    print("\n📝 Existing attendance records:")
    attendance_records = conn.execute('''
        SELECT sa.attendance_id, sa.session_id, sa.student_id, sa.attendance_time,
               s.student_number, as_.session_name
        FROM student_attendance sa
        JOIN students s ON sa.student_id = s.student_id
        JOIN attendance_sessions as_ ON sa.session_id = as_.session_id
        ORDER BY sa.attendance_time DESC
        LIMIT 10
    ''').fetchall()
    
    if attendance_records:
        for record in attendance_records:
            print(f"   {record['student_number']} attended {record['session_name']} at {record['attendance_time']}")
    else:
        print("   No attendance records found")
    
    conn.close()

if __name__ == "__main__":
    debug_attendance_issue()
