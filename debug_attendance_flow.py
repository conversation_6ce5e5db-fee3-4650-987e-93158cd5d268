import sqlite3
from datetime import datetime

def debug_attendance_flow():
    """Debug the complete attendance marking flow"""
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    print("🔍 Debugging complete attendance marking flow...")
    
    # Test parameters
    session_id = 2
    current_user_id = 10001  # This is what <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> would have for student 1
    student_id = current_user_id - 10000  # Convert to database student_id
    
    print(f"📊 Test Parameters:")
    print(f"   Session ID: {session_id}")
    print(f"   Current User ID (Flask-Login): {current_user_id}")
    print(f"   Student ID (Database): {student_id}")
    
    # Step 1: Check if session exists and is active
    print(f"\n🔍 Step 1: Session validation")
    session_data = conn.execute('''
        SELECT as_.*, m.module_code, m.module_name
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        WHERE as_.session_id = ? AND as_.is_active = 1
    ''', (session_id,)).fetchone()
    
    if session_data:
        print(f"✅ Session found: {session_data['session_name']}")
        print(f"   Module: {session_data['module_code']} - {session_data['module_name']}")
        print(f"   Module ID: {session_data['module_id']}")
        print(f"   Lecturer ID: {session_data['lecturer_id']}")
        print(f"   Is Active: {session_data['is_active']}")
    else:
        print("❌ Session not found or not active")
        conn.close()
        return
    
    # Step 2: Check attendance window
    print(f"\n🔍 Step 2: Attendance window validation")
    try:
        if session_data['attendance_window_start'] and session_data['attendance_window_end']:
            window_start = datetime.fromisoformat(session_data['attendance_window_start'])
            window_end = datetime.fromisoformat(session_data['attendance_window_end'])
            now = datetime.now()
            
            print(f"   Window Start: {window_start}")
            print(f"   Window End: {window_end}")
            print(f"   Current Time: {now}")
            
            is_open = window_start <= now <= window_end
            print(f"   Window Open: {'✅ Yes' if is_open else '❌ No'}")
            
            if not is_open:
                print("❌ Attendance window is not currently open")
                conn.close()
                return
        else:
            print("❌ No attendance window set")
            conn.close()
            return
    except Exception as e:
        print(f"❌ Window parsing error: {e}")
        conn.close()
        return
    
    # Step 3: Check student enrollment - TEST MULTIPLE APPROACHES
    print(f"\n🔍 Step 3: Student enrollment validation")
    
    # Approach 1: Direct module_registrations check (current approach)
    print(f"   Approach 1: Direct module_registrations check")
    enrollment1 = conn.execute('''
        SELECT mr.registration_id, mr.student_id, mr.module_id, mr.registration_status
        FROM module_registrations mr
        WHERE mr.student_id = ? AND mr.module_id = ? AND mr.registration_status = 'active'
    ''', (student_id, session_data['module_id'])).fetchone()
    
    if enrollment1:
        print(f"   ✅ Found enrollment: Registration ID {enrollment1['registration_id']}")
        print(f"      Student ID: {enrollment1['student_id']}")
        print(f"      Module ID: {enrollment1['module_id']}")
        print(f"      Status: {enrollment1['registration_status']}")
    else:
        print(f"   ❌ No enrollment found")
    
    # Approach 2: Check with student_enrollments join (original approach)
    print(f"   Approach 2: With student_enrollments join")
    enrollment2 = conn.execute('''
        SELECT mr.registration_id, se.student_id, mr.module_id
        FROM module_registrations mr
        JOIN student_enrollments se ON mr.enrollment_id = se.enrollment_id
        WHERE se.student_id = ? AND mr.module_id = ? AND mr.registration_status = 'active'
    ''', (student_id, session_data['module_id'])).fetchone()
    
    if enrollment2:
        print(f"   ✅ Found enrollment with join: Registration ID {enrollment2['registration_id']}")
    else:
        print(f"   ❌ No enrollment found with join")
    
    # Approach 3: Show all enrollments for this student
    print(f"   Approach 3: All enrollments for student {student_id}")
    all_enrollments = conn.execute('''
        SELECT mr.registration_id, mr.student_id, mr.module_id, mr.registration_status,
               m.module_code, m.module_name
        FROM module_registrations mr
        JOIN modules m ON mr.module_id = m.module_id
        WHERE mr.student_id = ?
        ORDER BY mr.module_id
    ''', (student_id,)).fetchall()
    
    print(f"   Found {len(all_enrollments)} total enrollments:")
    for enr in all_enrollments:
        status_icon = "✅" if enr['registration_status'] == 'active' else "❌"
        module_match = "🎯" if enr['module_id'] == session_data['module_id'] else "  "
        print(f"   {status_icon} {module_match} Module {enr['module_id']} ({enr['module_code']}) - Status: {enr['registration_status']}")
    
    # Step 4: Check existing attendance
    print(f"\n🔍 Step 4: Existing attendance check")
    existing_attendance = conn.execute('''
        SELECT attendance_id, session_id, student_id, attendance_time
        FROM student_attendance
        WHERE session_id = ? AND student_id = ?
    ''', (session_id, student_id)).fetchone()
    
    if existing_attendance:
        print(f"❌ Attendance already marked at {existing_attendance['attendance_time']}")
    else:
        print(f"✅ No existing attendance found - can mark attendance")
    
    # Step 5: Summary and recommendation
    print(f"\n📋 Summary:")
    session_ok = session_data is not None
    window_ok = is_open if 'is_open' in locals() else False
    enrollment_ok = enrollment1 is not None
    no_existing = existing_attendance is None
    
    print(f"   Session Valid: {'✅' if session_ok else '❌'}")
    print(f"   Window Open: {'✅' if window_ok else '❌'}")
    print(f"   Student Enrolled: {'✅' if enrollment_ok else '❌'}")
    print(f"   No Existing Attendance: {'✅' if no_existing else '❌'}")
    
    can_mark = session_ok and window_ok and enrollment_ok and no_existing
    print(f"\n🎯 Can Mark Attendance: {'✅ YES' if can_mark else '❌ NO'}")
    
    if not can_mark:
        print(f"\n🔧 Issues to fix:")
        if not session_ok:
            print(f"   - Session not found or not active")
        if not window_ok:
            print(f"   - Attendance window not open")
        if not enrollment_ok:
            print(f"   - Student not enrolled in module")
        if not no_existing:
            print(f"   - Attendance already marked")
    
    conn.close()

if __name__ == "__main__":
    debug_attendance_flow()
