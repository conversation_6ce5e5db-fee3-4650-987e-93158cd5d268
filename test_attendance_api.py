import requests
import json
import base64
from PIL import Image
import io

def test_attendance_api():
    """Test the attendance marking API directly"""
    
    # Create a simple test image
    img = Image.new('RGB', (100, 100), color='red')
    img_buffer = io.BytesIO()
    img.save(img_buffer, format='JPEG')
    img_buffer.seek(0)
    
    # Convert to base64
    img_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
    image_data_url = f"data:image/jpeg;base64,{img_base64}"
    
    # Test data
    test_data = {
        'session_id': 2,  # Active session
        'image': image_data_url
    }
    
    print("🔍 Testing attendance API...")
    print(f"Session ID: {test_data['session_id']}")
    print(f"Image data length: {len(test_data['image'])}")
    
    try:
        # Make request to the API
        response = requests.post(
            'http://localhost:5000/api/mark_attendance',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"Response Text: {response.text}")
            
    except Exception as e:
        print(f"❌ Error making request: {e}")

def test_session_data():
    """Test session data directly from database"""
    import sqlite3
    from datetime import datetime
    
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    print("\n🔍 Testing session data...")
    
    # Test the exact query from the API
    session_id = 2
    session_data = conn.execute('''
        SELECT as_.*, m.module_code, m.module_name
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        WHERE as_.session_id = ? AND as_.is_active = 1
    ''', (session_id,)).fetchone()
    
    if session_data:
        print(f"✅ Session found: {session_data['session_name']}")
        print(f"   Module: {session_data['module_code']} - {session_data['module_name']}")
        print(f"   Module ID: {session_data['module_id']}")
        print(f"   Window start: {session_data['attendance_window_start']}")
        print(f"   Window end: {session_data['attendance_window_end']}")
        
        # Test window validation
        try:
            if session_data['attendance_window_start'] and session_data['attendance_window_end']:
                window_start = datetime.fromisoformat(session_data['attendance_window_start'])
                window_end = datetime.fromisoformat(session_data['attendance_window_end'])
                now = datetime.now()
                
                print(f"   Current time: {now}")
                print(f"   Window open: {window_start <= now <= window_end}")
            else:
                print("   ❌ No attendance window set")
        except Exception as e:
            print(f"   ❌ Window parsing error: {e}")
        
        # Test enrollment for different student IDs
        for student_id in [1, 2, 3]:
            enrollment = conn.execute('''
                SELECT mr.registration_id
                FROM module_registrations mr
                WHERE mr.student_id = ? AND mr.module_id = ? AND mr.registration_status = 'active'
            ''', (student_id, session_data['module_id'])).fetchone()
            
            print(f"   Student {student_id} enrollment: {'✅ Enrolled' if enrollment else '❌ Not enrolled'}")
    else:
        print("❌ Session not found")
    
    conn.close()

if __name__ == "__main__":
    test_session_data()
    print("\n" + "="*50)
    test_attendance_api()
