"""
Facial Recognition System for Attendance Tracking
Handles face detection, encoding, recognition, and database operations
"""

import cv2
import numpy as np
import face_recognition
import sqlite3
import hashlib
import pickle
import base64
from datetime import datetime
import logging
from typing import List, Dict, Tuple, Optional
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FacialRecognitionSystem:
    """
    Main facial recognition system for attendance tracking
    """
    
    def __init__(self, database_path: str = "attendance_new.db", confidence_threshold: float = 0.6):
        """
        Initialize the facial recognition system
        
        Args:
            database_path: Path to the SQLite database
            confidence_threshold: Minimum confidence for face recognition
        """
        self.database_path = database_path
        self.confidence_threshold = confidence_threshold
        self.known_face_encodings = []
        self.known_face_names = []
        self.known_student_ids = []
        
        # Load existing face encodings from database
        self.load_face_encodings()
        
        logger.info(f"Facial Recognition System initialized with {len(self.known_face_encodings)} known faces")
    
    def get_db_connection(self):
        """Get database connection"""
        conn = sqlite3.connect(self.database_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def capture_face_images(self, num_images: int = 5) -> List[np.ndarray]:
        """
        Capture multiple face images from webcam for enrollment
        
        Args:
            num_images: Number of images to capture
            
        Returns:
            List of captured face images
        """
        cap = cv2.VideoCapture(0)
        captured_images = []
        
        if not cap.isOpened():
            logger.error("Could not open webcam")
            return []
        
        print(f"Capturing {num_images} images. Press SPACE to capture, ESC to cancel.")
        
        while len(captured_images) < num_images:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Flip frame horizontally for mirror effect
            frame = cv2.flip(frame, 1)
            
            # Detect faces in the frame
            face_locations = face_recognition.face_locations(frame)
            
            # Draw rectangles around faces
            for (top, right, bottom, left) in face_locations:
                cv2.rectangle(frame, (left, top), (right, bottom), (0, 255, 0), 2)
            
            # Display instructions
            cv2.putText(frame, f"Captured: {len(captured_images)}/{num_images}", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, "Press SPACE to capture", 
                       (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, "Press ESC to cancel", 
                       (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            cv2.imshow('Face Capture', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord(' '):  # Space key
                if len(face_locations) > 0:
                    captured_images.append(frame.copy())
                    print(f"Captured image {len(captured_images)}")
                else:
                    print("No face detected. Please position your face in the frame.")
            elif key == 27:  # ESC key
                break
        
        cap.release()
        cv2.destroyAllWindows()
        
        return captured_images
    
    def extract_face_encodings(self, images: List[np.ndarray]) -> List[np.ndarray]:
        """
        Extract face encodings from captured images
        
        Args:
            images: List of face images
            
        Returns:
            List of face encodings
        """
        encodings = []
        
        for i, image in enumerate(images):
            # Convert BGR to RGB
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Find face locations
            face_locations = face_recognition.face_locations(rgb_image)
            
            if len(face_locations) > 0:
                # Get face encodings
                face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
                
                if len(face_encodings) > 0:
                    encodings.append(face_encodings[0])
                    logger.info(f"Extracted encoding from image {i+1}")
                else:
                    logger.warning(f"No face encoding found in image {i+1}")
            else:
                logger.warning(f"No face detected in image {i+1}")
        
        return encodings
    
    def enroll_student_face(self, student_id: int, images: List[np.ndarray]) -> bool:
        """
        Enroll a student's face in the system
        
        Args:
            student_id: Student ID
            images: List of face images
            
        Returns:
            True if enrollment successful, False otherwise
        """
        try:
            # Extract face encodings
            encodings = self.extract_face_encodings(images)
            
            if len(encodings) == 0:
                logger.error("No valid face encodings found")
                return False
            
            # Calculate average encoding for better accuracy
            avg_encoding = np.mean(encodings, axis=0)
            
            # Serialize encoding
            encoding_blob = pickle.dumps(avg_encoding)
            encoding_hash = hashlib.sha256(encoding_blob).hexdigest()
            
            # Store in database
            conn = self.get_db_connection()
            
            # Check if student already has face encoding
            existing = conn.execute(
                "SELECT embedding_id FROM facial_embeddings WHERE student_id = ? AND is_active = 1",
                (student_id,)
            ).fetchone()
            
            if existing:
                # Update existing encoding
                conn.execute("""
                    UPDATE facial_embeddings 
                    SET embedding_vector = ?, embedding_hash = ?, model_version = ?, 
                        confidence_threshold = ?, created_at = ?
                    WHERE student_id = ? AND is_active = 1
                """, (encoding_blob, encoding_hash, "face_recognition_v1.3.0", 
                      self.confidence_threshold, datetime.now(), student_id))
                logger.info(f"Updated face encoding for student {student_id}")
            else:
                # Insert new encoding
                conn.execute("""
                    INSERT INTO facial_embeddings (
                        student_id, embedding_vector, embedding_hash, model_version,
                        confidence_threshold, is_primary, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (student_id, encoding_blob, encoding_hash, "face_recognition_v1.3.0",
                      self.confidence_threshold, True, None))
                logger.info(f"Enrolled new face encoding for student {student_id}")
            
            # Update student record
            conn.execute(
                "UPDATE students SET is_facial_registered = 1 WHERE student_id = ?",
                (student_id,)
            )
            
            conn.commit()
            conn.close()
            
            # Reload face encodings
            self.load_face_encodings()
            
            return True
            
        except Exception as e:
            logger.error(f"Error enrolling student face: {e}")
            return False
    
    def load_face_encodings(self):
        """Load all face encodings from database"""
        try:
            conn = self.get_db_connection()
            
            # Get all active face encodings with student info
            cursor = conn.execute("""
                SELECT fe.embedding_vector, s.student_id, s.first_name, s.last_name, s.student_number
                FROM facial_embeddings fe
                JOIN students s ON fe.student_id = s.student_id
                WHERE fe.is_active = 1 AND s.is_active = 1
            """)
            
            self.known_face_encodings = []
            self.known_face_names = []
            self.known_student_ids = []
            
            for row in cursor.fetchall():
                # Deserialize encoding
                encoding = pickle.loads(row['embedding_vector'])
                self.known_face_encodings.append(encoding)
                
                # Store student info
                name = f"{row['first_name']} {row['last_name']}"
                self.known_face_names.append(name)
                self.known_student_ids.append(row['student_id'])
            
            conn.close()
            logger.info(f"Loaded {len(self.known_face_encodings)} face encodings")
            
        except Exception as e:
            logger.error(f"Error loading face encodings: {e}")
    
    def recognize_face(self, image: np.ndarray) -> Tuple[Optional[int], Optional[str], float]:
        """
        Recognize a face in the given image
        
        Args:
            image: Input image
            
        Returns:
            Tuple of (student_id, name, confidence) or (None, None, 0.0) if no match
        """
        try:
            # Convert BGR to RGB
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Find face locations and encodings
            face_locations = face_recognition.face_locations(rgb_image)
            face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
            
            if len(face_encodings) == 0:
                return None, None, 0.0
            
            # Compare with known faces
            for face_encoding in face_encodings:
                # Calculate distances to all known faces
                face_distances = face_recognition.face_distance(self.known_face_encodings, face_encoding)
                
                if len(face_distances) > 0:
                    # Find best match
                    best_match_index = np.argmin(face_distances)
                    confidence = 1.0 - face_distances[best_match_index]
                    
                    if confidence >= self.confidence_threshold:
                        student_id = self.known_student_ids[best_match_index]
                        name = self.known_face_names[best_match_index]
                        return student_id, name, confidence
            
            return None, None, 0.0
            
        except Exception as e:
            logger.error(f"Error recognizing face: {e}")
            return None, None, 0.0
    
    def record_attendance(self, student_id: int, session_id: int, confidence: float, 
                         device_info: str = "webcam", ip_address: str = "127.0.0.1") -> bool:
        """
        Record attendance for a student
        
        Args:
            student_id: Student ID
            session_id: Class session ID
            confidence: Recognition confidence
            device_info: Device used for recognition
            ip_address: IP address of the request
            
        Returns:
            True if attendance recorded successfully
        """
        try:
            conn = self.get_db_connection()
            
            # Check if attendance already recorded
            existing = conn.execute(
                "SELECT attendance_id FROM attendance_records WHERE session_id = ? AND student_id = ?",
                (session_id, student_id)
            ).fetchone()
            
            if existing:
                logger.warning(f"Attendance already recorded for student {student_id} in session {session_id}")
                conn.close()
                return False
            
            # Generate record hash for integrity
            record_data = {
                'session_id': session_id,
                'student_id': student_id,
                'confidence': confidence,
                'timestamp': datetime.now().isoformat(),
                'method': 'facial'
            }
            record_hash = hashlib.sha256(str(record_data).encode()).hexdigest()
            
            # Insert attendance record
            conn.execute("""
                INSERT INTO attendance_records (
                    session_id, student_id, recognition_confidence, attendance_timestamp,
                    recognition_method, device_info, ip_address, record_hash
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (session_id, student_id, confidence, datetime.now(),
                  'facial', device_info, ip_address, record_hash))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Recorded attendance for student {student_id} with confidence {confidence:.2f}")
            return True
            
        except Exception as e:
            logger.error(f"Error recording attendance: {e}")
            return False

# Example usage and testing functions
def test_facial_recognition():
    """Test the facial recognition system"""
    fr_system = FacialRecognitionSystem()
    
    print("Facial Recognition System Test")
    print("1. Capture face images")
    print("2. Test recognition")
    print("3. Exit")
    
    while True:
        choice = input("Enter choice (1-3): ")
        
        if choice == '1':
            student_id = int(input("Enter student ID: "))
            images = fr_system.capture_face_images(5)
            
            if len(images) > 0:
                success = fr_system.enroll_student_face(student_id, images)
                if success:
                    print("Face enrollment successful!")
                else:
                    print("Face enrollment failed!")
            else:
                print("No images captured!")
        
        elif choice == '2':
            print("Starting face recognition test...")
            cap = cv2.VideoCapture(0)
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                frame = cv2.flip(frame, 1)
                student_id, name, confidence = fr_system.recognize_face(frame)
                
                if student_id:
                    cv2.putText(frame, f"ID: {student_id}", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                    cv2.putText(frame, f"Name: {name}", (10, 70), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                    cv2.putText(frame, f"Confidence: {confidence:.2f}", (10, 110), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                else:
                    cv2.putText(frame, "No match found", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                
                cv2.imshow('Face Recognition Test', frame)
                
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
            
            cap.release()
            cv2.destroyAllWindows()
        
        elif choice == '3':
            break
        else:
            print("Invalid choice!")

if __name__ == "__main__":
    test_facial_recognition()
