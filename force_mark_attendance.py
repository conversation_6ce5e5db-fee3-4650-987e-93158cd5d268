import sqlite3
from datetime import datetime

def force_mark_attendance():
    """Force mark attendance for student 1 in session 2"""
    conn = sqlite3.connect('attendance_fixed.db')
    
    # Parameters
    session_id = 2
    student_id = 1
    confidence = 0.95
    attendance_method = 'manual_override'
    
    print(f"🔧 Force marking attendance...")
    print(f"   Session ID: {session_id}")
    print(f"   Student ID: {student_id}")
    
    # Check if already marked
    existing = conn.execute('''
        SELECT attendance_id FROM student_attendance
        WHERE session_id = ? AND student_id = ?
    ''', (session_id, student_id)).fetchone()
    
    if existing:
        print(f"❌ Attendance already marked (ID: {existing[0]})")
        conn.close()
        return
    
    # Mark attendance directly
    conn.execute('''
        INSERT INTO student_attendance (
            session_id, student_id, confidence_score, attendance_method, attendance_time
        ) VALUES (?, ?, ?, ?, ?)
    ''', (session_id, student_id, confidence, attendance_method, datetime.now().isoformat()))
    
    conn.commit()
    
    # Verify
    result = conn.execute('''
        SELECT sa.attendance_id, sa.attendance_time, sa.confidence_score,
               as_.session_name, s.first_name, s.last_name
        FROM student_attendance sa
        JOIN attendance_sessions as_ ON sa.session_id = as_.session_id
        JOIN students s ON sa.student_id = s.student_id
        WHERE sa.session_id = ? AND sa.student_id = ?
    ''', (session_id, student_id)).fetchone()
    
    if result:
        print(f"✅ Attendance marked successfully!")
        print(f"   Attendance ID: {result[0]}")
        print(f"   Time: {result[1]}")
        print(f"   Student: {result[4]} {result[5]}")
        print(f"   Session: {result[3]}")
        print(f"   Confidence: {result[2]}")
    else:
        print(f"❌ Failed to mark attendance")
    
    conn.close()

if __name__ == "__main__":
    force_mark_attendance()
