import sqlite3
import hashlib

def reset_bhavek_password():
    conn = sqlite3.connect('attendance_fixed.db')
    
    # Set a known password
    new_password = "bhavek123"
    password_hash = hashlib.sha256(new_password.encode()).hexdigest()
    
    print(f"🔧 Resetting <PERSON><PERSON><PERSON>'s password to: {new_password}")
    print(f"   New hash: {password_hash[:20]}...")
    
    # Update the password
    conn.execute('''
        UPDATE users 
        SET password_hash = ?
        WHERE username = ?
    ''', (password_hash, 'Bhavek'))
    
    conn.commit()
    
    # Verify the update
    user = conn.execute('''
        SELECT username, password_hash, is_active, role_id
        FROM users 
        WHERE username = ?
    ''', ('Bhavek',)).fetchone()
    
    if user:
        print(f"✅ Password updated successfully")
        print(f"   - Username: {user[0]}")
        print(f"   - New hash: {user[1][:20]}...")
        print(f"   - Is Active: {user[2]}")
        print(f"   - Role ID: {user[3]}")
        
        # Test the new password
        test_hash = hashlib.sha256(new_password.encode()).hexdigest()
        if test_hash == user[1]:
            print(f"✅ Password verification successful!")
            print(f"\n🎯 Login credentials:")
            print(f"   Username: Bhavek")
            print(f"   Password: {new_password}")
        else:
            print("❌ Password verification failed")
    else:
        print("❌ User not found after update")
    
    conn.close()

if __name__ == "__main__":
    reset_bhavek_password()
