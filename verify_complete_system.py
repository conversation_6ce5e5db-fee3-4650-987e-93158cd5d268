#!/usr/bin/env python3
"""
Complete System Verification Script
Verifies that all components work correctly with the current database
"""

import sqlite3
import os
import sys
import requests
import time
from datetime import datetime

def check_database():
    """Check database structure and data"""
    print("🗄️  Checking Database...")
    
    db_name = 'attendance_fixed_20250803_114853.db'
    if not os.path.exists(db_name):
        print(f"❌ Database file {db_name} not found!")
        return False

    try:
        conn = sqlite3.connect(db_name)
        conn.row_factory = sqlite3.Row
        
        # Check key tables
        tables = [
            'institutions', 'faculties', 'departments', 'user_roles', 'users', 'students',
            'courses', 'course_years', 'modules', 'student_enrollments', 'module_registrations',
            'student_face_encodings', 'module_lecturers', 'attendance_sessions', 
            'student_attendance', 'attendance_marks'
        ]
        
        for table in tables:
            try:
                count = conn.execute(f"SELECT COUNT(*) as count FROM {table}").fetchone()['count']
                print(f"   ✅ {table}: {count} records")
            except Exception as e:
                print(f"   ❌ {table}: Error - {e}")
                return False
        
        # Check admin user
        admin_user = conn.execute("""
            SELECT u.username, ur.role_name 
            FROM users u 
            JOIN user_roles ur ON u.role_id = ur.role_id 
            WHERE ur.role_name IN ('admin', 'super_admin')
            LIMIT 1
        """).fetchone()
        
        if admin_user:
            print(f"   ✅ Admin user found: {admin_user['username']} ({admin_user['role_name']})")
        else:
            print("   ❌ No admin user found!")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")
        return False

def check_flask_app():
    """Check if Flask app is running"""
    print("\n🌐 Checking Flask Application...")
    
    try:
        response = requests.get('http://127.0.0.1:5000', timeout=5)
        if response.status_code == 200:
            print("   ✅ Flask app is running and responding")
            return True
        else:
            print(f"   ❌ Flask app returned status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("   ❌ Flask app is not running or not accessible")
        return False
    except Exception as e:
        print(f"   ❌ Error checking Flask app: {e}")
        return False

def check_admin_interface():
    """Check admin interface functionality"""
    print("\n👤 Checking Admin Interface...")
    
    try:
        # Check admin dashboard
        response = requests.get('http://127.0.0.1:5000/admin', timeout=5)
        if response.status_code == 200:
            print("   ✅ Admin dashboard accessible")
        else:
            print(f"   ⚠️  Admin dashboard returned status: {response.status_code}")
        
        # Check admin tables
        response = requests.get('http://127.0.0.1:5000/admin/tables', timeout=5)
        if response.status_code == 200:
            print("   ✅ Admin tables page accessible")
        else:
            print(f"   ⚠️  Admin tables returned status: {response.status_code}")
        
        # Check specific table view
        response = requests.get('http://127.0.0.1:5000/admin/table/students', timeout=5)
        if response.status_code == 200:
            print("   ✅ Table view functionality working")
        else:
            print(f"   ⚠️  Table view returned status: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error checking admin interface: {e}")
        return False

def check_student_interface():
    """Check student interface functionality"""
    print("\n🎓 Checking Student Interface...")
    
    try:
        # Check student portal
        response = requests.get('http://127.0.0.1:5000/choose_portal', timeout=5)
        if response.status_code == 200:
            print("   ✅ Portal selection page accessible")
        else:
            print(f"   ⚠️  Portal selection returned status: {response.status_code}")
        
        # Check student registration
        response = requests.get('http://127.0.0.1:5000/student/register', timeout=5)
        if response.status_code == 200:
            print("   ✅ Student registration page accessible")
        else:
            print(f"   ⚠️  Student registration returned status: {response.status_code}")
        
        # Check student login
        response = requests.get('http://127.0.0.1:5000/student/login', timeout=5)
        if response.status_code == 200:
            print("   ✅ Student login page accessible")
        else:
            print(f"   ⚠️  Student login returned status: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error checking student interface: {e}")
        return False

def check_staff_interface():
    """Check staff interface functionality"""
    print("\n👨‍🏫 Checking Staff Interface...")
    
    try:
        # Check staff login
        response = requests.get('http://127.0.0.1:5000/staff/login', timeout=5)
        if response.status_code == 200:
            print("   ✅ Staff login page accessible")
        else:
            print(f"   ⚠️  Staff login returned status: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error checking staff interface: {e}")
        return False

def check_api_endpoints():
    """Check API endpoints"""
    print("\n🔌 Checking API Endpoints...")
    
    try:
        # Check OpenCV status
        response = requests.get('http://127.0.0.1:5000/api/opencv_status', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ OpenCV status API: {data.get('status', 'unknown')}")
        else:
            print(f"   ⚠️  OpenCV status API returned status: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error checking API endpoints: {e}")
        return False

def main():
    """Main verification function"""
    print("🔍 Complete System Verification")
    print("=" * 50)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    checks = [
        ("Database Structure", check_database),
        ("Flask Application", check_flask_app),
        ("Admin Interface", check_admin_interface),
        ("Student Interface", check_student_interface),
        ("Staff Interface", check_staff_interface),
        ("API Endpoints", check_api_endpoints)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"   ❌ {check_name} check failed with exception: {e}")
            results.append((check_name, False))
    
    print("\n" + "=" * 50)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {check_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 ALL SYSTEMS OPERATIONAL!")
        print("✅ The Facial Recognition Attendance System is fully functional")
        print("🚀 Ready for use at: http://127.0.0.1:5000")
        print("\n📋 Quick Access:")
        print("   • Main Portal: http://127.0.0.1:5000")
        print("   • Admin Panel: http://127.0.0.1:5000/admin")
        print("   • Staff Login: http://127.0.0.1:5000/staff/login")
        print("   • Student Portal: http://127.0.0.1:5000/choose_portal")
        return True
    else:
        print(f"\n⚠️  {total - passed} issues found - system may have limited functionality")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
