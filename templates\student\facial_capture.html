{% extends "base.html" %}

{% block title %}Facial Capture - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-lg border-0">
                <div class="card-header text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-camera me-2"></i>
                        Facial Recognition Capture
                    </h2>
                    <p class="mb-0 mt-2">Position your face in the camera and capture your biometric data</p>
                </div>
                <div class="card-body p-5">
                    <div class="row">
                        <!-- Camera Section -->
                        <div class="col-md-8">
                            <div class="text-center">
                                <div class="camera-container mb-4">
                                    <video id="video" width="640" height="480" autoplay style="border: 3px solid #007bff; border-radius: 10px; background: #000;"></video>
                                    <canvas id="canvas" width="640" height="480" style="display: none;"></canvas>

                                    <!-- Face outline guide -->
                                    <div class="face-guide-overlay">
                                        <div class="face-outline">
                                            <div class="face-guide-text">Position your face here</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="camera-controls">
                                    <button id="startCamera" class="btn btn-primary btn-lg me-3">
                                        <i class="fas fa-video me-2"></i>
                                        Start Camera
                                    </button>
                                    <button id="capturePhoto" class="btn btn-success btn-lg me-3" disabled>
                                        <i class="fas fa-camera me-2"></i>
                                        Capture Photo
                                    </button>
                                    <button id="retakePhoto" class="btn btn-warning btn-lg" style="display: none;">
                                        <i class="fas fa-redo me-2"></i>
                                        Retake
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Instructions Section -->
                        <div class="col-md-4">
                            <div class="instructions">
                                <h5 class="mb-3">
                                    <i class="fas fa-info-circle me-2 text-info"></i>
                                    Instructions
                                </h5>
                                
                                <div class="alert alert-light">
                                    <ol class="mb-0">
                                        <li class="mb-2">Click "Start Camera" to begin</li>
                                        <li class="mb-2">Position your face in the center</li>
                                        <li class="mb-2">Ensure good lighting</li>
                                        <li class="mb-2">Look directly at the camera</li>
                                        <li class="mb-2">Click "Capture Photo" when ready</li>
                                    </ol>
                                </div>
                                
                                <div class="status-section mt-4">
                                    <h6 class="mb-2">
                                        <i class="fas fa-check-circle me-2 text-success"></i>
                                        Requirements
                                    </h6>
                                    <ul class="list-unstyled small">
                                        <li id="faceDetected" class="mb-1">
                                            <i class="fas fa-circle text-muted me-2"></i>
                                            Face detected
                                        </li>
                                        <li id="singleFace" class="mb-1">
                                            <i class="fas fa-circle text-muted me-2"></i>
                                            Single face only
                                        </li>
                                        <li id="goodLighting" class="mb-1">
                                            <i class="fas fa-circle text-muted me-2"></i>
                                            Good lighting
                                        </li>
                                    </ul>
                                </div>
                                
                                <div id="captureStatus" class="mt-4" style="display: none;">
                                    <div class="alert alert-info">
                                        <i class="fas fa-spinner fa-spin me-2"></i>
                                        Processing facial data...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            <button id="saveCapture" class="btn btn-success btn-lg me-3" style="display: none;">
                                <i class="fas fa-save me-2"></i>
                                Save Facial Data
                            </button>
                            <a href="{{ url_for('student_facial_setup') }}" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Setup
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    Facial Data Captured Successfully!
                </h5>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-user-check text-success" style="font-size: 4rem;"></i>
                <h4 class="mt-3">Setup Complete!</h4>
                <p>Your facial recognition data has been securely stored. You can now use the automated attendance system.</p>
            </div>
            <div class="modal-footer">
                <a href="{{ url_for('student_dashboard') }}" class="btn btn-primary">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Go to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<script>
let video = document.getElementById('video');
let canvas = document.getElementById('canvas');
let context = canvas.getContext('2d');
let capturedImageData = null;

// Camera controls
document.getElementById('startCamera').addEventListener('click', startCamera);
document.getElementById('capturePhoto').addEventListener('click', capturePhoto);
document.getElementById('retakePhoto').addEventListener('click', retakePhoto);
document.getElementById('saveCapture').addEventListener('click', saveFacialData);

async function startCamera() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
            video: { 
                width: 640, 
                height: 480,
                facingMode: 'user'
            } 
        });
        
        video.srcObject = stream;
        document.getElementById('startCamera').disabled = true;
        document.getElementById('capturePhoto').disabled = false;
        
        // Update status
        updateStatus('faceDetected', true);

        // Show face guide
        const faceGuide = document.querySelector('.face-guide-overlay');
        if (faceGuide) {
            faceGuide.style.display = 'flex';
        }
        
    } catch (err) {
        console.error('Error accessing camera:', err);
        alert('Could not access camera. Please ensure camera permissions are granted.');
    }
}

function capturePhoto() {
    // Flip the image back to normal orientation for processing
    context.save();
    context.scale(-1, 1);
    context.drawImage(video, -640, 0, 640, 480);
    context.restore();

    capturedImageData = canvas.toDataURL('image/jpeg', 0.8);

    // Show captured image (also flipped for consistency)
    video.style.display = 'none';
    canvas.style.display = 'block';
    canvas.style.transform = 'scaleX(-1)'; // Flip canvas display to match video

    // Hide face guide overlay
    const faceGuide = document.querySelector('.face-guide-overlay');
    if (faceGuide) {
        faceGuide.style.display = 'none';
    }

    // Update buttons
    document.getElementById('capturePhoto').style.display = 'none';
    document.getElementById('retakePhoto').style.display = 'inline-block';
    document.getElementById('saveCapture').style.display = 'inline-block';

    // Update status
    updateStatus('singleFace', true);
    updateStatus('goodLighting', true);
}

function retakePhoto() {
    video.style.display = 'block';
    canvas.style.display = 'none';
    canvas.style.transform = ''; // Reset canvas transform

    // Show face guide overlay again
    const faceGuide = document.querySelector('.face-guide-overlay');
    if (faceGuide) {
        faceGuide.style.display = 'flex';
    }

    // Update buttons
    document.getElementById('capturePhoto').style.display = 'inline-block';
    document.getElementById('retakePhoto').style.display = 'none';
    document.getElementById('saveCapture').style.display = 'none';

    capturedImageData = null;
}

async function saveFacialData() {
    if (!capturedImageData) {
        alert('Please capture a photo first.');
        return;
    }
    
    // Show processing status
    document.getElementById('captureStatus').style.display = 'block';
    document.getElementById('saveCapture').disabled = true;
    
    try {
        const response = await fetch('/api/capture_face', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                image: capturedImageData
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Show success modal
            const modal = new bootstrap.Modal(document.getElementById('successModal'));
            modal.show();
        } else {
            alert('Error: ' + result.error);
        }
        
    } catch (error) {
        console.error('Error saving facial data:', error);
        alert('Failed to save facial data. Please try again.');
    } finally {
        document.getElementById('captureStatus').style.display = 'none';
        document.getElementById('saveCapture').disabled = false;
    }
}

function updateStatus(elementId, success) {
    const element = document.getElementById(elementId);
    const icon = element.querySelector('i');

    if (success) {
        icon.className = 'fas fa-check-circle text-success me-2';

        // Update face guide color when face is detected
        if (elementId === 'faceDetected') {
            updateFaceGuideStatus(true);
        }
    } else {
        icon.className = 'fas fa-circle text-muted me-2';

        if (elementId === 'faceDetected') {
            updateFaceGuideStatus(false);
        }
    }
}

function updateFaceGuideStatus(faceDetected) {
    const faceOutline = document.querySelector('.face-outline');
    const faceText = document.querySelector('.face-guide-text');

    if (faceOutline && faceText) {
        if (faceDetected) {
            faceOutline.style.borderColor = 'rgba(40, 167, 69, 0.8)';
            faceOutline.style.background = 'rgba(40, 167, 69, 0.1)';
            faceText.style.color = 'rgba(40, 167, 69, 0.9)';
            faceText.textContent = 'Face detected - Ready to capture!';
        } else {
            faceOutline.style.borderColor = 'rgba(0, 255, 0, 0.8)';
            faceOutline.style.background = 'rgba(0, 255, 0, 0.1)';
            faceText.style.color = 'rgba(0, 255, 0, 0.9)';
            faceText.textContent = 'Position your face here';
        }
    }
}

// Cleanup camera stream when leaving page
window.addEventListener('beforeunload', function() {
    if (video.srcObject) {
        video.srcObject.getTracks().forEach(track => track.stop());
    }
});
</script>

<style>
.camera-container {
    position: relative;
    display: inline-block;
}

.face-guide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.face-outline {
    width: 280px;
    height: 350px;
    border: 3px solid rgba(0, 255, 0, 0.8);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    position: relative;
    background: rgba(0, 255, 0, 0.1);
    animation: pulse-guide 2s ease-in-out infinite;
}

.face-guide-text {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(0, 255, 0, 0.9);
    font-size: 14px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
    white-space: nowrap;
}

@keyframes pulse-guide {
    0% {
        border-color: rgba(0, 255, 0, 0.8);
        background: rgba(0, 255, 0, 0.1);
    }
    50% {
        border-color: rgba(0, 255, 0, 1);
        background: rgba(0, 255, 0, 0.2);
    }
    100% {
        border-color: rgba(0, 255, 0, 0.8);
        background: rgba(0, 255, 0, 0.1);
    }
}

.instructions {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    height: fit-content;
}

.status-section {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
}

#video, #canvas {
    max-width: 100%;
    height: auto;
    transform: scaleX(-1); /* Flip the video horizontally to mirror the user */
}

@media (max-width: 768px) {
    #video, #canvas {
        width: 100%;
        height: auto;
    }
    
    .camera-controls .btn {
        margin-bottom: 10px;
    }
}
</style>
{% endblock %}
