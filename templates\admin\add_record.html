{% extends "base.html" %}

{% block title %}Add Record - {{ table_name.replace('_', ' ').title() }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-plus me-2 text-success"></i>
                        Add New Record
                    </h1>
                    <p class="text-muted mb-0">{{ table_name.replace('_', ' ').title() }}</p>
                </div>
                <div>
                    <a href="{{ url_for('admin_view_table', table_name=table_name) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Table
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit me-2"></i>
                        Record Details
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            {% for col in schema %}
                                {% if not col['pk'] or not 'AUTOINCREMENT' in col['type']|string %}
                                    {% if col['name'] not in ['created_at', 'updated_at'] %}
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ col['name'] }}" class="form-label">
                                            {{ col['name'].replace('_', ' ').title() }}
                                            {% if col['notnull'] and not col['dflt_value'] %}
                                                <span class="text-danger">*</span>
                                            {% endif %}
                                        </label>
                                        
                                        {% if col['name'] in foreign_keys %}
                                            <!-- Foreign Key Dropdown -->
                                            <select class="form-select" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                                    {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                                <option value="">Select {{ foreign_keys[col['name']]['table'].replace('_', ' ').title() }}</option>
                                                {% for option in foreign_keys[col['name']]['options'] %}
                                                <option value="{{ option[0] if option|length > 0 else '' }}">
                                                    {% if option|length > 1 and option[1] %}
                                                        {{ option[1] }}
                                                    {% elif option|length > 0 %}
                                                        {{ option[0] }}
                                                    {% else %}
                                                        Unknown
                                                    {% endif %}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        {% elif col['name'] == 'password_hash' %}
                                            <!-- Password Field -->
                                            <input type="password" class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                                   placeholder="Enter password"
                                                   {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                            <small class="form-text text-muted">Password will be automatically hashed</small>
                                        {% elif col['name'] == 'email' %}
                                            <!-- Email Field -->
                                            <input type="email" class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                                   placeholder="Enter email address"
                                                   {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                        {% elif col['name'].endswith('_date') or col['name'].endswith('_at') %}
                                            <!-- Date/DateTime Field -->
                                            <input type="datetime-local" class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                                   {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                        {% elif col['type']|string|upper == 'INTEGER' %}
                                            <!-- Integer Field -->
                                            <input type="number" class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                                   {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                        {% elif col['type']|string|upper == 'REAL' %}
                                            <!-- Float Field -->
                                            <input type="number" step="0.01" class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                                   {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                        {% elif col['type']|string|upper == 'BOOLEAN' %}
                                            <!-- Boolean Field -->
                                            <select class="form-select" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                                    {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                                <option value="">Select...</option>
                                                <option value="1">True</option>
                                                <option value="0">False</option>
                                            </select>
                                        {% elif col['name'] in ['description', 'notes', 'address'] %}
                                            <!-- Textarea for long text -->
                                            <textarea class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}" rows="3"
                                                      {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}></textarea>
                                        {% else %}
                                            <!-- Text Field -->
                                            <input type="text" class="form-control" id="{{ col['name'] }}" name="{{ col['name'] }}"
                                                   {% if col['notnull'] and not col['dflt_value'] %}required{% endif %}>
                                        {% endif %}
                                        
                                        {% if col['dflt_value'] %}
                                        <small class="form-text text-muted">Default: {{ col['dflt_value'] }}</small>
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('admin_view_table', table_name=table_name) }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>
                                        Add Record
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="m-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Field Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Required Fields</h6>
                            <ul class="list-unstyled">
                                {% for col in schema %}
                                    {% if col['notnull'] and not col['dflt_value'] and col['name'] not in ['created_at', 'updated_at'] %}
                                    <li><i class="fas fa-asterisk text-danger me-2" style="font-size: 0.7em;"></i>{{ col['name'].replace('_', ' ').title() }}</li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">Auto-Generated Fields</h6>
                            <ul class="list-unstyled">
                                {% for col in schema %}
                                    {% if col['pk'] and 'AUTOINCREMENT' in col['type']|string %}
                                    <li><i class="fas fa-magic text-success me-2"></i>{{ col['name'].replace('_', ' ').title() }} (Auto ID)</li>
                                    {% endif %}
                                    {% if col['name'] in ['created_at', 'updated_at'] %}
                                    <li><i class="fas fa-clock text-success me-2"></i>{{ col['name'].replace('_', ' ').title() }} (Auto Timestamp)</li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-populate some fields based on table type
document.addEventListener('DOMContentLoaded', function() {
    const tableName = '{{ table_name }}';
    
    // Set default values for specific tables
    if (tableName === 'users') {
        const roleSelect = document.getElementById('role_id');
        if (roleSelect) {
            // Default to lecturer role if available
            for (let option of roleSelect.options) {
                if (option.text.toLowerCase().includes('lecturer')) {
                    option.selected = true;
                    break;
                }
            }
        }
    }
    
    // Auto-generate student numbers for students table
    if (tableName === 'students') {
        const studentNumberField = document.getElementById('student_number');
        if (studentNumberField && !studentNumberField.value) {
            const year = new Date().getFullYear();
            const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
            studentNumberField.value = `${year}${random}`;
        }
    }
});
</script>
{% endblock %}
