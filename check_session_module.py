#!/usr/bin/env python3
"""
Check what module the active session is for
"""

import sqlite3

def check_session_module():
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    print("🔍 Checking session module...")
    
    # Get the active session details
    session = conn.execute("""
        SELECT as_.session_id, as_.module_id, as_.session_name, as_.is_active,
               m.module_code, m.module_name
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        WHERE as_.is_active = 1
        ORDER BY as_.session_id DESC
        LIMIT 1
    """).fetchone()
    
    if session:
        print(f"📍 Active session:")
        print(f"   Session ID: {session['session_id']}")
        print(f"   Module ID: {session['module_id']}")
        print(f"   Module: {session['module_code']} - {session['module_name']}")
        print(f"   Session Name: {session['session_name']}")
        
        # Check if student 1 is registered for this module
        student_id = 1
        registration = conn.execute("""
            SELECT mr.registration_id, mr.student_id, mr.module_id, mr.registration_status
            FROM module_registrations mr
            WHERE mr.student_id = ? AND mr.module_id = ?
        """, (student_id, session['module_id'])).fetchone()
        
        print(f"\n🎓 Student {student_id} registration for module {session['module_id']}:")
        if registration:
            print(f"   Registration ID: {registration['registration_id']}")
            print(f"   Status: {registration['registration_status']}")
            print(f"   ✅ Student IS registered for this module")
        else:
            print(f"   ❌ Student is NOT registered for this module")
            
        # Show all modules student is registered for
        print(f"\n📚 All modules student {student_id} is registered for:")
        all_regs = conn.execute("""
            SELECT mr.module_id, m.module_code, m.module_name, mr.registration_status
            FROM module_registrations mr
            JOIN modules m ON mr.module_id = m.module_id
            WHERE mr.student_id = ?
            ORDER BY mr.module_id
        """, (student_id,)).fetchall()
        
        for reg in all_regs:
            marker = "🎯" if reg['module_id'] == session['module_id'] else "  "
            status_marker = "✅" if reg['registration_status'] == 'active' else "❌"
            print(f"   {marker} {status_marker} Module {reg['module_id']}: {reg['module_code']} - {reg['module_name']} ({reg['registration_status']})")
    else:
        print("❌ No active session found")
    
    conn.close()

if __name__ == "__main__":
    check_session_module()
