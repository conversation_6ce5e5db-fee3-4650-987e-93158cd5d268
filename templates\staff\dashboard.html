{% extends "base.html" %}

{% block title %}Staff Dashboard - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1">Welcome back, {{ current_user.username }}!</h1>
                    <p class="text-muted mb-0">Staff Dashboard - {{ current_user.role }}</p>
                </div>
                <div>
                    <span class="badge bg-success">
                        <i class="fas fa-circle me-1"></i>Online
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row g-4 mb-5">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-users text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1" id="total-students">
                        <div class="spinner-border spinner-border-sm" role="status"></div>
                    </h4>
                    <p class="text-muted small mb-0">Total Students</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-calendar-check text-success" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1" id="attendance-rate">
                        <div class="spinner-border spinner-border-sm" role="status"></div>
                    </h4>
                    <p class="text-muted small mb-0">Attendance Rate</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-chalkboard text-info" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1" id="active-sessions">
                        <div class="spinner-border spinner-border-sm" role="status"></div>
                    </h4>
                    <p class="text-muted small mb-0">Active Sessions</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-user-check text-warning" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1" id="todays-attendance">
                        <div class="spinner-border spinner-border-sm" role="status"></div>
                    </h4>
                    <p class="text-muted small mb-0">Today's Attendance</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row g-4">
        <!-- Quick Actions -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <a href="{{ url_for('create_attendance_session') }}" class="btn btn-outline-primary">
                            <i class="fas fa-plus me-2"></i>Create Attendance Session
                        </a>
                        {% if active_sessions %}
                        <div class="dropdown">
                            <button class="btn btn-outline-success dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-camera me-2"></i>Start Attendance
                            </button>
                            <ul class="dropdown-menu w-100">
                                {% for session in active_sessions %}
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('start_attendance_session', session_id=session.session_id) }}">
                                        {{ session.session_name }} ({{ session.module_code }})
                                    </a>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% else %}
                        <button class="btn btn-outline-success" disabled>
                            <i class="fas fa-camera me-2"></i>No Sessions Available
                        </button>
                        {% endif %}
                        <a href="{{ url_for('calculate_attendance_marks') }}" class="btn btn-outline-success">
                            <i class="fas fa-calculator me-2"></i>Calculate Marks
                        </a>
                        <a href="{{ url_for('staff_reports') }}" class="btn btn-outline-info">
                            <i class="fas fa-chart-bar me-2"></i>View Reports
                        </a>
                        <a href="{{ url_for('manage_students') }}" class="btn btn-outline-warning">
                            <i class="fas fa-users me-2"></i>Manage Students
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Recent Activity
                    </h5>
                    <small class="text-muted" id="activity-last-updated">Loading...</small>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush" id="recent-activity">
                        <div class="text-center py-4">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="text-muted mt-2">Loading recent activity...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Schedule -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>Today's Schedule
                    </h5>
                    <small class="text-muted">{{ moment().format('MMMM Do, YYYY') }}</small>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Session</th>
                                    <th>Module</th>
                                    <th>Location</th>
                                    <th>Attendance</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if todays_schedule %}
                                    {% for session in todays_schedule %}
                                    <tr>
                                        <td>
                                            <strong>{{ session.scheduled_start_time }}</strong><br>
                                            <small class="text-muted">{{ session.scheduled_end_time }}</small>
                                        </td>
                                        <td>{{ session.session_name }}</td>
                                        <td>{{ session.module_code }} - {{ session.module_name }}</td>
                                        <td>{{ session.location or 'TBA' }}</td>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ session.attendance_count }}/{{ session.enrolled_count }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if session.is_completed %}
                                                <span class="badge bg-success">Completed</span>
                                            {% elif session.is_active %}
                                                <span class="badge bg-warning">Active</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Scheduled</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if not session.is_completed %}
                                                {% if session.is_active %}
                                                    <a href="{{ url_for('monitor_attendance_session', session_id=session.session_id) }}"
                                                       class="btn btn-sm btn-success">Monitor</a>
                                                {% else %}
                                                    <a href="{{ url_for('start_attendance_session', session_id=session.session_id) }}"
                                                       class="btn btn-sm btn-primary">Start</a>
                                                {% endif %}
                                            {% else %}
                                                <a href="{{ url_for('view_session_report', session_id=session.session_id) }}"
                                                   class="btn btn-sm btn-outline-info">View Report</a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center text-muted py-4">
                                            <i class="fas fa-calendar-times me-2"></i>
                                            No sessions scheduled for today.
                                            <br>
                                            <a href="{{ url_for('create_attendance_session') }}" class="btn btn-sm btn-primary mt-2">
                                                <i class="fas fa-plus me-1"></i>Create New Session
                                            </a>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Real-time dashboard updates
let lastStatsUpdate = null;
let lastActivityUpdate = null;

function updateDashboardStats() {
    fetch('/api/staff/dashboard_stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('total-students').textContent = data.total_students;
            document.getElementById('attendance-rate').textContent = data.attendance_rate + '%';
            document.getElementById('active-sessions').textContent = data.active_sessions;
            document.getElementById('todays-attendance').textContent = data.todays_attendance;
            lastStatsUpdate = data.timestamp;
        })
        .catch(error => {
            console.error('Error updating stats:', error);
            // Show error state
            document.getElementById('total-students').innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i>';
            document.getElementById('attendance-rate').innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i>';
            document.getElementById('active-sessions').innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i>';
            document.getElementById('todays-attendance').innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i>';
        });
}

function updateRecentActivity() {
    fetch('/api/staff/recent_activity')
        .then(response => response.json())
        .then(data => {
            const activityContainer = document.getElementById('recent-activity');
            const lastUpdatedElement = document.getElementById('activity-last-updated');

            if (data.activities.length === 0) {
                activityContainer.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-info-circle text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No recent activity</p>
                    </div>
                `;
            } else {
                activityContainer.innerHTML = data.activities.map(activity => `
                    <div class="list-group-item border-0 px-0">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">${activity.title}</h6>
                                <p class="mb-1 small text-muted">${activity.description}</p>
                                <small class="text-muted">${formatTimeAgo(activity.time)}</small>
                            </div>
                            <span class="badge bg-${activity.badge_class}">${activity.status}</span>
                        </div>
                    </div>
                `).join('');
            }

            lastUpdatedElement.textContent = `Updated ${formatTimeAgo(data.timestamp)}`;
            lastActivityUpdate = data.timestamp;
        })
        .catch(error => {
            console.error('Error updating activity:', error);
            document.getElementById('recent-activity').innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
                    <p class="text-muted mt-2">Error loading activity</p>
                </div>
            `;
        });
}

function formatTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now - time) / 1000);

    if (diffInSeconds < 60) {
        return 'just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    }
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Load initial data
    updateDashboardStats();
    updateRecentActivity();

    // Set up periodic updates
    setInterval(updateDashboardStats, 30000); // Update stats every 30 seconds
    setInterval(updateRecentActivity, 60000); // Update activity every minute

    // Update time displays every minute
    setInterval(function() {
        if (lastActivityUpdate) {
            document.getElementById('activity-last-updated').textContent = `Updated ${formatTimeAgo(lastActivityUpdate)}`;
        }
    }, 60000);
});

// Auto-refresh page every 5 minutes to ensure data consistency
setInterval(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}

{% block extra_css %}
<style>
.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

.badge {
    font-size: 0.75em;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}
</style>
{% endblock %}
