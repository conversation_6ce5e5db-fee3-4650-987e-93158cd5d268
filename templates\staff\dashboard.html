{% extends "base.html" %}

{% block title %}Staff Dashboard - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1">Welcome back, {{ current_user.username }}!</h1>
                    <p class="text-muted mb-0">Staff Dashboard - {{ current_user.role }}</p>
                </div>
                <div>
                    <span class="badge bg-success">
                        <i class="fas fa-circle me-1"></i>Online
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row g-4 mb-5">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-users text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1">1,234</h4>
                    <p class="text-muted small mb-0">Total Students</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-calendar-check text-success" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1">89%</h4>
                    <p class="text-muted small mb-0">Attendance Rate</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-chalkboard text-info" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1">24</h4>
                    <p class="text-muted small mb-0">Active Sessions</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-user-check text-warning" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1">156</h4>
                    <p class="text-muted small mb-0">Today's Attendance</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row g-4">
        <!-- Quick Actions -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <a href="{{ url_for('create_attendance_session') }}" class="btn btn-outline-primary">
                            <i class="fas fa-plus me-2"></i>Create Attendance Session
                        </a>
                        {% if active_sessions %}
                        <div class="dropdown">
                            <button class="btn btn-outline-success dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-camera me-2"></i>Start Attendance
                            </button>
                            <ul class="dropdown-menu w-100">
                                {% for session in active_sessions %}
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('start_attendance_session', session_id=session.session_id) }}">
                                        {{ session.session_name }} ({{ session.module_code }})
                                    </a>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% else %}
                        <button class="btn btn-outline-success" disabled>
                            <i class="fas fa-camera me-2"></i>No Sessions Available
                        </button>
                        {% endif %}
                        <a href="{{ url_for('calculate_attendance_marks') }}" class="btn btn-outline-success">
                            <i class="fas fa-calculator me-2"></i>Calculate Marks
                        </a>
                        <button class="btn btn-outline-info">
                            <i class="fas fa-chart-bar me-2"></i>View Reports
                        </button>
                        <a href="{{ url_for('manage_students') }}" class="btn btn-outline-warning">
                            <i class="fas fa-users me-2"></i>Manage Students
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">CS101 - Programming Fundamentals</h6>
                                    <p class="mb-1 small text-muted">Attendance recorded for 45 students</p>
                                    <small class="text-muted">2 hours ago</small>
                                </div>
                                <span class="badge bg-success">Completed</span>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">CS102 - Data Structures</h6>
                                    <p class="mb-1 small text-muted">Session created for tomorrow</p>
                                    <small class="text-muted">4 hours ago</small>
                                </div>
                                <span class="badge bg-info">Scheduled</span>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">New Student Registration</h6>
                                    <p class="mb-1 small text-muted">John Doe completed facial recognition setup</p>
                                    <small class="text-muted">6 hours ago</small>
                                </div>
                                <span class="badge bg-primary">New</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Schedule -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>Today's Schedule
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Semester</th>
                                    <th>Module</th>
                                    <th>Course</th>
                                    <th>Enrolled</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if modules %}
                                    {% for module in modules %}
                                    <tr>
                                        <td>{{ module.semester }}</td>
                                        <td>{{ module.module_code }} - {{ module.module_name }}</td>
                                        <td>{{ module.course_name }} (Year {{ module.year_level }})</td>
                                        <td>-</td>
                                        <td><span class="badge bg-secondary">Available</span></td>
                                        <td>
                                            <a href="{{ url_for('create_attendance_session') }}?module_id={{ module.module_id }}"
                                               class="btn btn-sm btn-outline-primary">Create Session</a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">
                                            <i class="fas fa-info-circle me-2"></i>
                                            No modules assigned. Contact administrator to assign modules.
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.list-group-item:hover {
    background-color: #f8f9fa;
}
</style>
{% endblock %}
