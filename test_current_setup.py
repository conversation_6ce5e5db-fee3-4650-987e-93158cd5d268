import sqlite3
from datetime import datetime

def test_current_setup():
    """Test the current setup to identify any issues"""
    print("🔍 Testing current setup...")
    
    try:
        # Test database connection
        conn = sqlite3.connect('attendance_fixed.db')
        conn.row_factory = sqlite3.Row
        print("✅ Database connection successful")
        
        # Test student login
        student_id = 1
        current_time = datetime.now().isoformat()
        print(f"✅ Current time: {current_time}")
        
        # Test student dashboard query
        print("\n📚 Testing student dashboard query...")
        active_sessions = conn.execute('''
            SELECT as_.session_id, as_.session_name, as_.session_date,
                   as_.attendance_window_start, as_.attendance_window_end,
                   as_.is_active, m.module_code, m.module_name,
                   CASE WHEN ar.student_id IS NOT NULL THEN 1 ELSE 0 END as already_attended
            FROM attendance_sessions as_
            JOIN modules m ON as_.module_id = m.module_id
            JOIN module_registrations mr ON m.module_id = mr.module_id
            LEFT JOIN attendance_records ar ON as_.session_id = ar.session_id AND ar.student_id = ?
            WHERE mr.student_id = ? AND mr.registration_status = 'active'
            AND as_.is_active = 1 AND as_.is_completed = 0
            AND as_.attendance_window_start IS NOT NULL 
            AND as_.attendance_window_end IS NOT NULL
            AND ? BETWEEN as_.attendance_window_start AND as_.attendance_window_end
            ORDER BY as_.session_date DESC, as_.scheduled_start_time DESC
            LIMIT 10
        ''', (student_id, student_id, current_time)).fetchall()
        
        print(f"✅ Student dashboard query successful: {len(active_sessions)} sessions found")
        for session in active_sessions:
            print(f"   - {session['session_name']} ({session['module_code']})")
        
        # Test student attendance query
        print("\n🎯 Testing student attendance query...")
        attendance_sessions = conn.execute('''
            SELECT DISTINCT as_.session_id, as_.session_name, as_.session_date,
                   as_.attendance_window_start, as_.attendance_window_end,
                   as_.is_active, as_.location, m.module_code, m.module_name,
                   CASE WHEN sa.attendance_id IS NOT NULL THEN 1 ELSE 0 END as already_marked
            FROM attendance_sessions as_
            JOIN modules m ON as_.module_id = m.module_id
            JOIN module_registrations mr ON m.module_id = mr.module_id
            JOIN student_enrollments se ON mr.enrollment_id = se.enrollment_id
            LEFT JOIN student_attendance sa ON as_.session_id = sa.session_id AND sa.student_id = ?
            WHERE se.student_id = ? AND mr.registration_status = 'active'
            AND as_.is_active = 1
            AND as_.attendance_window_start IS NOT NULL 
            AND as_.attendance_window_end IS NOT NULL
            AND ? BETWEEN as_.attendance_window_start AND as_.attendance_window_end
            ORDER BY as_.attendance_window_end ASC
        ''', (student_id, student_id, current_time)).fetchall()
        
        print(f"✅ Student attendance query successful: {len(attendance_sessions)} sessions found")
        for session in attendance_sessions:
            print(f"   - {session['session_name']} ({session['module_code']})")
        
        # Test staff dashboard query
        print("\n👨‍🏫 Testing staff dashboard query...")
        lecturer_id = 2  # Bhavek
        staff_sessions = conn.execute('''
            SELECT as_.session_id, as_.session_name, as_.session_date,
                   as_.attendance_window_start, as_.attendance_window_end,
                   as_.is_active, m.module_code, m.module_name
            FROM attendance_sessions as_
            JOIN modules m ON as_.module_id = m.module_id
            WHERE as_.lecturer_id = ? AND as_.is_completed = 0
            ORDER BY as_.session_date DESC, as_.scheduled_start_time DESC
            LIMIT 5
        ''', (lecturer_id,)).fetchall()
        
        print(f"✅ Staff dashboard query successful: {len(staff_sessions)} sessions found")
        for session in staff_sessions:
            print(f"   - {session['session_name']} ({session['module_code']})")
        
        # Test manage students query
        print("\n👥 Testing manage students query...")
        students = conn.execute('''
            SELECT DISTINCT s.student_id, s.student_number, s.first_name, s.last_name, s.email,
                   COUNT(DISTINCT mr.module_id) as enrolled_modules,
                   COUNT(DISTINCT ar.record_id) as total_attendance
            FROM students s
            JOIN module_registrations mr ON s.student_id = mr.student_id
            JOIN modules m ON mr.module_id = m.module_id
            JOIN module_lecturers ml ON m.module_id = ml.module_id
            LEFT JOIN attendance_records ar ON s.student_id = ar.student_id
            WHERE ml.lecturer_id = ? AND mr.registration_status = 'active'
            GROUP BY s.student_id, s.student_number, s.first_name, s.last_name, s.email
            ORDER BY s.last_name, s.first_name
        ''', (lecturer_id,)).fetchall()
        
        print(f"✅ Manage students query successful: {len(students)} students found")
        for student in students:
            print(f"   - {student['student_number']}: {student['first_name']} {student['last_name']}")
        
        conn.close()
        print("\n🎉 All tests passed successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_current_setup()
