{% extends "base.html" %}

{% block title %}Choose Portal - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="text-center mb-5">
                <h1 class="display-4 fw-bold mb-3">Choose Your Portal</h1>
                <p class="lead text-muted">
                    Select the appropriate portal based on your role in the institution.
                </p>
            </div>
            
            <div class="row g-4">
                <!-- Student Portal -->
                <div class="col-md-6">
                    <div class="card h-100 border-0 shadow-lg">
                        <div class="card-header text-center py-4">
                            <i class="fas fa-user-graduate" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                            <h3 class="mb-0">Student Portal</h3>
                        </div>
                        <div class="card-body p-4">
                            <h5 class="card-title mb-3">New Student Registration</h5>
                            <p class="card-text mb-4">
                                Register as a new student by providing your details, selecting your faculty, 
                                course, academic year, and modules. Complete the process with facial recognition setup.
                            </p>
                            
                            <div class="mb-4">
                                <h6 class="fw-bold mb-2">Registration Process:</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Basic Information
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Faculty Selection
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Course & Year Selection
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Module Registration
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Facial Recognition Setup
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('student_register') }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-user-plus me-2"></i>
                                    New Student Registration
                                </a>
                                <a href="{{ url_for('student_login') }}" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Student Login
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Staff Portal -->
                <div class="col-md-6">
                    <div class="card h-100 border-0 shadow-lg">
                        <div class="card-header text-center py-4">
                            <i class="fas fa-chalkboard-teacher" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                            <h3 class="mb-0">Staff Portal</h3>
                        </div>
                        <div class="card-body p-4">
                            <h5 class="card-title mb-3">Staff & Lecturer Login</h5>
                            <p class="card-text mb-4">
                                Access the staff dashboard to manage attendance, view reports, and oversee 
                                student enrollment. Pre-configured accounts are provided by administration.
                            </p>
                            
                            <div class="mb-4">
                                <h6 class="fw-bold mb-2">Staff Features:</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-chart-bar text-primary me-2"></i>
                                        Attendance Reports
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-calendar-check text-primary me-2"></i>
                                        Class Session Management
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-users text-primary me-2"></i>
                                        Student Enrollment Overview
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-download text-primary me-2"></i>
                                        Export Attendance Data
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-cog text-primary me-2"></i>
                                        System Administration
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="d-grid">
                                <a href="{{ url_for('staff_login') }}" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Staff Login
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Information Section -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-4">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h5 class="mb-2">
                                        <i class="fas fa-info-circle text-info me-2"></i>
                                        Need Help?
                                    </h5>
                                    <p class="mb-0 text-muted">
                                        If you're a new student, use the Student Portal to register. 
                                        Staff members should contact IT support for login credentials.
                                    </p>
                                </div>
                                <div class="col-md-4 text-md-end">
                                    <a href="#" class="btn btn-info">
                                        <i class="fas fa-question-circle me-2"></i>
                                        Contact Support
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
