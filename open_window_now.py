import sqlite3
from datetime import datetime, timedelta

def open_attendance_window_now():
    conn = sqlite3.connect('attendance_fixed.db')
    
    print("🔧 Opening attendance window for testing...")
    
    # Get the most recent session
    session = conn.execute('''
        SELECT session_id, session_name, module_id, lecturer_id
        FROM attendance_sessions
        WHERE is_active = 1 AND is_completed = 0
        ORDER BY session_date DESC, scheduled_start_time DESC
        LIMIT 1
    ''').fetchone()
    
    if session:
        session_id = session[0]
        session_name = session[1]
        
        # Set attendance window to be open for the next 30 minutes
        now = datetime.now()
        window_start = now
        window_end = now + timedelta(minutes=30)
        
        print(f"📝 Updating session {session_id}: {session_name}")
        print(f"   Window Start: {window_start}")
        print(f"   Window End: {window_end}")
        
        # Update the session with new attendance window
        conn.execute('''
            UPDATE attendance_sessions 
            SET attendance_window_start = ?,
                attendance_window_end = ?,
                is_active = 1
            WHERE session_id = ?
        ''', (window_start.isoformat(), window_end.isoformat(), session_id))
        
        conn.commit()
        
        print(f"✅ Attendance window opened successfully!")
        print(f"   Students can now mark attendance for the next 30 minutes")
        print(f"   Window closes at: {window_end.strftime('%H:%M:%S')}")
        
    else:
        print("❌ No active sessions found")
    
    conn.close()

if __name__ == "__main__":
    open_attendance_window_now()
