{% extends "base.html" %}

{% block title %}Admin Dashboard - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                        Admin Dashboard
                    </h1>
                    <p class="text-muted mb-0">System overview and management</p>
                </div>
                <div>
                    <a href="{{ url_for('admin_tables') }}" class="btn btn-primary">
                        <i class="fas fa-table me-2"></i>
                        Manage Tables
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Students
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_students }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-graduate fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Staff
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_staff }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Modules
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_modules }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-books fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Attendance Records
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_attendance }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <!-- Recent Students -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        Recent Student Registrations
                    </h6>
                </div>
                <div class="card-body">
                    {% if recent_students %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Student Number</th>
                                        <th>Name</th>
                                        <th>Registered</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for student in recent_students %}
                                    <tr>
                                        <td>{{ student.student_number }}</td>
                                        <td>{{ student.first_name }} {{ student.last_name }}</td>
                                        <td>{{ student.created_at if student.created_at else 'N/A' }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">No recent student registrations.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Attendance -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-calendar-check me-2"></i>
                        Recent Attendance
                    </h6>
                </div>
                <div class="card-body">
                    {% if recent_attendance %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Student</th>
                                        <th>Module</th>
                                        <th>Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for attendance in recent_attendance %}
                                    <tr>
                                        <td>
                                            <small>{{ attendance.student_number }}</small><br>
                                            {{ attendance.first_name }} {{ attendance.last_name }}
                                        </td>
                                        <td>
                                            <small>{{ attendance.module_code }}</small><br>
                                            {{ attendance.session_name }}
                                        </td>
                                        <td>{{ attendance.attendance_time if attendance.attendance_time else 'N/A' }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">No recent attendance records.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('admin_view_table', table_name='students') }}" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-user-graduate me-2"></i>
                                Manage Students
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('admin_view_table', table_name='users') }}" class="btn btn-outline-success btn-block">
                                <i class="fas fa-user-tie me-2"></i>
                                Manage Staff
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('admin_view_table', table_name='modules') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-books me-2"></i>
                                Manage Modules
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('admin_view_table', table_name='attendance_sessions') }}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-calendar-check me-2"></i>
                                View Sessions
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}
