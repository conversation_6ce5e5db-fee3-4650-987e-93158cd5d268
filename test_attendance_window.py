#!/usr/bin/env python3
"""
Test attendance window timing
"""

import sqlite3
from datetime import datetime

def test_attendance_window():
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    print("🔍 Testing attendance window timing...")
    
    # Get the active session
    session = conn.execute("""
        SELECT session_id, session_name, attendance_window_start, attendance_window_end, is_active
        FROM attendance_sessions 
        WHERE is_active = 1
        ORDER BY session_id DESC
        LIMIT 1
    """).fetchone()
    
    if not session:
        print("❌ No active session found")
        return
    
    print(f"📍 Session {session['session_id']}: {session['session_name']}")
    print(f"   Window start: {session['attendance_window_start']}")
    print(f"   Window end: {session['attendance_window_end']}")
    print(f"   Is active: {session['is_active']}")
    
    # Check current time
    now = datetime.now()
    print(f"   Current time: {now.isoformat()}")
    
    # Parse window times
    try:
        window_start = datetime.fromisoformat(session['attendance_window_start'])
        window_end = datetime.fromisoformat(session['attendance_window_end'])
        
        print(f"\n⏰ Time analysis:")
        print(f"   Window start: {window_start}")
        print(f"   Current time: {now}")
        print(f"   Window end:   {window_end}")
        
        if now < window_start:
            print(f"   ⏳ Window hasn't started yet (starts in {(window_start - now).total_seconds():.0f} seconds)")
        elif now > window_end:
            print(f"   ⏰ Window has ended (ended {(now - window_end).total_seconds():.0f} seconds ago)")
        else:
            print(f"   ✅ Window is currently open (closes in {(window_end - now).total_seconds():.0f} seconds)")
            
    except Exception as e:
        print(f"   ❌ Error parsing window times: {e}")
    
    conn.close()

if __name__ == "__main__":
    test_attendance_window()
