{% extends "base.html" %}

{% block title %}Staff Login - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-6 col-md-8">
            <div class="card shadow-lg border-0">
                <div class="card-header text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-chalkboard-teacher me-2"></i>
                        Staff Login
                    </h2>
                    <p class="mb-0 mt-2">Access the staff dashboard</p>
                </div>
                <div class="card-body p-5">
                    <form method="POST">
                        <!-- Username -->
                        <div class="mb-4">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-1"></i>Username
                            </label>
                            <input type="text" class="form-control form-control-lg" 
                                   id="username" name="username" required 
                                   placeholder="Enter your username"
                                   autocomplete="username">
                        </div>

                        <!-- Password -->
                        <div class="mb-4">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-1"></i>Password
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control form-control-lg" 
                                       id="password" name="password" required 
                                       placeholder="Enter your password"
                                       autocomplete="current-password">
                                <button class="btn btn-outline-secondary" type="button" 
                                        onclick="togglePassword()">
                                    <i class="fas fa-eye" id="password-eye"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Remember Me -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    Remember me
                                </label>
                            </div>
                        </div>

                        <!-- Login Button -->
                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Login
                            </button>
                        </div>

                        <!-- Forgot Password -->
                        <div class="text-center">
                            <a href="#" class="text-muted small">
                                <i class="fas fa-question-circle me-1"></i>
                                Forgot your password?
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Demo Credentials -->
            <div class="card border-0 bg-light mt-4">
                <div class="card-body p-4">
                    <h6 class="mb-3">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        Demo Credentials
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-2"><strong>Admin Account:</strong></p>
                            <p class="small text-muted mb-1">Username: <code>admin</code></p>
                            <p class="small text-muted">Password: <code>admin123</code></p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-2"><strong>Lecturer Account:</strong></p>
                            <p class="small text-muted mb-1">Username: <code>prof.smith</code></p>
                            <p class="small text-muted">Password: <code>lecturer123</code></p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-info me-2" onclick="fillCredentials('admin', 'admin123')">
                            Use Admin
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="fillCredentials('prof.smith', 'lecturer123')">
                            Use Lecturer
                        </button>
                    </div>
                </div>
            </div>

            <!-- Student Portal Link -->
            <div class="text-center mt-4">
                <p class="mb-0">
                    Are you a student? 
                    <a href="{{ url_for('student_register') }}">Register here</a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function togglePassword() {
    const field = document.getElementById('password');
    const eye = document.getElementById('password-eye');
    
    if (field.type === 'password') {
        field.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}

function fillCredentials(username, password) {
    document.getElementById('username').value = username;
    document.getElementById('password').value = password;
}

// Auto-focus username field
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('username').focus();
});
</script>
{% endblock %}
