import sqlite3

def debug_dashboard_queries():
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    print("🔍 Debugging Dashboard Queries...")
    
    # Test student dashboard query (using student ID 1)
    student_id = 1
    print(f"\n📚 Testing Student Dashboard for student_id: {student_id}")
    
    # Student's modules query
    print("\n1. Student's enrolled modules:")
    modules = conn.execute('''
        SELECT DISTINCT m.module_id, m.module_code, m.module_name, m.semester,
               c.course_name, cy.year_level
        FROM modules m
        JOIN course_years cy ON m.course_year_id = cy.course_year_id
        JOIN courses c ON cy.course_id = c.course_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        WHERE mr.student_id = ? AND mr.registration_status = 'active'
        ORDER BY c.course_name, cy.year_level, m.module_name
    ''', (student_id,)).fetchall()
    
    print(f"   Found {len(modules)} modules:")
    for module in modules:
        print(f"     - {module['module_code']}: {module['module_name']}")
    
    # Student's active sessions query
    print("\n2. Student's active sessions:")
    active_sessions = conn.execute('''
        SELECT as_.session_id, as_.session_name, as_.session_date,
               as_.attendance_window_start, as_.attendance_window_end,
               as_.is_active, m.module_code, m.module_name,
               CASE WHEN ar.student_id IS NOT NULL THEN 1 ELSE 0 END as already_attended
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        LEFT JOIN attendance_records ar ON as_.session_id = ar.session_id AND ar.student_id = ?
        WHERE mr.student_id = ? AND mr.registration_status = 'active'
        AND as_.is_active = 1 AND as_.is_completed = 0
        ORDER BY as_.session_date DESC, as_.scheduled_start_time DESC
        LIMIT 10
    ''', (student_id, student_id)).fetchall()
    
    print(f"   Found {len(active_sessions)} active sessions:")
    for session in active_sessions:
        print(f"     - Session {session['session_id']}: {session['session_name']}")
        print(f"       Module: {session['module_code']} - {session['module_name']}")
        print(f"       Date: {session['session_date']}, Active: {session['is_active']}")
        print(f"       Already attended: {session['already_attended']}")
    
    # Test staff dashboard query (using lecturer ID 2 - Bhavek)
    lecturer_id = 2
    print(f"\n👨‍🏫 Testing Staff Dashboard for lecturer_id: {lecturer_id}")
    
    # Staff's modules query
    print("\n1. Staff's assigned modules:")
    staff_modules = conn.execute('''
        SELECT m.module_id, m.module_code, m.module_name, m.semester,
               c.course_name, cy.year_level
        FROM modules m
        JOIN course_years cy ON m.course_year_id = cy.course_year_id
        JOIN courses c ON cy.course_id = c.course_id
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        WHERE ml.lecturer_id = ?
        ORDER BY c.course_name, cy.year_level, m.module_name
    ''', (lecturer_id,)).fetchall()
    
    print(f"   Found {len(staff_modules)} modules:")
    for module in staff_modules:
        print(f"     - {module['module_code']}: {module['module_name']}")
    
    # Staff's active sessions query
    print("\n2. Staff's active sessions:")
    staff_sessions = conn.execute('''
        SELECT as_.session_id, as_.session_name, as_.session_date,
               as_.attendance_window_start, as_.attendance_window_end,
               as_.is_active, m.module_code, m.module_name
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        WHERE as_.lecturer_id = ? AND as_.is_completed = 0
        ORDER BY as_.session_date DESC, as_.scheduled_start_time DESC
        LIMIT 5
    ''', (lecturer_id,)).fetchall()
    
    print(f"   Found {len(staff_sessions)} active sessions:")
    for session in staff_sessions:
        print(f"     - Session {session['session_id']}: {session['session_name']}")
        print(f"       Module: {session['module_code']} - {session['module_name']}")
        print(f"       Date: {session['session_date']}, Active: {session['is_active']}")
    
    # Check if there are any module_lecturers assignments
    print(f"\n🔗 Checking module_lecturers assignments:")
    assignments = conn.execute('''
        SELECT ml.assignment_id, ml.module_id, ml.lecturer_id, m.module_code, u.username
        FROM module_lecturers ml
        JOIN modules m ON ml.module_id = m.module_id
        JOIN users u ON ml.lecturer_id = u.user_id
        ORDER BY ml.lecturer_id, m.module_code
    ''').fetchall()
    
    print(f"   Found {len(assignments)} assignments:")
    for assignment in assignments:
        print(f"     - {assignment['username']} assigned to {assignment['module_code']} (module_id: {assignment['module_id']})")
    
    # Check current user IDs
    print(f"\n👤 Checking user IDs:")
    users = conn.execute('''
        SELECT user_id, username, first_name, last_name, role_id
        FROM users
        ORDER BY user_id
    ''').fetchall()
    
    for user in users:
        print(f"     - ID {user['user_id']}: {user['username']} ({user['first_name']} {user['last_name']}) - Role: {user['role_id']}")
    
    # Check student IDs
    print(f"\n🎓 Checking student IDs:")
    students = conn.execute('''
        SELECT student_id, student_number, first_name, last_name
        FROM students
        ORDER BY student_id
    ''').fetchall()
    
    for student in students:
        print(f"     - ID {student['student_id']}: {student['student_number']} ({student['first_name']} {student['last_name']})")
    
    conn.close()

if __name__ == "__main__":
    debug_dashboard_queries()
