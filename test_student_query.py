import sqlite3
from datetime import datetime

def test_student_query():
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    student_id = 1
    print(f"🔍 Testing student query for student_id: {student_id}")
    
    # Current time
    now = datetime.now()
    print(f"Current time: {now}")
    print(f"Current time ISO: {now.isoformat()}")
    
    # Test the original query
    print(f"\n1. Original query with datetime('now'):")
    sessions1 = conn.execute('''
        SELECT as_.session_id, as_.session_name, as_.session_date,
               as_.attendance_window_start, as_.attendance_window_end,
               m.module_code, m.module_name,
               CASE WHEN ar.student_id IS NOT NULL THEN 1 ELSE 0 END as already_attended
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        LEFT JOIN attendance_records ar ON as_.session_id = ar.session_id AND ar.student_id = ?
        WHERE mr.student_id = ? AND mr.registration_status = 'active'
        AND as_.attendance_window_start IS NOT NULL 
        AND as_.attendance_window_end IS NOT NULL
        AND datetime('now') BETWEEN as_.attendance_window_start AND as_.attendance_window_end
        ORDER BY as_.session_date DESC
    ''', (student_id, student_id)).fetchall()
    
    print(f"   Found {len(sessions1)} sessions")
    for session in sessions1:
        print(f"     - {session['session_name']} ({session['module_code']})")
    
    # Test with explicit current time
    print(f"\n2. Query with explicit current time:")
    current_time = now.isoformat()
    sessions2 = conn.execute('''
        SELECT as_.session_id, as_.session_name, as_.session_date,
               as_.attendance_window_start, as_.attendance_window_end,
               m.module_code, m.module_name,
               CASE WHEN ar.student_id IS NOT NULL THEN 1 ELSE 0 END as already_attended
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        LEFT JOIN attendance_records ar ON as_.session_id = ar.session_id AND ar.student_id = ?
        WHERE mr.student_id = ? AND mr.registration_status = 'active'
        AND as_.attendance_window_start IS NOT NULL 
        AND as_.attendance_window_end IS NOT NULL
        AND ? BETWEEN as_.attendance_window_start AND as_.attendance_window_end
        ORDER BY as_.session_date DESC
    ''', (student_id, student_id, current_time)).fetchall()
    
    print(f"   Found {len(sessions2)} sessions")
    for session in sessions2:
        print(f"     - {session['session_name']} ({session['module_code']})")
    
    # Test without time constraint to see all sessions
    print(f"\n3. All sessions for student (no time constraint):")
    all_sessions = conn.execute('''
        SELECT as_.session_id, as_.session_name, as_.session_date,
               as_.attendance_window_start, as_.attendance_window_end,
               m.module_code, m.module_name,
               CASE WHEN ar.student_id IS NOT NULL THEN 1 ELSE 0 END as already_attended
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        LEFT JOIN attendance_records ar ON as_.session_id = ar.session_id AND ar.student_id = ?
        WHERE mr.student_id = ? AND mr.registration_status = 'active'
        ORDER BY as_.session_date DESC
    ''', (student_id, student_id)).fetchall()
    
    print(f"   Found {len(all_sessions)} total sessions")
    for session in all_sessions:
        print(f"     - {session['session_name']} ({session['module_code']})")
        print(f"       Window: {session['attendance_window_start']} to {session['attendance_window_end']}")
        
        # Check if window is open manually
        if session['attendance_window_start'] and session['attendance_window_end']:
            try:
                window_start = datetime.fromisoformat(session['attendance_window_start'])
                window_end = datetime.fromisoformat(session['attendance_window_end'])
                is_open = window_start <= now <= window_end
                print(f"       Window open: {is_open}")
            except Exception as e:
                print(f"       Error parsing time: {e}")
        else:
            print(f"       No window set")
    
    conn.close()

if __name__ == "__main__":
    test_student_query()
