{% extends "base.html" %}

{% block title %}Registration Complete - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg">
                <div class="card-header bg-success text-white text-center">
                    <h3><i class="fas fa-check-circle me-2"></i>Registration Complete!</h3>
                </div>
                <div class="card-body text-center p-5">
                    <div class="mb-4">
                        <i class="fas fa-graduation-cap text-success" style="font-size: 4rem;"></i>
                    </div>
                    
                    <h4 class="text-success mb-3">Welcome to the University!</h4>
                    <p class="lead mb-4">Your student registration has been completed successfully.</p>
                    
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i>What's Next?</h5>
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2"><i class="fas fa-camera text-primary me-2"></i>Set up facial recognition for attendance</li>
                            <li class="mb-2"><i class="fas fa-calendar text-primary me-2"></i>Check your class schedule</li>
                            <li class="mb-2"><i class="fas fa-book text-primary me-2"></i>Access your enrolled modules</li>
                            <li><i class="fas fa-chart-line text-primary me-2"></i>Monitor your attendance records</li>
                        </ul>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6 mb-3">
                            <a href="{{ url_for('student_facial_setup') }}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-camera me-2"></i>Set Up Facial Recognition
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary btn-lg w-100">
                                <i class="fas fa-home me-2"></i>Go to Dashboard
                            </a>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="text-muted">
                        <small>
                            <i class="fas fa-shield-alt me-1"></i>
                            Your data is secure and protected. Facial recognition data is encrypted and stored safely.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
}

.alert {
    border-radius: 10px;
}

.list-unstyled li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.list-unstyled li:last-child {
    border-bottom: none;
}
</style>
{% endblock %}
