{% extends "base.html" %}

{% block title %}Facial Recognition Setup{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-header text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-camera me-2"></i>
                        Facial Recognition Setup
                    </h2>
                    <p class="mb-0 mt-2">Complete your biometric enrollment for automated attendance</p>
                </div>
                <div class="card-body p-5">
                    <!-- Status Check -->
                    <div id="statusSection">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Checking enrollment status...</p>
                        </div>
                    </div>

                    <!-- Already Enrolled -->
                    <div id="enrolledSection" class="d-none">
                        <div class="text-center">
                            <div class="mb-4">
                                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            </div>
                            <h4 class="text-success mb-3">Facial Recognition Already Set Up!</h4>
                            <p class="lead mb-4">
                                Your face has been successfully enrolled in the system. 
                                You can now use facial recognition for automatic attendance tracking.
                            </p>
                            
                            <div class="alert alert-info" role="alert">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Next Steps:</strong> Simply attend your classes and the system will automatically record your attendance when you're recognized.
                            </div>
                            
                            <div class="d-flex justify-content-center gap-3 mt-4">
                                <button class="btn btn-outline-primary" onclick="testRecognition()">
                                    <i class="fas fa-camera me-2"></i>
                                    Test Recognition
                                </button>
                                <button class="btn btn-warning" onclick="reEnroll()">
                                    <i class="fas fa-redo me-2"></i>
                                    Re-enroll Face
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Not Enrolled -->
                    <div id="notEnrolledSection" class="d-none">
                        <div class="text-center mb-4">
                            <div class="mb-4">
                                <i class="fas fa-user-circle text-muted" style="font-size: 4rem;"></i>
                            </div>
                            <h4 class="mb-3">Set Up Facial Recognition</h4>
                            <p class="lead mb-4">
                                To enable automatic attendance tracking, we need to capture your facial features.
                                This process is secure and your biometric data will be encrypted.
                            </p>
                        </div>

                        <!-- Instructions -->
                        <div class="row mb-4">
                            <div class="col-md-4 text-center">
                                <div class="mb-3">
                                    <i class="fas fa-camera text-primary" style="font-size: 2rem;"></i>
                                </div>
                                <h6>Step 1: Camera Access</h6>
                                <p class="small text-muted">Allow camera access when prompted</p>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="mb-3">
                                    <i class="fas fa-user-check text-success" style="font-size: 2rem;"></i>
                                </div>
                                <h6>Step 2: Face Capture</h6>
                                <p class="small text-muted">Capture 5 clear photos of your face</p>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="mb-3">
                                    <i class="fas fa-shield-alt text-warning" style="font-size: 2rem;"></i>
                                </div>
                                <h6>Step 3: Secure Storage</h6>
                                <p class="small text-muted">Your data is encrypted and stored securely</p>
                            </div>
                        </div>

                        <!-- Start Button -->
                        <div class="d-grid">
                            <button class="btn btn-primary btn-lg" onclick="startEnrollment()">
                                <i class="fas fa-camera me-2"></i>
                                Start Facial Recognition Setup
                            </button>
                        </div>
                    </div>

                    <!-- Test Recognition Section -->
                    <div id="testSection" class="d-none">
                        <div class="text-center mb-4">
                            <h4>Test Facial Recognition</h4>
                            <p>Position your face in front of the camera to test recognition</p>
                        </div>
                        
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <video id="testVideo" width="100%" height="300" autoplay muted class="border rounded"></video>
                                        <div id="testResult" class="mt-3"></div>
                                        <button class="btn btn-secondary mt-3" onclick="stopTest()">
                                            <i class="fas fa-stop me-2"></i>
                                            Stop Test
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Privacy Notice -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-4">
                            <h6 class="mb-3">
                                <i class="fas fa-shield-alt text-primary me-2"></i>
                                Privacy & Security
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="small mb-0">
                                        <li>Your facial data is encrypted using industry-standard algorithms</li>
                                        <li>Biometric data is stored securely and never shared with third parties</li>
                                        <li>You can request deletion of your data at any time</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="small mb-0">
                                        <li>Only facial features are stored, not actual photos</li>
                                        <li>Data is used exclusively for attendance tracking</li>
                                        <li>Full compliance with data protection regulations</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let testStream = null;
let testInterval = null;

// Check enrollment status on page load
document.addEventListener('DOMContentLoaded', function() {
    checkEnrollmentStatus();
});

function checkEnrollmentStatus() {
    fetch('/facial/api/status')
        .then(response => response.json())
        .then(data => {
            document.getElementById('statusSection').classList.add('d-none');
            
            if (data.success && data.enrolled) {
                document.getElementById('enrolledSection').classList.remove('d-none');
            } else {
                document.getElementById('notEnrolledSection').classList.remove('d-none');
            }
        })
        .catch(error => {
            console.error('Error checking status:', error);
            document.getElementById('statusSection').innerHTML = 
                '<div class="alert alert-danger">Error checking enrollment status. Please refresh the page.</div>';
        });
}

function startEnrollment() {
    window.location.href = '/facial/capture';
}

function reEnroll() {
    if (confirm('Are you sure you want to re-enroll your face? This will replace your current facial recognition data.')) {
        // Clear existing images and start fresh
        fetch('/facial/api/clear_images', { method: 'POST' })
            .then(() => {
                window.location.href = '/facial/capture';
            });
    }
}

function testRecognition() {
    document.getElementById('enrolledSection').classList.add('d-none');
    document.getElementById('testSection').classList.remove('d-none');
    
    // Start camera
    navigator.mediaDevices.getUserMedia({ video: true })
        .then(stream => {
            testStream = stream;
            const video = document.getElementById('testVideo');
            video.srcObject = stream;
            
            // Start recognition testing
            testInterval = setInterval(performRecognitionTest, 2000);
        })
        .catch(error => {
            console.error('Error accessing camera:', error);
            document.getElementById('testResult').innerHTML = 
                '<div class="alert alert-danger">Error accessing camera: ' + error.message + '</div>';
        });
}

function performRecognitionTest() {
    const video = document.getElementById('testVideo');
    const canvas = document.createElement('canvas');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0);
    
    const imageData = canvas.toDataURL('image/jpeg');
    
    fetch('/facial/api/recognize', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ image: imageData })
    })
    .then(response => response.json())
    .then(data => {
        const resultDiv = document.getElementById('testResult');
        
        if (data.success) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Recognition Successful!</strong><br>
                    Name: ${data.name}<br>
                    Confidence: ${(data.confidence * 100).toFixed(1)}%
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    No face recognized. Please position your face clearly in the camera.
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error testing recognition:', error);
    });
}

function stopTest() {
    if (testStream) {
        testStream.getTracks().forEach(track => track.stop());
        testStream = null;
    }
    
    if (testInterval) {
        clearInterval(testInterval);
        testInterval = null;
    }
    
    document.getElementById('testSection').classList.add('d-none');
    document.getElementById('enrolledSection').classList.remove('d-none');
}
</script>
{% endblock %}
