import sqlite3
from datetime import datetime, timedelta

def open_attendance_window():
    conn = sqlite3.connect('attendance_fixed.db')
    
    print("🔧 Opening attendance window for testing...")
    
    # Get the most recent session
    session = conn.execute('''
        SELECT session_id, session_name, module_id, lecturer_id
        FROM attendance_sessions
        WHERE is_active = 1 AND is_completed = 0
        ORDER BY session_date DESC, scheduled_start_time DESC
        LIMIT 1
    ''').fetchone()
    
    if session:
        session_id = session[0]
        session_name = session[1]
        
        # Set attendance window to be open for the next 30 minutes
        now = datetime.now()
        window_start = now
        window_end = now + timedelta(minutes=30)
        
        print(f"📝 Updating session {session_id}: {session_name}")
        print(f"   Window Start: {window_start}")
        print(f"   Window End: {window_end}")
        
        # Update the session with new attendance window
        conn.execute('''
            UPDATE attendance_sessions 
            SET attendance_window_start = ?,
                attendance_window_end = ?,
                is_active = 1
            WHERE session_id = ?
        ''', (window_start.isoformat(), window_end.isoformat(), session_id))
        
        conn.commit()
        
        print(f"✅ Attendance window opened successfully!")
        print(f"   Students can now mark attendance for the next 30 minutes")
        print(f"   Window closes at: {window_end.strftime('%H:%M:%S')}")
        
        # Verify the update
        updated_session = conn.execute('''
            SELECT session_id, session_name, attendance_window_start, attendance_window_end, is_active
            FROM attendance_sessions
            WHERE session_id = ?
        ''', (session_id,)).fetchone()
        
        print(f"\n🔍 Verification:")
        print(f"   Session ID: {updated_session[0]}")
        print(f"   Session Name: {updated_session[1]}")
        print(f"   Window Start: {updated_session[2]}")
        print(f"   Window End: {updated_session[3]}")
        print(f"   Is Active: {updated_session[4]}")
        
    else:
        print("❌ No active sessions found to open attendance window")
        
        # Show available sessions
        all_sessions = conn.execute('''
            SELECT session_id, session_name, session_date, is_active, is_completed
            FROM attendance_sessions
            ORDER BY session_date DESC
        ''').fetchall()
        
        print(f"\n📋 Available sessions:")
        for s in all_sessions:
            print(f"   - Session {s[0]}: {s[1]} (Date: {s[2]}, Active: {s[3]}, Completed: {s[4]})")
    
    conn.close()

def create_test_session():
    """Create a new test session with immediate attendance window"""
    conn = sqlite3.connect('attendance_fixed.db')
    
    print("🆕 Creating new test session...")
    
    # Get a module and lecturer for testing
    module_lecturer = conn.execute('''
        SELECT m.module_id, ml.lecturer_id, m.module_code, m.module_name, u.username
        FROM modules m
        JOIN module_lecturers ml ON m.module_id = ml.module_id
        JOIN users u ON ml.lecturer_id = u.user_id
        LIMIT 1
    ''').fetchone()
    
    if module_lecturer:
        module_id, lecturer_id, module_code, module_name, lecturer_name = module_lecturer
        
        now = datetime.now()
        session_date = now.date()
        start_time = now.time()
        end_time = (now + timedelta(hours=2)).time()
        window_start = now
        window_end = now + timedelta(minutes=30)
        
        session_name = f"Test Session - {now.strftime('%H:%M')}"
        
        print(f"📝 Creating session:")
        print(f"   Module: {module_code} - {module_name}")
        print(f"   Lecturer: {lecturer_name}")
        print(f"   Session: {session_name}")
        print(f"   Date: {session_date}")
        print(f"   Attendance window: {window_start.strftime('%H:%M')} - {window_end.strftime('%H:%M')}")
        
        # Create the session
        cursor = conn.execute('''
            INSERT INTO attendance_sessions (
                module_id, lecturer_id, session_name, session_type, session_date,
                scheduled_start_time, scheduled_end_time, attendance_window_start,
                attendance_window_end, is_active, is_completed, location
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (module_id, lecturer_id, session_name, 'lecture', session_date,
              start_time, end_time, window_start.isoformat(), window_end.isoformat(),
              1, 0, 'Test Location'))
        
        session_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ Test session created successfully!")
        print(f"   Session ID: {session_id}")
        print(f"   Students can mark attendance now!")
        
    else:
        print("❌ No modules or lecturers found to create test session")
    
    conn.close()

if __name__ == "__main__":
    print("Choose an option:")
    print("1. Open attendance window for existing session")
    print("2. Create new test session with open window")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        open_attendance_window()
    elif choice == "2":
        create_test_session()
    else:
        print("Invalid choice. Opening window for existing session...")
        open_attendance_window()
