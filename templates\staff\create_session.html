{% extends "base.html" %}

{% block title %}Create Attendance Session - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-header text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>
                        Create Attendance Session
                    </h2>
                    <p class="mb-0 mt-2">Set up a new attendance session for your module</p>
                </div>
                <div class="card-body p-5">
                    <form method="POST" id="sessionForm">
                        <div class="row">
                            <!-- Module Selection -->
                            <div class="col-md-6 mb-4">
                                <label for="module_id" class="form-label">
                                    <i class="fas fa-book me-2"></i>Module
                                </label>
                                <select class="form-select" id="module_id" name="module_id" required>
                                    <option value="">Select a module...</option>
                                    {% for module in modules %}
                                    <option value="{{ module.module_id }}" 
                                            {% if request.args.get('module_id') == module.module_id|string %}selected{% endif %}>
                                        {{ module.module_code }} - {{ module.module_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Session Name -->
                            <div class="col-md-6 mb-4">
                                <label for="session_name" class="form-label">
                                    <i class="fas fa-tag me-2"></i>Session Name
                                </label>
                                <input type="text" class="form-control" id="session_name" name="session_name" 
                                       placeholder="e.g., Lecture 1, Tutorial 3, Lab Session" required>
                            </div>

                            <!-- Session Type -->
                            <div class="col-md-6 mb-4">
                                <label for="session_type" class="form-label">
                                    <i class="fas fa-chalkboard me-2"></i>Session Type
                                </label>
                                <select class="form-select" id="session_type" name="session_type" required>
                                    <option value="lecture">Lecture</option>
                                    <option value="tutorial">Tutorial</option>
                                    <option value="lab">Laboratory</option>
                                    <option value="practical">Practical</option>
                                    <option value="seminar">Seminar</option>
                                    <option value="exam">Exam</option>
                                </select>
                            </div>

                            <!-- Session Date -->
                            <div class="col-md-6 mb-4">
                                <label for="session_date" class="form-label">
                                    <i class="fas fa-calendar me-2"></i>Session Date
                                </label>
                                <input type="date" class="form-control" id="session_date" name="session_date" 
                                       min="{{ today }}" required>
                            </div>

                            <!-- Start Time -->
                            <div class="col-md-6 mb-4">
                                <label for="start_time" class="form-label">
                                    <i class="fas fa-clock me-2"></i>Start Time
                                </label>
                                <input type="time" class="form-control" id="start_time" name="start_time" required>
                            </div>

                            <!-- End Time -->
                            <div class="col-md-6 mb-4">
                                <label for="end_time" class="form-label">
                                    <i class="fas fa-clock me-2"></i>End Time
                                </label>
                                <input type="time" class="form-control" id="end_time" name="end_time" required>
                            </div>

                            <!-- Location -->
                            <div class="col-12 mb-4">
                                <label for="location" class="form-label">
                                    <i class="fas fa-map-marker-alt me-2"></i>Location (Optional)
                                </label>
                                <input type="text" class="form-control" id="location" name="location" 
                                       placeholder="e.g., Room 201, Lab A, Lecture Hall 1">
                            </div>
                        </div>

                        <!-- Information Box -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>How Attendance Sessions Work:</h6>
                            <ul class="mb-0">
                                <li>Create the session with scheduled times</li>
                                <li>Start the attendance window when class begins (2-minute window)</li>
                                <li>Students use facial recognition to mark attendance during the active window</li>
                                <li>Each attended session counts as 100% for that class</li>
                                <li>Final attendance contributes 10% to the module grade</li>
                            </ul>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row mt-4">
                            <div class="col-12 text-center">
                                <a href="{{ url_for('staff_dashboard') }}" class="btn btn-outline-secondary btn-lg me-3">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    Cancel
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save me-2"></i>
                                    Create Session
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Set default date to today
document.getElementById('session_date').value = new Date().toISOString().split('T')[0];

// Auto-generate session name based on module selection
document.getElementById('module_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        const moduleCode = selectedOption.text.split(' - ')[0];
        const sessionType = document.getElementById('session_type').value;
        const sessionName = `${moduleCode} ${sessionType.charAt(0).toUpperCase() + sessionType.slice(1)}`;
        document.getElementById('session_name').value = sessionName;
    }
});

// Update session name when session type changes
document.getElementById('session_type').addEventListener('change', function() {
    const moduleSelect = document.getElementById('module_id');
    const selectedOption = moduleSelect.options[moduleSelect.selectedIndex];
    if (selectedOption.value) {
        const moduleCode = selectedOption.text.split(' - ')[0];
        const sessionType = this.value;
        const sessionName = `${moduleCode} ${sessionType.charAt(0).toUpperCase() + sessionType.slice(1)}`;
        document.getElementById('session_name').value = sessionName;
    }
});

// Validate end time is after start time
document.getElementById('end_time').addEventListener('change', function() {
    const startTime = document.getElementById('start_time').value;
    const endTime = this.value;
    
    if (startTime && endTime && endTime <= startTime) {
        alert('End time must be after start time');
        this.value = '';
    }
});

// Form validation
document.getElementById('sessionForm').addEventListener('submit', function(e) {
    const startTime = document.getElementById('start_time').value;
    const endTime = document.getElementById('end_time').value;
    
    if (endTime <= startTime) {
        e.preventDefault();
        alert('End time must be after start time');
        return false;
    }
});
</script>

<style>
.card {
    border-radius: 15px;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.alert-info {
    background-color: #e7f3ff;
    border-color: #b8daff;
    color: #004085;
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
}
</style>
{% endblock %}
