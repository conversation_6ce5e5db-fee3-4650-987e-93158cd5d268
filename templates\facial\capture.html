{% extends "base.html" %}

{% block title %}Face Capture - Facial Recognition Setup{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-lg border-0">
                <div class="card-header text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-camera me-2"></i>
                        Face Capture
                    </h2>
                    <p class="mb-0 mt-2">Capture clear photos of your face for enrollment</p>
                </div>
                <div class="card-body p-5">
                    <!-- Progress Bar -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">Progress</span>
                            <span id="progressText">0 / 5 images captured</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div id="progressBar" class="progress-bar bg-success" role="progressbar" 
                                 style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>

                    <!-- Camera Section -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-body text-center">
                                    <div class="camera-container">
                                        <video id="video" width="100%" height="400" autoplay muted class="border rounded"></video>
                                        <canvas id="canvas" style="display: none;"></canvas>

                                        <!-- Face outline guide -->
                                        <div class="face-guide-overlay">
                                            <div class="face-outline">
                                                <div class="face-guide-text">Position your face here</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Camera Controls -->
                                    <div class="mt-3">
                                        <button id="startCamera" class="btn btn-primary btn-lg">
                                            <i class="fas fa-video me-2"></i>
                                            Start Camera
                                        </button>
                                        <button id="captureBtn" class="btn btn-success btn-lg d-none">
                                            <i class="fas fa-camera me-2"></i>
                                            Capture Photo
                                        </button>
                                        <button id="retakeBtn" class="btn btn-warning d-none">
                                            <i class="fas fa-redo me-2"></i>
                                            Retake
                                        </button>
                                    </div>
                                    
                                    <!-- Status Messages -->
                                    <div id="cameraStatus" class="mt-3"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Instructions and Captured Images -->
                        <div class="col-lg-4">
                            <!-- Instructions -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Instructions
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <ul class="small mb-0">
                                        <li class="mb-2">Look directly at the camera</li>
                                        <li class="mb-2">Ensure good lighting on your face</li>
                                        <li class="mb-2">Keep your face centered in the frame</li>
                                        <li class="mb-2">Capture from slightly different angles</li>
                                        <li class="mb-2">Avoid glasses or face coverings</li>
                                        <li class="mb-0">Maintain a neutral expression</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <!-- Captured Images -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-images me-2"></i>
                                        Captured Images
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="capturedImages" class="row g-2">
                                        <!-- Captured images will appear here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="/facial/setup" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Setup
                        </a>
                        
                        <button id="enrollBtn" class="btn btn-success btn-lg d-none">
                            <i class="fas fa-check me-2"></i>
                            Complete Enrollment
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tips Card -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-4">
                            <h6 class="mb-3">
                                <i class="fas fa-lightbulb text-warning me-2"></i>
                                Tips for Best Results
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="small mb-2"><strong>Lighting:</strong> Use natural light or bright indoor lighting</p>
                                    <p class="small mb-2"><strong>Position:</strong> Keep your face 2-3 feet from the camera</p>
                                    <p class="small mb-0"><strong>Background:</strong> Use a plain background if possible</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="small mb-2"><strong>Angles:</strong> Capture straight-on and slight side angles</p>
                                    <p class="small mb-2"><strong>Expression:</strong> Keep a neutral, relaxed expression</p>
                                    <p class="small mb-0"><strong>Quality:</strong> Ensure images are clear and not blurry</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let stream = null;
let capturedCount = 0;
const requiredImages = 5;

// DOM elements
const video = document.getElementById('video');
const canvas = document.getElementById('canvas');
const startCameraBtn = document.getElementById('startCamera');
const captureBtn = document.getElementById('captureBtn');
const retakeBtn = document.getElementById('retakeBtn');
const enrollBtn = document.getElementById('enrollBtn');
const progressBar = document.getElementById('progressBar');
const progressText = document.getElementById('progressText');
const cameraStatus = document.getElementById('cameraStatus');
const capturedImages = document.getElementById('capturedImages');

// Event listeners
startCameraBtn.addEventListener('click', startCamera);
captureBtn.addEventListener('click', capturePhoto);
retakeBtn.addEventListener('click', retakePhoto);
enrollBtn.addEventListener('click', completeEnrollment);

function startCamera() {
    navigator.mediaDevices.getUserMedia({ 
        video: { 
            width: { ideal: 640 }, 
            height: { ideal: 480 },
            facingMode: 'user'
        } 
    })
    .then(mediaStream => {
        stream = mediaStream;
        video.srcObject = stream;
        
        startCameraBtn.classList.add('d-none');
        captureBtn.classList.remove('d-none');
        
        cameraStatus.innerHTML = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                Camera started successfully! Position your face in the frame and click capture.
            </div>
        `;
    })
    .catch(error => {
        console.error('Error accessing camera:', error);
        cameraStatus.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Error accessing camera: ${error.message}
            </div>
        `;
    });
}

function capturePhoto() {
    if (!stream) return;
    
    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    // Draw current video frame to canvas (flip back to normal orientation for processing)
    const ctx = canvas.getContext('2d');
    ctx.save();
    ctx.scale(-1, 1);
    ctx.drawImage(video, -canvas.width, 0);
    ctx.restore();
    
    // Convert to base64
    const imageData = canvas.toDataURL('image/jpeg', 0.8);
    
    // Send to server
    fetch('/facial/api/capture_image', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ image: imageData })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            capturedCount = data.captured;
            updateProgress();
            addCapturedImage(imageData);
            
            if (data.complete) {
                captureBtn.classList.add('d-none');
                enrollBtn.classList.remove('d-none');
                
                cameraStatus.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        All images captured! Click "Complete Enrollment" to finish.
                    </div>
                `;
            } else {
                cameraStatus.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-camera me-2"></i>
                        Image ${data.captured} captured! ${data.required - data.captured} more needed.
                    </div>
                `;
            }
        } else {
            cameraStatus.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error: ${data.error}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error capturing image:', error);
        cameraStatus.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Error capturing image. Please try again.
            </div>
        `;
    });
}

function retakePhoto() {
    // Clear captured images and restart
    fetch('/facial/api/clear_images', { method: 'POST' })
        .then(() => {
            capturedCount = 0;
            updateProgress();
            capturedImages.innerHTML = '';
            
            captureBtn.classList.remove('d-none');
            enrollBtn.classList.add('d-none');
            retakeBtn.classList.add('d-none');
            
            cameraStatus.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Images cleared. Start capturing again.
                </div>
            `;
        });
}

function completeEnrollment() {
    enrollBtn.disabled = true;
    enrollBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    
    fetch('/facial/api/enroll', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            cameraStatus.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    ${data.message}
                </div>
            `;
            
            // Stop camera
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
            
            // Redirect after success
            setTimeout(() => {
                window.location.href = '/facial/setup';
            }, 2000);
        } else {
            cameraStatus.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error: ${data.error}
                </div>
            `;
            
            enrollBtn.disabled = false;
            enrollBtn.innerHTML = '<i class="fas fa-check me-2"></i>Complete Enrollment';
        }
    })
    .catch(error => {
        console.error('Error enrolling:', error);
        cameraStatus.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Error completing enrollment. Please try again.
            </div>
        `;
        
        enrollBtn.disabled = false;
        enrollBtn.innerHTML = '<i class="fas fa-check me-2"></i>Complete Enrollment';
    });
}

function updateProgress() {
    const percentage = (capturedCount / requiredImages) * 100;
    progressBar.style.width = percentage + '%';
    progressBar.setAttribute('aria-valuenow', percentage);
    progressText.textContent = `${capturedCount} / ${requiredImages} images captured`;
    
    if (capturedCount > 0) {
        retakeBtn.classList.remove('d-none');
    }
}

function addCapturedImage(imageData) {
    const imageDiv = document.createElement('div');
    imageDiv.className = 'col-6';
    imageDiv.innerHTML = `
        <div class="card">
            <img src="${imageData}" class="card-img-top" style="height: 80px; object-fit: cover;">
            <div class="card-body p-1 text-center">
                <small class="text-muted">Image ${capturedCount}</small>
            </div>
        </div>
    `;
    capturedImages.appendChild(imageDiv);
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (stream) {
        stream.getTracks().forEach(track => track.stop());
    }
});
</script>

<style>
#video {
    transform: scaleX(-1); /* Flip the video horizontally to mirror the user */
}

.camera-container {
    position: relative;
    display: inline-block;
}

.face-guide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.face-outline {
    width: 300px;
    height: 380px;
    border: 3px solid rgba(0, 123, 255, 0.8);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    position: relative;
    background: rgba(0, 123, 255, 0.1);
    animation: pulse-guide 2s ease-in-out infinite;
}

.face-guide-text {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(0, 123, 255, 0.9);
    font-size: 14px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
    white-space: nowrap;
}

@keyframes pulse-guide {
    0% {
        border-color: rgba(0, 123, 255, 0.8);
        background: rgba(0, 123, 255, 0.1);
    }
    50% {
        border-color: rgba(0, 123, 255, 1);
        background: rgba(0, 123, 255, 0.2);
    }
    100% {
        border-color: rgba(0, 123, 255, 0.8);
        background: rgba(0, 123, 255, 0.1);
    }
}
</style>
{% endblock %}
