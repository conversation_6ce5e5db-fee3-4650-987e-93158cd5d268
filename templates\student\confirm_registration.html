{% extends "base.html" %}

{% block title %}Student Registration - Confirmation{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Progress Steps -->
            <div class="progress-step mb-4">
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Basic Info</small>
                </div>
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Faculty</small>
                </div>
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Course</small>
                </div>
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Year</small>
                </div>
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Modules</small>
                </div>
                <div class="step active">
                    <div class="step-number">6</div>
                    <small>Confirm</small>
                </div>
            </div>

            <!-- Confirmation -->
            <div class="card shadow-lg border-0">
                <div class="card-header text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        Confirm Your Registration
                    </h2>
                    <p class="mb-0 mt-2">Please review your information before completing registration</p>
                </div>
                <div class="card-body p-5">
                    <!-- Student Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="mb-3">
                                <i class="fas fa-user me-2"></i>
                                Personal Information
                            </h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Student Number:</strong></td>
                                    <td>{{ registration.student_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ registration.first_name }} {{ registration.last_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ registration.email }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5 class="mb-3">
                                <i class="fas fa-graduation-cap me-2"></i>
                                Academic Information
                            </h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Faculty:</strong></td>
                                    <td>{{ summary.faculty_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Course:</strong></td>
                                    <td>{{ summary.course_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Academic Year:</strong></td>
                                    <td>{{ summary.year_name }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Selected Modules -->
                    <div class="mb-4">
                        <h5 class="mb-3">
                            <i class="fas fa-book me-2"></i>
                            Selected Modules ({{ modules|length }})
                        </h5>
                        <div class="row g-3">
                            {% for module in modules %}
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-body p-3">
                                        <h6 class="card-title mb-1">{{ module.module_name }}</h6>
                                        <p class="text-muted small mb-2">{{ module.module_code }}</p>
                                        <div class="d-flex gap-2">
                                            <span class="badge bg-info">{{ module.credits }} Credits</span>
                                            <span class="badge bg-secondary">{{ module.semester }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Total Credits -->
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <i class="fas fa-calculator me-2"></i>
                                <strong>Total Credits:</strong> {{ modules|sum(attribute='credits') }} credits
                            </div>
                        </div>
                    </div>

                    <!-- Next Steps -->
                    <div class="card bg-light border-0 mb-4">
                        <div class="card-body">
                            <h6 class="mb-3">
                                <i class="fas fa-list-check me-2"></i>
                                What happens next?
                            </h6>
                            <ol class="mb-0">
                                <li class="mb-2">Your registration will be saved to the system</li>
                                <li class="mb-2">You'll be redirected to set up facial recognition</li>
                                <li class="mb-2">Complete biometric enrollment for attendance tracking</li>
                                <li class="mb-0">Start attending classes with automatic attendance</li>
                            </ol>
                        </div>
                    </div>

                    <!-- Confirmation Form -->
                    <form method="POST" action="{{ url_for('student_complete_registration') }}">
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirm_info" required>
                                <label class="form-check-label" for="confirm_info">
                                    I confirm that all the information above is correct and accurate.
                                </label>
                            </div>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" id="agree_terms" required>
                                <label class="form-check-label" for="agree_terms">
                                    I agree to proceed with facial recognition enrollment and understand 
                                    that my biometric data will be securely stored for attendance purposes.
                                </label>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="javascript:history.back()" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Modules
                            </a>
                            
                            <button type="submit" class="btn btn-success btn-lg" id="completeBtn">
                                <i class="fas fa-check me-2"></i>
                                Complete Registration
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Notice -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-0 bg-warning bg-opacity-10">
                        <div class="card-body p-4">
                            <h6 class="mb-2">
                                <i class="fas fa-shield-alt text-warning me-2"></i>
                                Privacy & Security Notice
                            </h6>
                            <p class="small mb-0">
                                Your personal and biometric data will be encrypted and stored securely. 
                                We comply with all data protection regulations and your information will 
                                only be used for attendance tracking and academic purposes. You have the 
                                right to request deletion of your data at any time.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[action*="complete_registration"]');
    const completeBtn = document.getElementById('completeBtn');

    if (form && completeBtn) {
        completeBtn.addEventListener('click', function(e) {
            const confirmInfo = document.getElementById('confirm_info').checked;
            const agreeTerms = document.getElementById('agree_terms').checked;

            if (!confirmInfo || !agreeTerms) {
                e.preventDefault();
                alert('Please confirm all checkboxes before proceeding.');
                return false;
            }

            // Show loading state immediately
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Completing Registration...';
                this.disabled = true;
            }, 100);

            // Form will submit naturally since this is a submit button
        });
    }
});
</script>
{% endblock %}
