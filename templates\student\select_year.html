{% extends "base.html" %}

{% block title %}Student Registration - Step 4: Academic Year Selection{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Progress Steps -->
            <div class="progress-step mb-4">
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Basic Info</small>
                </div>
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Faculty</small>
                </div>
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Course</small>
                </div>
                <div class="step active">
                    <div class="step-number">4</div>
                    <div class="step-line"></div>
                    <small>Year</small>
                </div>
                <div class="step">
                    <div class="step-number">5</div>
                    <div class="step-line"></div>
                    <small>Modules</small>
                </div>
                <div class="step">
                    <div class="step-number">6</div>
                    <small>Facial Setup</small>
                </div>
            </div>

            <!-- Year Selection -->
            <div class="card shadow-lg border-0">
                <div class="card-header text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Select Your Academic Year
                    </h2>
                    <p class="mb-0 mt-2">Step 4: Choose your current year in {{ course.course_name }}</p>
                </div>
                <div class="card-body p-5">
                    {% if years %}
                        <div class="row g-4 justify-content-center">
                            {% for year in years %}
                            <div class="col-md-4 col-sm-6">
                                <div class="card h-100 year-card border-2" style="cursor: pointer;" 
                                     onclick="selectYear({{ year.course_year_id }})">
                                    <div class="card-body text-center p-4">
                                        <div class="mb-3">
                                            {% if year.year_number == 1 %}
                                                <i class="fas fa-seedling text-success" style="font-size: 3rem;"></i>
                                            {% elif year.year_number == 2 %}
                                                <i class="fas fa-tree text-info" style="font-size: 3rem;"></i>
                                            {% elif year.year_number == 3 %}
                                                <i class="fas fa-graduation-cap text-warning" style="font-size: 3rem;"></i>
                                            {% else %}
                                                <i class="fas fa-star text-primary" style="font-size: 3rem;"></i>
                                            {% endif %}
                                        </div>
                                        
                                        <h4 class="card-title mb-2">{{ year.year_name or ('Year ' + year.year_number|string) }}</h4>
                                        
                                        <p class="text-muted small mb-3">
                                            Academic Year {{ year.year_number }}
                                        </p>
                                        
                                        {% if year.year_number == 1 %}
                                            <p class="card-text small mb-3">
                                                Foundation year with core subjects and introduction to your field of study.
                                            </p>
                                        {% elif year.year_number == 2 %}
                                            <p class="card-text small mb-3">
                                                Intermediate level with specialized subjects and practical applications.
                                            </p>
                                        {% elif year.year_number == 3 %}
                                            <p class="card-text small mb-3">
                                                Advanced studies, projects, and preparation for graduation.
                                            </p>
                                        {% endif %}
                                        
                                        <div class="mt-auto">
                                            <span class="btn btn-outline-primary btn-sm w-100">
                                                <i class="fas fa-arrow-right me-1"></i>
                                                Select Year {{ year.year_number }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                            <h4 class="mt-3">No Academic Years Available</h4>
                            <p class="text-muted">
                                No academic years are currently available for this course. 
                                Please contact the administration for assistance.
                            </p>
                            <a href="{{ url_for('student_select_faculty') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Choose Different Course
                            </a>
                        </div>
                    {% endif %}

                    <!-- Navigation Buttons -->
                    <div class="d-flex justify-content-between mt-5">
                        <a href="javascript:history.back()" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Course Selection
                        </a>
                        
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                Click on a year card to continue
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Course Information -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-4">
                            <h6 class="mb-3">
                                <i class="fas fa-info-circle text-info me-2"></i>
                                Course Information
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-2">
                                        <strong>Course:</strong> {{ course.course_name }}
                                    </p>
                                    <p class="mb-2">
                                        <strong>Duration:</strong> {{ course.duration_years }} years
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-2">
                                        <strong>Note:</strong> Each year has different modules and requirements.
                                    </p>
                                    <p class="mb-0">
                                        <strong>Next:</strong> You'll select your modules for the chosen year.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.year-card {
    transition: all 0.3s ease;
    border-color: #e9ecef !important;
}

.year-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    border-color: #667eea !important;
}

.year-card:hover .card-title {
    color: #667eea;
}

.year-card:hover .btn-outline-primary {
    background-color: #667eea;
    color: white;
    border-color: #667eea;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function selectYear(courseYearId) {
    // Add loading state
    const cards = document.querySelectorAll('.year-card');
    cards.forEach(card => {
        card.style.opacity = '0.6';
        card.style.pointerEvents = 'none';
    });
    
    // Show loading message
    const selectedCard = event.currentTarget;
    const btn = selectedCard.querySelector('.btn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Loading...';
    btn.classList.remove('btn-outline-primary');
    btn.classList.add('btn-primary');
    
    // Navigate to module selection
    setTimeout(() => {
        window.location.href = `/student/select_modules/${courseYearId}`;
    }, 500);
}

// Add keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        history.back();
    }
});
</script>
{% endblock %}
