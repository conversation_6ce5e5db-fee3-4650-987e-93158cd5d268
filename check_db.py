import sqlite3

# Connect to database
conn = sqlite3.connect('attendance_fixed.db')
cursor = conn.cursor()

# Get all tables
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()

print("Tables in database:")
for table in tables:
    print(f"- {table[0]}")

# Check specifically for attendance_sessions
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='attendance_sessions';")
result = cursor.fetchone()

if result:
    print("\n✅ attendance_sessions table EXISTS")
    
    # Get table schema
    cursor.execute("PRAGMA table_info(attendance_sessions);")
    columns = cursor.fetchall()
    print("\nColumns in attendance_sessions:")
    for col in columns:
        print(f"  - {col[1]} ({col[2]})")
else:
    print("\n❌ attendance_sessions table NOT FOUND")

conn.close()
