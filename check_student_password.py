import sqlite3
from flask_bcrypt import Bcrypt

def check_student_password():
    """Check what password is set for student 22302925"""
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    student = conn.execute('''
        SELECT student_id, student_number, first_name, last_name, password_hash
        FROM students
        WHERE student_number = ?
    ''', ('22302925',)).fetchone()
    
    if student:
        print(f"Student found: {student['first_name']} {student['last_name']}")
        print(f"Student ID: {student['student_id']}")
        print(f"Student Number: {student['student_number']}")
        print(f"Password Hash: {student['password_hash']}")
        
        # Test common passwords
        bcrypt = Bcrypt()
        test_passwords = ['password123', 'student123', '123456', 'password', student['student_number']]
        
        for pwd in test_passwords:
            if student['password_hash'] and bcrypt.check_password_hash(student['password_hash'], pwd):
                print(f"✅ Password found: '{pwd}'")
                return pwd
        
        print("❌ None of the test passwords worked")
        return None
    else:
        print("❌ Student not found")
        return None
    
    conn.close()

if __name__ == "__main__":
    check_student_password()
