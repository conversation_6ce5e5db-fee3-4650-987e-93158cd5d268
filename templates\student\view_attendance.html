{% extends "base.html" %}

{% block title %}Attendance Records - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1">Attendance Records & Marks</h1>
                    <p class="text-muted mb-0">View your attendance history and grade contributions</p>
                </div>
                <div>
                    <a href="{{ url_for('student_attendance') }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-calendar-check me-2"></i>Mark Attendance
                    </a>
                    <a href="{{ url_for('student_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Overall Statistics -->
    <div class="row g-4 mb-5">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-calendar-alt text-primary fs-1 mb-2"></i>
                    <h4 class="mb-1">{{ total_sessions }}</h4>
                    <p class="text-muted small mb-0">Total Sessions</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-user-check text-success fs-1 mb-2"></i>
                    <h4 class="mb-1">{{ attended_sessions }}</h4>
                    <p class="text-muted small mb-0">Attended</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-percentage text-info fs-1 mb-2"></i>
                    <h4 class="mb-1">{{ "%.1f"|format(overall_percentage) }}%</h4>
                    <p class="text-muted small mb-0">Attendance Rate</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-star text-warning fs-1 mb-2"></i>
                    <h4 class="mb-1">{{ "%.2f"|format(overall_contribution) }}%</h4>
                    <p class="text-muted small mb-0">Grade Contribution</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Module Summary -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Attendance Summary by Module
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if attendance_summary %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Module</th>
                                    <th>Course</th>
                                    <th>Semester</th>
                                    <th>Sessions</th>
                                    <th>Attended</th>
                                    <th>Attendance %</th>
                                    <th>Grade Contribution</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for module in attendance_summary %}
                                <tr>
                                    <td>
                                        <strong>{{ module.module_code }}</strong><br>
                                        <small class="text-muted">{{ module.module_name }}</small>
                                    </td>
                                    <td>{{ module.course_name }} (Year {{ module.year_number }})</td>
                                    <td>{{ module.semester }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ module.total_sessions }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ module.attended_sessions }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="progress me-2" style="width: 60px; height: 20px;">
                                                <div class="progress-bar 
                                                    {% if module.attendance_percentage >= 80 %}bg-success
                                                    {% elif module.attendance_percentage >= 60 %}bg-warning
                                                    {% else %}bg-danger{% endif %}" 
                                                    style="width: {{ module.attendance_percentage }}%"></div>
                                            </div>
                                            <span class="small">{{ "%.1f"|format(module.attendance_percentage) }}%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ "%.2f"|format(module.attendance_contribution) }}%</strong>
                                    </td>
                                    <td>
                                        {% if module.attendance_percentage >= 80 %}
                                        <span class="badge bg-success">Excellent</span>
                                        {% elif module.attendance_percentage >= 60 %}
                                        <span class="badge bg-warning">Good</span>
                                        {% elif module.attendance_percentage >= 40 %}
                                        <span class="badge bg-danger">Poor</span>
                                        {% else %}
                                        <span class="badge bg-dark">Critical</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3 text-muted">No Modules Found</h5>
                        <p class="text-muted">You are not enrolled in any modules yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Records -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        Detailed Attendance Records
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if detailed_records %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Session</th>
                                    <th>Module</th>
                                    <th>Type</th>
                                    <th>Location</th>
                                    <th>Time Marked</th>
                                    <th>Confidence</th>
                                    <th>Mark</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in detailed_records %}
                                <tr class="{% if not record.marked_at %}table-warning{% endif %}">
                                    <td>{{ record.session_date }}</td>
                                    <td>{{ record.session_name }}</td>
                                    <td>
                                        <strong>{{ record.module_code }}</strong><br>
                                        <small class="text-muted">{{ record.module_name }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ record.session_type|title }}</span>
                                    </td>
                                    <td>{{ record.location or '-' }}</td>
                                    <td>
                                        {% if record.marked_at %}
                                        {{ record.marked_at }}
                                        {% else %}
                                        <span class="text-muted">Not marked</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if record.confidence_score %}
                                        <span class="badge bg-success">
                                            {{ (record.confidence_score * 100)|round(1) }}%
                                        </span>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong class="{% if record.session_mark > 0 %}text-success{% else %}text-danger{% endif %}">
                                            {{ "%.0f"|format(record.session_mark) }}%
                                        </strong>
                                    </td>
                                    <td>
                                        {% if record.marked_at %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Present
                                        </span>
                                        {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>Absent
                                        </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-clipboard-list text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3 text-muted">No Attendance Records</h5>
                        <p class="text-muted">No completed attendance sessions found for your modules.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Information Panel -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>Understanding Your Attendance Marks:</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="mb-0">
                            <li><strong>Session Mark:</strong> 100% for attended sessions, 0% for missed sessions</li>
                            <li><strong>Attendance %:</strong> Percentage of sessions attended per module</li>
                            <li><strong>Grade Contribution:</strong> Your attendance percentage × 10% (attendance weight)</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="mb-0">
                            <li><strong>Excellent:</strong> 80%+ attendance (8%+ grade contribution)</li>
                            <li><strong>Good:</strong> 60-79% attendance (6-7.9% grade contribution)</li>
                            <li><strong>Poor/Critical:</strong> Below 60% attendance (less than 6% grade contribution)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.fs-1 {
    font-size: 3rem !important;
}

.progress {
    border-radius: 10px;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.table-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.badge {
    font-size: 0.8rem;
}

.alert-info {
    background-color: #e7f3ff;
    border-color: #b8daff;
    color: #004085;
}
</style>
{% endblock %}
