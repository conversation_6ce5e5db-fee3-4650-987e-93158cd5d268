import os
import re

def fix_strftime_in_templates():
    """Fix all strftime errors in templates"""
    
    # Define the replacements
    replacements = [
        # Simple cases - just remove .strftime() calls
        (r'\.strftime\([\'"][^\'\"]*[\'\"]\)', ''),
        
        # Complex cases with conditionals
        (r'(\w+)\.strftime\([\'"][^\'\"]*[\'\"]\)\s+if\s+\1\.strftime\s+else\s+(\w+)', r'\1 if \1 else \2'),
        (r'(\w+)\.strftime\([\'"][^\'\"]*[\'\"]\)\s+if\s+\1\s+else\s+[\'"]([^\'"]*)[\'"]', r'\1 if \1 else "\2"'),
    ]
    
    # Find all HTML files in templates directory
    template_files = []
    for root, dirs, files in os.walk('templates'):
        for file in files:
            if file.endswith('.html'):
                template_files.append(os.path.join(root, file))
    
    print(f"Found {len(template_files)} template files")
    
    for file_path in template_files:
        print(f"\nProcessing: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Apply replacements
            for pattern, replacement in replacements:
                content = re.sub(pattern, replacement, content)
            
            # Manual fixes for specific patterns
            content = content.replace('.strftime(', '(')
            content = re.sub(r'(\w+)\.strftime\([\'"][^\'\"]*[\'\"]\)', r'\1', content)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ Fixed strftime errors")
            else:
                print(f"  ⚪ No changes needed")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")

if __name__ == "__main__":
    fix_strftime_in_templates()
