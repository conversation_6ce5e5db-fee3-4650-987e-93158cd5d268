{% extends "base.html" %}

{% block title %}Monitor Attendance Session - {{ session.session_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Session Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">{{ session.session_name }}</h2>
                            <p class="text-muted mb-2">{{ session.module_code }} - {{ session.module_name }}</p>
                            <div class="d-flex gap-3">
                                <span class="badge bg-info">{{ session.session_type|title }}</span>
                                <span class="badge bg-secondary">{{ session.session_date }}</span>
                                <span class="badge bg-primary">{{ session.scheduled_start_time }} - {{ session.scheduled_end_time }}</span>
                                {% if session.location %}
                                <span class="badge bg-warning">{{ session.location }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            {% if session.is_active %}
                            <div class="mb-2">
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-circle me-1"></i>ACTIVE
                                </span>
                            </div>
                            <div id="countdown" class="text-danger fw-bold fs-5"></div>
                            <a href="{{ url_for('stop_attendance_session', session_id=session.session_id) }}" 
                               class="btn btn-danger btn-sm mt-2">
                                <i class="fas fa-stop me-1"></i>Stop Session
                            </a>
                            {% else %}
                            <span class="badge bg-secondary fs-6">INACTIVE</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-users text-primary fs-1 mb-2"></i>
                    <h4 class="mb-1">{{ enrolled_students|length }}</h4>
                    <p class="text-muted small mb-0">Enrolled Students</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-user-check text-success fs-1 mb-2"></i>
                    <h4 class="mb-1">{{ attendance_records|length }}</h4>
                    <p class="text-muted small mb-0">Present</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-user-times text-danger fs-1 mb-2"></i>
                    <h4 class="mb-1">{{ enrolled_students|length - attendance_records|length }}</h4>
                    <p class="text-muted small mb-0">Absent</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-percentage text-info fs-1 mb-2"></i>
                    <h4 class="mb-1">
                        {% if enrolled_students|length > 0 %}
                        {{ ((attendance_records|length / enrolled_students|length) * 100)|round(1) }}%
                        {% else %}
                        0%
                        {% endif %}
                    </h4>
                    <p class="text-muted small mb-0">Attendance Rate</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Records -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        Students Present ({{ attendance_records|length }})
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if attendance_records %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Student Number</th>
                                    <th>Name</th>
                                    <th>Time Marked</th>
                                    <th>Confidence</th>
                                    <th>Method</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in attendance_records %}
                                <tr>
                                    <td>{{ record.student_number }}</td>
                                    <td>{{ record.first_name }} {{ record.last_name }}</td>
                                    <td>{{ record.marked_at if record.marked_at else 'N/A' }}</td>
                                    <td>
                                        <span class="badge bg-success">
                                            {{ (record.confidence_score * 100)|round(1) }}%
                                        </span>
                                    </td>
                                    <td>
                                        <i class="fas fa-camera text-primary me-1"></i>
                                        {{ record.recognition_method|title }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-user-clock text-muted fs-1 mb-3"></i>
                        <p class="text-muted">No students have marked attendance yet.</p>
                        {% if session.is_active %}
                        <p class="text-info">Attendance window is active - students can mark attendance now.</p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Students Absent ({{ enrolled_students|length - attendance_records|length }})
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% set present_student_ids = attendance_records|map(attribute='student_id')|list %}
                    {% set absent_students = enrolled_students|rejectattr('student_id', 'in', present_student_ids)|list %}
                    
                    {% if absent_students %}
                    <div class="list-group list-group-flush">
                        {% for student in absent_students %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ student.first_name }} {{ student.last_name }}</h6>
                                    <small class="text-muted">{{ student.student_number }}</small>
                                </div>
                                <span class="badge bg-danger">Absent</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle text-success fs-1 mb-3"></i>
                        <p class="text-success mb-0">All students are present!</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="{{ url_for('staff_dashboard') }}" class="btn btn-outline-secondary btn-lg me-3">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Dashboard
            </a>
            {% if not session.is_completed %}
            <a href="{{ url_for('stop_attendance_session', session_id=session.session_id) }}" 
               class="btn btn-danger btn-lg">
                <i class="fas fa-stop me-2"></i>
                End Session
            </a>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Auto-refresh page every 5 seconds if session is active
{% if session.is_active %}
setInterval(function() {
    location.reload();
}, 5000);

// Countdown timer
function updateCountdown() {
    const endTime = new Date('{{ session.attendance_window_end }}').getTime();
    const now = new Date().getTime();
    const timeLeft = endTime - now;
    
    if (timeLeft > 0) {
        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
        
        document.getElementById('countdown').innerHTML = 
            `${minutes}:${seconds.toString().padStart(2, '0')} remaining`;
    } else {
        document.getElementById('countdown').innerHTML = 'Session Expired';
    }
}

// Update countdown every second
setInterval(updateCountdown, 1000);
updateCountdown();
{% endif %}
</script>

<style>
.card {
    transition: transform 0.2s;
}

.fs-1 {
    font-size: 3rem !important;
}

.badge {
    font-size: 0.8rem;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}
</style>
{% endblock %}
