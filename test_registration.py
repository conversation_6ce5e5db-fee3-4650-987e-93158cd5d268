"""
Test script to debug the registration flow
"""

import requests
import json

# Base URL
BASE_URL = "http://127.0.0.1:5000"

def test_registration_flow():
    """Test the complete registration flow"""
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    print("Testing Student Registration Flow...")
    print("=" * 50)
    
    # Step 1: Get registration page
    print("1. Loading registration page...")
    response = session.get(f"{BASE_URL}/student/register")
    print(f"   Status: {response.status_code}")
    
    # Step 2: Submit registration form
    print("2. Submitting registration form...")
    registration_data = {
        'student_number': 'CS20240001',
        'first_name': 'Test',
        'last_name': 'Student',
        'email': '<EMAIL>',
        'password': 'testpassword123',
        'confirm_password': 'testpassword123'
    }
    
    response = session.post(f"{BASE_URL}/student/register", data=registration_data)
    print(f"   Status: {response.status_code}")
    print(f"   Redirected to: {response.url}")
    
    # Step 3: Select faculty
    print("3. Loading faculty selection...")
    response = session.get(f"{BASE_URL}/student/select_faculty")
    print(f"   Status: {response.status_code}")
    
    # Step 4: Select course (Engineering faculty = ID 1)
    print("4. Selecting course in Engineering faculty...")
    response = session.get(f"{BASE_URL}/student/select_course/1")
    print(f"   Status: {response.status_code}")
    
    # Step 5: Select year (Computer Science course = ID 1, Year 1 = course_year_id 1)
    print("5. Selecting academic year...")
    response = session.get(f"{BASE_URL}/student/select_year/1")
    print(f"   Status: {response.status_code}")
    
    # Step 6: Select modules
    print("6. Loading module selection...")
    response = session.get(f"{BASE_URL}/student/select_modules/1")
    print(f"   Status: {response.status_code}")
    
    # Step 7: Confirm registration
    print("7. Confirming registration with modules...")
    module_data = {
        'modules': ['1', '2', '3']  # Select first 3 modules
    }
    response = session.post(f"{BASE_URL}/student/confirm_registration", data=module_data)
    print(f"   Status: {response.status_code}")
    print(f"   Final URL: {response.url}")
    
    if response.status_code == 200:
        print("✅ Registration flow completed successfully!")
    else:
        print("❌ Registration flow failed!")
        print(f"   Response content: {response.text[:500]}...")

def test_database_content():
    """Check what's in the database"""
    import sqlite3
    
    print("\nDatabase Content Check...")
    print("=" * 50)
    
    conn = sqlite3.connect('attendance_new.db')
    conn.row_factory = sqlite3.Row
    
    # Check faculties
    faculties = conn.execute("SELECT * FROM faculties").fetchall()
    print(f"Faculties: {len(faculties)}")
    for faculty in faculties:
        print(f"  - {faculty['faculty_id']}: {faculty['faculty_name']} ({faculty['faculty_code']})")
    
    # Check courses
    courses = conn.execute("SELECT * FROM courses").fetchall()
    print(f"\nCourses: {len(courses)}")
    for course in courses:
        print(f"  - {course['course_id']}: {course['course_name']} (Faculty: {course['faculty_id']})")
    
    # Check course years
    course_years = conn.execute("SELECT * FROM course_years").fetchall()
    print(f"\nCourse Years: {len(course_years)}")
    for year in course_years[:5]:  # Show first 5
        print(f"  - {year['course_year_id']}: Course {year['course_id']}, Year {year['year_number']}")
    
    # Check modules
    modules = conn.execute("SELECT * FROM modules LIMIT 10").fetchall()
    print(f"\nModules (first 10): {len(modules)}")
    for module in modules:
        print(f"  - {module['module_id']}: {module['module_name']} (Year: {module['course_year_id']})")
    
    conn.close()

if __name__ == "__main__":
    # First check database content
    test_database_content()
    
    # Then test registration flow
    test_registration_flow()
