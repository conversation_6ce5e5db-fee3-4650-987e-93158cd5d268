#!/usr/bin/env python3
"""
Debug face recognition confidence issues
"""

import sqlite3
import json
import numpy as np
import cv2

def debug_face_recognition():
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    print("🔍 Debugging face recognition confidence...")
    
    # Get face encodings for student 1 (22302925)
    student_id = 1
    encodings = conn.execute("""
        SELECT encoding_id, face_encoding, created_at
        FROM student_face_encodings
        WHERE student_id = ?
        ORDER BY created_at DESC
    """, (student_id,)).fetchall()
    
    print(f"📊 Found {len(encodings)} face encodings for student {student_id}:")
    
    if len(encodings) == 0:
        print("❌ No face encodings found! Student needs to complete facial setup first.")
        return
    
    # Analyze the encodings
    for i, encoding in enumerate(encodings):
        try:
            face_data = json.loads(encoding['face_encoding'])
            print(f"   Encoding {i+1}: {len(face_data)} elements, created: {encoding['created_at']}")
            
            # Convert to numpy array and analyze
            face_array = np.array(face_data)
            print(f"     Min: {face_array.min():.2f}, Max: {face_array.max():.2f}, Mean: {face_array.mean():.2f}")
            
        except Exception as e:
            print(f"   ❌ Error parsing encoding {i+1}: {e}")
    
    # Test self-comparison (should be 1.0)
    if len(encodings) >= 1:
        print(f"\n🧪 Testing self-comparison...")
        
        try:
            face_data = json.loads(encodings[0]['face_encoding'])
            face_array = np.array(face_data, dtype=np.float32)
            
            # Test comparing the encoding with itself
            similarity = cv2.matchTemplate(
                face_array.reshape(100, 100),
                face_array.reshape(100, 100),
                cv2.TM_CCOEFF_NORMED
            )[0][0]
            
            print(f"   Self-comparison confidence: {similarity:.4f}")
            
            if similarity < 0.99:
                print(f"   ⚠️  Self-comparison should be ~1.0, but got {similarity:.4f}")
            else:
                print(f"   ✅ Self-comparison looks good!")
                
        except Exception as e:
            print(f"   ❌ Self-comparison failed: {e}")
    
    # Test comparison between different encodings
    if len(encodings) >= 2:
        print(f"\n🔄 Testing comparison between different encodings...")
        
        try:
            face1 = np.array(json.loads(encodings[0]['face_encoding']), dtype=np.float32)
            face2 = np.array(json.loads(encodings[1]['face_encoding']), dtype=np.float32)
            
            similarity = cv2.matchTemplate(
                face1.reshape(100, 100),
                face2.reshape(100, 100),
                cv2.TM_CCOEFF_NORMED
            )[0][0]
            
            print(f"   Cross-comparison confidence: {similarity:.4f}")
            
            if similarity < 0.7:
                print(f"   ⚠️  Cross-comparison is below threshold (0.7): {similarity:.4f}")
                print(f"   This suggests the facial recognition method might be too strict.")
            else:
                print(f"   ✅ Cross-comparison passes threshold!")
                
        except Exception as e:
            print(f"   ❌ Cross-comparison failed: {e}")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    print(f"   1. Current threshold: 0.7 (70%)")
    print(f"   2. If self-comparison is good but live scanning fails:")
    print(f"      - Lighting conditions might be different")
    print(f"      - Camera angle might be different") 
    print(f"      - Face detection might be inconsistent")
    print(f"   3. Consider lowering threshold to 0.5 (50%) for testing")
    print(f"   4. Or implement a more robust facial recognition method")
    
    conn.close()

def suggest_fixes():
    print(f"\n🔧 Suggested fixes:")
    print(f"   1. TEMPORARY: Lower the confidence threshold for testing")
    print(f"   2. BETTER: Improve the facial recognition method")
    print(f"   3. BEST: Use a proper facial recognition library like face_recognition")
    
    print(f"\n📝 Quick fix options:")
    print(f"   A. Change threshold from 0.7 to 0.5 in app.py line 1757")
    print(f"   B. Add debug output to see actual confidence scores")
    print(f"   C. Re-register face with better lighting/angle")

if __name__ == "__main__":
    debug_face_recognition()
    suggest_fixes()
