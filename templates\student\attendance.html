{% extends "base.html" %}

{% block title %}Attendance - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1">Attendance</h1>
                    <p class="text-muted mb-0">Mark your attendance and view your records</p>
                </div>
                <div>
                    <a href="{{ url_for('view_attendance_records') }}" class="btn btn-outline-info me-2">
                        <i class="fas fa-chart-bar me-2"></i>View Records
                    </a>
                    <a href="{{ url_for('student_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Sessions -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Active Attendance Sessions
                    </h5>
                </div>
                <div class="card-body">
                    {% if active_sessions %}
                    <div class="row g-4">
                        {% for session in active_sessions %}
                        <div class="col-md-6 col-lg-4">
                            <div class="card border-0 shadow-sm h-100 {% if session.already_marked %}border-success{% else %}border-warning{% endif %}">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <h6 class="card-title mb-0">{{ session.session_name }}</h6>
                                        {% if session.already_marked %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Marked
                                        </span>
                                        {% else %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>Active
                                        </span>
                                        {% endif %}
                                    </div>
                                    
                                    <p class="text-muted small mb-2">
                                        <i class="fas fa-book me-1"></i>
                                        {{ session.module_code }} - {{ session.module_name }}
                                    </p>
                                    
                                    <p class="text-muted small mb-2">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ session.session_date }}
                                    </p>
                                    
                                    {% if session.location %}
                                    <p class="text-muted small mb-3">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ session.location }}
                                    </p>
                                    {% endif %}
                                    
                                    <div class="countdown-timer text-danger fw-bold mb-3" 
                                         data-end-time="{{ session.attendance_window_end }}">
                                        Calculating...
                                    </div>
                                    
                                    {% if session.already_marked %}
                                    <button class="btn btn-success btn-sm w-100" disabled>
                                        <i class="fas fa-check me-2"></i>
                                        Attendance Marked
                                    </button>
                                    {% else %}
                                    <a href="{{ url_for('mark_attendance_page', session_id=session.session_id) }}" 
                                       class="btn btn-primary btn-sm w-100">
                                        <i class="fas fa-camera me-2"></i>
                                        Mark Attendance
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3 text-muted">No Active Sessions</h5>
                        <p class="text-muted">There are currently no active attendance sessions for your modules.</p>
                        <p class="text-info small">
                            <i class="fas fa-info-circle me-1"></i>
                            Attendance sessions are typically opened by lecturers at the start of class for a 2-minute window.
                        </p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Attendance History -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Recent Attendance History
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_attendance %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Session</th>
                                    <th>Module</th>
                                    <th>Date</th>
                                    <th>Time Marked</th>
                                    <th>Confidence</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in recent_attendance %}
                                <tr>
                                    <td>{{ record.session_name }}</td>
                                    <td>{{ record.module_code }} - {{ record.module_name }}</td>
                                    <td>{{ record.session_date }}</td>
                                    <td>{{ record.marked_at if record.marked_at else 'N/A' }}</td>
                                    <td>
                                        <span class="badge bg-success">
                                            {{ (record.confidence_score * 100)|round(1) }}%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Present
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list text-muted" style="font-size: 3rem;"></i>
                        <h6 class="mt-3 text-muted">No Attendance Records</h6>
                        <p class="text-muted">You haven't marked attendance for any sessions yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Information Panel -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>How Attendance Works:</h6>
                <ul class="mb-0">
                    <li><strong>Active Sessions:</strong> Your lecturer will open attendance for 2 minutes at the start of class</li>
                    <li><strong>Facial Recognition:</strong> Use your camera to mark attendance - ensure good lighting and face the camera</li>
                    <li><strong>Attendance Value:</strong> Each attended session counts as 100% for that class</li>
                    <li><strong>Final Grade:</strong> Your total attendance contributes 10% to your final module grade</li>
                    <li><strong>Time Limit:</strong> You must mark attendance within the 2-minute window</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh page every 10 seconds to check for new active sessions
setInterval(function() {
    location.reload();
}, 10000);

// Countdown timers for active sessions
function updateCountdowns() {
    const timers = document.querySelectorAll('.countdown-timer');
    
    timers.forEach(function(timer) {
        const endTime = new Date(timer.getAttribute('data-end-time')).getTime();
        const now = new Date().getTime();
        const timeLeft = endTime - now;
        
        if (timeLeft > 0) {
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
            
            timer.innerHTML = `<i class="fas fa-hourglass-half me-1"></i>${minutes}:${seconds.toString().padStart(2, '0')} remaining`;
            timer.className = 'countdown-timer text-warning fw-bold mb-3';
        } else {
            timer.innerHTML = '<i class="fas fa-times-circle me-1"></i>Session Expired';
            timer.className = 'countdown-timer text-danger fw-bold mb-3';
            
            // Disable the mark attendance button
            const button = timer.parentElement.querySelector('.btn-primary');
            if (button) {
                button.className = 'btn btn-secondary btn-sm w-100';
                button.innerHTML = '<i class="fas fa-times me-2"></i>Session Expired';
                button.setAttribute('disabled', 'disabled');
                button.removeAttribute('href');
            }
        }
    });
}

// Update countdowns every second
setInterval(updateCountdowns, 1000);
updateCountdowns();

// Show notification for new active sessions
{% if active_sessions and not active_sessions|selectattr('already_marked')|list %}
// Only show notification if there are unmarked active sessions
if (Notification.permission === 'granted') {
    new Notification('Attendance Session Active', {
        body: 'You have active attendance sessions that need to be marked.',
        icon: '/static/favicon.ico'
    });
} else if (Notification.permission !== 'denied') {
    Notification.requestPermission().then(function(permission) {
        if (permission === 'granted') {
            new Notification('Attendance Session Active', {
                body: 'You have active attendance sessions that need to be marked.',
                icon: '/static/favicon.ico'
            });
        }
    });
}
{% endif %}
</script>

<style>
.card {
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

.border-warning {
    border-left: 4px solid #ffc107 !important;
}

.border-success {
    border-left: 4px solid #198754 !important;
}

.countdown-timer {
    font-size: 0.9rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

.alert-info {
    background-color: #e7f3ff;
    border-color: #b8daff;
    color: #004085;
}
</style>
{% endblock %}
