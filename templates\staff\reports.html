{% extends "base.html" %}

{% block title %}Reports - Facial Recognition Attendance System{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1">Attendance Reports</h1>
                    <p class="text-muted mb-0">View and analyze attendance data for your modules</p>
                </div>
                <div>
                    <a href="{{ url_for('staff_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row g-4 mb-5">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-week me-2"></i>This Week
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <h4 class="text-primary">{{ weekly_stats.total_sessions or 0 }}</h4>
                            <small class="text-muted">Sessions</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-success">{{ weekly_stats.unique_students or 0 }}</h4>
                            <small class="text-muted">Students</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-info">{{ weekly_stats.total_attendance or 0 }}</h4>
                            <small class="text-muted">Attendance</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>This Month
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <h4 class="text-primary">{{ monthly_stats.total_sessions or 0 }}</h4>
                            <small class="text-muted">Sessions</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-success">{{ monthly_stats.unique_students or 0 }}</h4>
                            <small class="text-muted">Students</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-info">{{ monthly_stats.total_attendance or 0 }}</h4>
                            <small class="text-muted">Attendance</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Generate Custom Report
                    </h5>
                </div>
                <div class="card-body">
                    <form id="report-form">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="module-select" class="form-label">Module</label>
                                <select class="form-select" id="module-select" name="module_id">
                                    <option value="">All Modules</option>
                                    {% for module in modules %}
                                    <option value="{{ module.module_id }}">{{ module.module_code }} - {{ module.module_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="start-date" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="start-date" name="start_date">
                            </div>
                            <div class="col-md-3">
                                <label for="end-date" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="end-date" name="end_date">
                            </div>
                            <div class="col-md-3">
                                <label for="report-type" class="form-label">Report Type</label>
                                <select class="form-select" id="report-type" name="report_type">
                                    <option value="summary">Summary Report</option>
                                    <option value="detailed">Detailed Report</option>
                                    <option value="student">Student Report</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-chart-bar me-2"></i>Generate Report
                                </button>
                                <button type="button" class="btn btn-outline-success" id="export-btn">
                                    <i class="fas fa-download me-2"></i>Export to Excel
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Results -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>Report Results
                    </h5>
                    <small class="text-muted" id="report-timestamp"></small>
                </div>
                <div class="card-body">
                    <div id="report-content">
                        <div class="text-center py-5">
                            <i class="fas fa-chart-line text-muted" style="font-size: 4rem;"></i>
                            <h5 class="mt-3 text-muted">No Report Generated</h5>
                            <p class="text-muted">Select your filters above and click "Generate Report" to view attendance data.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set default dates (last 30 days)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    document.getElementById('end-date').value = today.toISOString().split('T')[0];
    document.getElementById('start-date').value = thirtyDaysAgo.toISOString().split('T')[0];
    
    // Handle form submission
    document.getElementById('report-form').addEventListener('submit', function(e) {
        e.preventDefault();
        generateReport();
    });
    
    // Handle export button
    document.getElementById('export-btn').addEventListener('click', function() {
        exportReport();
    });
});

function generateReport() {
    const formData = new FormData(document.getElementById('report-form'));
    const params = new URLSearchParams(formData);
    
    // Show loading state
    document.getElementById('report-content').innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="text-muted mt-3">Generating report...</p>
        </div>
    `;
    
    fetch(`/api/staff/generate_report?${params}`)
        .then(response => response.json())
        .then(data => {
            displayReport(data);
            document.getElementById('report-timestamp').textContent = `Generated: ${new Date().toLocaleString()}`;
        })
        .catch(error => {
            console.error('Error generating report:', error);
            document.getElementById('report-content').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-warning">Error Generating Report</h5>
                    <p class="text-muted">Please try again or contact support if the problem persists.</p>
                </div>
            `;
        });
}

function displayReport(data) {
    let html = '';
    
    if (data.type === 'summary') {
        html = generateSummaryReport(data);
    } else if (data.type === 'detailed') {
        html = generateDetailedReport(data);
    } else if (data.type === 'student') {
        html = generateStudentReport(data);
    }
    
    document.getElementById('report-content').innerHTML = html;
}

function generateSummaryReport(data) {
    if (!data.sessions || data.sessions.length === 0) {
        return `
            <div class="text-center py-4">
                <i class="fas fa-info-circle text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-muted">No Data Found</h5>
                <p class="text-muted">No sessions found for the selected criteria.</p>
            </div>
        `;
    }
    
    return `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Session</th>
                        <th>Module</th>
                        <th>Enrolled</th>
                        <th>Attended</th>
                        <th>Rate</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.sessions.map(session => `
                        <tr>
                            <td>${session.session_date}</td>
                            <td>${session.session_name}</td>
                            <td>${session.module_code}</td>
                            <td>${session.enrolled_count}</td>
                            <td>${session.attendance_count}</td>
                            <td>
                                <span class="badge ${session.attendance_rate >= 80 ? 'bg-success' : session.attendance_rate >= 60 ? 'bg-warning' : 'bg-danger'}">
                                    ${session.attendance_rate}%
                                </span>
                            </td>
                            <td>
                                <span class="badge ${session.is_completed ? 'bg-success' : 'bg-secondary'}">
                                    ${session.is_completed ? 'Completed' : 'Pending'}
                                </span>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

function generateDetailedReport(data) {
    // Implementation for detailed report
    return '<p class="text-muted">Detailed report implementation coming soon...</p>';
}

function generateStudentReport(data) {
    // Implementation for student report
    return '<p class="text-muted">Student report implementation coming soon...</p>';
}

function exportReport() {
    // Implementation for Excel export
    alert('Export functionality coming soon!');
}
</script>
{% endblock %}

{% block extra_css %}
<style>
.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}
