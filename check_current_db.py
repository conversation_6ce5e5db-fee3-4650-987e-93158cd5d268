import sqlite3

conn = sqlite3.connect('attendance_fixed.db')

print("Current database tables:")
tables = [row[0] for row in conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()]
for table in sorted(tables):
    print(f"  {table}")

print(f"\nTotal tables: {len(tables)}")

# Check if key tables exist
key_tables = ['institutions', 'faculties', 'departments', 'users', 'students']
print(f"\nChecking key tables:")
for table in key_tables:
    if table in tables:
        try:
            count = conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
            print(f"  ✅ {table}: {count} records")
        except Exception as e:
            print(f"  ❌ {table}: Error - {e}")
    else:
        print(f"  ❌ {table}: Missing")

conn.close()
