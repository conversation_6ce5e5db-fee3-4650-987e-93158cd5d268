import sqlite3
import hashlib

def check_user_bhavek():
    conn = sqlite3.connect('attendance_fixed.db')
    
    print("🔍 Checking user 'Bhavek'...")
    
    # Check if user exists
    user = conn.execute('''
        SELECT u.user_id, u.username, u.email, u.password_hash, u.first_name, u.last_name, 
               u.role_id, u.is_active, ur.role_name
        FROM users u
        LEFT JOIN user_roles ur ON u.role_id = ur.role_id
        WHERE u.username = ?
    ''', ('Bhavek',)).fetchone()
    
    if user:
        print(f"✅ User found:")
        print(f"   - User ID: {user[0]}")
        print(f"   - Username: {user[1]}")
        print(f"   - Email: {user[2]}")
        print(f"   - Password Hash: {user[3][:20]}...")
        print(f"   - Name: {user[4]} {user[5]}")
        print(f"   - Role ID: {user[6]}")
        print(f"   - Is Active: {user[7]}")
        print(f"   - Role Name: {user[8]}")
        
        # Test password hashing
        test_password = "password123"  # Common test password
        test_hash = hashlib.sha256(test_password.encode()).hexdigest()
        print(f"\n🔐 Password testing:")
        print(f"   - Test password: {test_password}")
        print(f"   - Test hash: {test_hash[:20]}...")
        print(f"   - Stored hash: {user[3][:20]}...")
        print(f"   - Hashes match: {test_hash == user[3]}")
        
        # Try other common passwords
        common_passwords = ["123456", "admin123", "bhavek123", "Bhavek123", "password"]
        for pwd in common_passwords:
            pwd_hash = hashlib.sha256(pwd.encode()).hexdigest()
            if pwd_hash == user[3]:
                print(f"   ✅ Password is: {pwd}")
                break
        else:
            print("   ❌ Password doesn't match common passwords")
            
    else:
        print("❌ User 'Bhavek' not found")
        
        # Check all users
        print("\n📋 All users in database:")
        users = conn.execute('''
            SELECT u.username, u.first_name, u.last_name, ur.role_name, u.is_active
            FROM users u
            LEFT JOIN user_roles ur ON u.role_id = ur.role_id
            ORDER BY u.user_id
        ''').fetchall()
        
        for user in users:
            print(f"   - {user[0]} ({user[1]} {user[2]}) - Role: {user[3]} - Active: {user[4]}")
    
    conn.close()

if __name__ == "__main__":
    check_user_bhavek()
