{% extends "base.html" %}

{% block title %}{{ table_name.replace('_', ' ').title() }} - Admin Panel{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-table me-2 text-primary"></i>
                        {{ table_name.replace('_', ' ').title() }}
                    </h1>
                    <p class="text-muted mb-0">{{ total_records }} records total</p>
                </div>
                <div>
                    {% if table_name not in ['student_face_encodings', 'attendance_marks', 'student_attendance'] %}
                    <a href="{{ url_for('admin_add_record', table_name=table_name) }}" class="btn btn-success me-2">
                        <i class="fas fa-plus me-2"></i>
                        Add Record
                    </a>
                    {% endif %}
                    <a href="{{ url_for('admin_export_table', table_name=table_name) }}" class="btn btn-info me-2">
                        <i class="fas fa-download me-2"></i>
                        Export CSV
                    </a>
                    <a href="{{ url_for('admin_tables') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Tables
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ search }}" placeholder="Search in text fields...">
                        </div>
                        <div class="col-md-3">
                            <label for="per_page" class="form-label">Records per page</label>
                            <select class="form-select" id="per_page" name="per_page">
                                <option value="25" {% if per_page == 25 %}selected{% endif %}>25</option>
                                <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                                <option value="200" {% if per_page == 200 %}selected{% endif %}>200</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>
                                Search
                            </button>
                            <a href="{{ url_for('admin_view_table', table_name=table_name) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body">
                    {% if records %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    {% for column in columns %}
                                    <th>
                                        <a href="?sort={{ column }}&order={{ 'DESC' if sort_by == column and sort_order == 'ASC' else 'ASC' }}{% if search %}&search={{ search }}{% endif %}&per_page={{ per_page }}" 
                                           class="text-white text-decoration-none">
                                            {{ column.replace('_', ' ').title() }}
                                            {% if sort_by == column %}
                                                {% if sort_order == 'ASC' %}
                                                    <i class="fas fa-sort-up"></i>
                                                {% else %}
                                                    <i class="fas fa-sort-down"></i>
                                                {% endif %}
                                            {% else %}
                                                <i class="fas fa-sort text-muted"></i>
                                            {% endif %}
                                        </a>
                                    </th>
                                    {% endfor %}
                                    <th width="120">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in records %}
                                <tr>
                                    {% for column in columns %}
                                    <td>
                                        {% set value = record[column] %}
                                        {% set display_key = column + '_display' %}
                                        {% if value is none %}
                                            <span class="text-muted">NULL</span>
                                        {% elif column.endswith('_at') or column.endswith('_date') %}
                                            {% if value %}
                                                {{ value if value.strftime else value }}
                                            {% else %}
                                                <span class="text-muted">NULL</span>
                                            {% endif %}
                                        {% elif column == 'password_hash' %}
                                            <span class="text-muted">••••••••</span>
                                        {% elif column == 'face_encoding' %}
                                            <span class="text-muted">[Binary Data]</span>
                                        {% elif column.endswith('_id') and display_key in record and record[display_key] %}
                                            <div>
                                                <strong>{{ record[display_key] }}</strong>
                                                <br><small class="text-muted">ID: {{ value }}</small>
                                            </div>
                                        {% elif value|string|length > 50 %}
                                            <span title="{{ value }}">{{ value|string|truncate(50) }}</span>
                                        {% else %}
                                            {{ value }}
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                    <td>
                                        {% set primary_key = None %}
                                        {% for col in schema if col['pk'] %}
                                            {% set primary_key = col['name'] %}
                                        {% endfor %}
                                        {% if not primary_key %}
                                            {% set primary_key = schema[0]['name'] if schema|length > 0 else 'id' %}
                                        {% endif %}
                                        {% set record_id = record[primary_key] if primary_key in record else record[0] %}
                                        
                                        {% if table_name not in ['student_face_encodings', 'attendance_marks'] %}
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('admin_edit_record', table_name=table_name, record_id=record_id) }}" 
                                               class="btn btn-outline-primary btn-sm" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if current_user.role == 'super_admin' %}
                                            <button type="button" class="btn btn-outline-danger btn-sm" 
                                                    onclick="confirmDelete('{{ table_name }}', {{ record_id }})" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                        {% else %}
                                        <span class="text-muted">Read-only</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if total_pages > 1 %}
                    <nav aria-label="Table pagination">
                        <ul class="pagination justify-content-center">
                            {% if current_page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ current_page - 1 }}{% if sort_by %}&sort={{ sort_by }}&order={{ sort_order }}{% endif %}{% if search %}&search={{ search }}{% endif %}&per_page={{ per_page }}">
                                    Previous
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in range(1, total_pages + 1) %}
                                {% if page_num == current_page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% elif page_num <= 3 or page_num > total_pages - 3 or (page_num >= current_page - 2 and page_num <= current_page + 2) %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_num }}{% if sort_by %}&sort={{ sort_by }}&order={{ sort_order }}{% endif %}{% if search %}&search={{ search }}{% endif %}&per_page={{ per_page }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% elif page_num == 4 or page_num == total_pages - 3 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if current_page < total_pages %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ current_page + 1 }}{% if sort_by %}&sort={{ sort_by }}&order={{ sort_order }}{% endif %}{% if search %}&search={{ search }}{% endif %}&per_page={{ per_page }}">
                                    Next
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No records found</h5>
                        <p class="text-muted">
                            {% if search %}
                                No records match your search criteria.
                            {% else %}
                                This table is empty.
                            {% endif %}
                        </p>
                        {% if table_name not in ['student_face_encodings', 'attendance_marks', 'student_attendance'] %}
                        <a href="{{ url_for('admin_add_record', table_name=table_name) }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Add First Record
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this record? This action cannot be undone.</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> Deleting this record may affect related data in other tables.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(tableName, recordId) {
    const form = document.getElementById('deleteForm');
    form.action = `/admin/table/${tableName}/delete/${recordId}`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
{% endblock %}
