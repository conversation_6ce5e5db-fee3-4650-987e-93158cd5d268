{% extends "base.html" %}

{% block title %}My Modules{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-book me-2 text-primary"></i>
                        My Modules
                    </h2>
                    <p class="text-muted mb-0">View your enrolled modules and academic progress</p>
                </div>
                <a href="{{ url_for('student_dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Dashboard
                </a>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="display-6 text-primary mb-2">{{ total_modules }}</div>
                            <h6 class="card-title mb-0">Total Modules</h6>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="display-6 text-success mb-2">{{ total_credits }}</div>
                            <h6 class="card-title mb-0">Total Credits</h6>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="display-6 text-warning mb-2">{{ core_modules|length }}</div>
                            <h6 class="card-title mb-0">Core Modules</h6>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="display-6 text-info mb-2">{{ elective_modules|length }}</div>
                            <h6 class="card-title mb-0">Elective Modules</h6>
                        </div>
                    </div>
                </div>
            </div>

            {% if enrolled_modules %}
                <!-- Course Information -->
                {% set first_module = enrolled_modules[0] %}
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <h5 class="card-title mb-3">
                            <i class="fas fa-graduation-cap me-2"></i>
                            Academic Information
                        </h5>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Faculty:</strong><br>
                                <span class="text-muted">{{ first_module.faculty_name }}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Year Level:</strong><br>
                                <span class="text-muted">Year {{ first_module.year_level }}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Course:</strong><br>
                                <span class="text-muted">{{ first_module.course_name }}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Year:</strong><br>
                                <span class="text-muted">{{ first_module.year_name }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Module Type Legend -->
                <div class="alert alert-info mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6 class="alert-heading mb-2">
                                <i class="fas fa-info-circle me-2"></i>
                                Module Types
                            </h6>
                            <p class="mb-0">
                                <span class="badge bg-warning text-dark me-2">Core</span> modules are mandatory and automatically enrolled.
                                <span class="badge bg-success ms-2 me-2">Elective</span> modules are optional and selected by students.
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <small class="text-muted">
                                Total: {{ core_modules|length }} Core + {{ elective_modules|length }} Elective
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Modules by Semester -->
                {% for semester, modules in modules_by_semester.items() %}
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            Semester {{ semester }}
                            <span class="badge bg-primary ms-2">{{ modules|length }} modules</span>
                            <span class="badge bg-warning text-dark ms-1">{{ modules|rejectattr('is_elective')|list|length }} Core</span>
                            <span class="badge bg-success ms-1">{{ modules|selectattr('is_elective')|list|length }} Elective</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            {% for module in modules %}
                            <div class="col-lg-6">
                                <div class="card border-{{ 'success' if module.is_elective else 'warning' }} h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0">{{ module.module_name }}</h6>
                                            {% if module.is_elective %}
                                                <span class="badge bg-success">Elective</span>
                                            {% else %}
                                                <span class="badge bg-warning text-dark">Core</span>
                                            {% endif %}
                                        </div>
                                        <p class="text-muted small mb-2">{{ module.module_code }}</p>
                                        
                                        {% if module.description %}
                                        <p class="card-text small mb-3">
                                            {{ module.description[:120] }}
                                            {% if module.description|length > 120 %}...{% endif %}
                                        </p>
                                        {% endif %}
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex gap-2">
                                                <span class="badge bg-info">{{ module.credits }} Credits</span>
                                                <span class="badge bg-secondary">{{ module.semester }}</span>
                                            </div>
                                            <small class="text-muted">
                                                Enrolled: {{ module.registration_date if module.registration_date else 'N/A' }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endfor %}

                <!-- Quick Actions -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <a href="{{ url_for('student_attendance') }}" class="btn btn-primary w-100">
                                    <i class="fas fa-calendar-check me-2"></i>
                                    Mark Attendance
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="{{ url_for('view_attendance_records') }}" class="btn btn-success w-100">
                                    <i class="fas fa-chart-line me-2"></i>
                                    View Attendance Records
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="{{ url_for('student_dashboard') }}" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-home me-2"></i>
                                    Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

            {% else %}
                <!-- No Modules -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-book-open display-1 text-muted"></i>
                        </div>
                        <h4 class="mb-3">No Modules Found</h4>
                        <p class="text-muted mb-4">You are not currently enrolled in any modules.</p>
                        <a href="{{ url_for('student_dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.display-6 {
    font-size: 2rem;
    font-weight: 600;
}

.badge {
    font-size: 0.75em;
}

/* Core module styling */
.border-warning {
    border-left: 4px solid #ffc107 !important;
}

/* Elective module styling */
.border-success {
    border-left: 4px solid #198754 !important;
}

/* Module card enhancements */
.card-body h6.card-title {
    font-weight: 600;
    color: #2c3e50;
}

.card-body .text-muted.small {
    font-family: 'Courier New', monospace;
    font-weight: 500;
}

/* Badge styling */
.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
    font-weight: 600;
}

.badge.bg-success {
    background-color: #198754 !important;
    color: #fff !important;
    font-weight: 600;
}

/* Alert styling */
.alert-info {
    background-color: #e7f3ff;
    border-color: #b8daff;
    color: #0c5460;
}
</style>
{% endblock %}
