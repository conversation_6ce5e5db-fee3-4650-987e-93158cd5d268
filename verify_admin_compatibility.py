#!/usr/bin/env python3
"""
Admin System Compatibility Verification Script
Verifies that the admin system works correctly with the current database schema
"""

import sqlite3
import sys
import os

def verify_database_compatibility():
    """Verify that the admin system is compatible with the current database"""
    
    db_path = 'attendance_fixed.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file '{db_path}' not found!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        
        print("🔍 Verifying Admin System Compatibility with Current Database")
        print("=" * 60)
        
        # Get all tables in the database
        tables_query = "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
        actual_tables = [row[0] for row in conn.execute(tables_query).fetchall()]
        
        print(f"📊 Found {len(actual_tables)} tables in database:")
        for table in actual_tables:
            print(f"   ✓ {table}")
        
        print("\n" + "=" * 60)
        
        # Expected tables for admin system
        expected_tables = [
            'institutions', 'faculties', 'departments', 'user_roles', 'users', 'students',
            'courses', 'course_years', 'modules', 'student_enrollments', 'module_registrations',
            'student_face_encodings', 'module_lecturers', 'attendance_sessions', 
            'student_attendance', 'attendance_marks', 'attendance_records'
        ]
        
        print("🎯 Checking Admin System Table Coverage:")
        
        missing_tables = []
        for table in expected_tables:
            if table in actual_tables:
                print(f"   ✅ {table} - Available")
            else:
                print(f"   ❌ {table} - Missing")
                missing_tables.append(table)
        
        if missing_tables:
            print(f"\n⚠️  Warning: {len(missing_tables)} expected tables are missing:")
            for table in missing_tables:
                print(f"   - {table}")
        else:
            print(f"\n✅ All expected tables are present!")
        
        print("\n" + "=" * 60)
        
        # Check table schemas for key fields
        print("🔧 Verifying Table Schemas:")
        
        schema_checks = {
            'users': ['user_id', 'username', 'email', 'password_hash', 'role_id'],
            'students': ['student_id', 'student_number', 'first_name', 'last_name', 'email'],
            'attendance_sessions': ['session_id', 'module_id', 'lecturer_id', 'session_name'],
            'student_attendance': ['attendance_id', 'session_id', 'student_id', 'marked_at'],
            'modules': ['module_id', 'module_code', 'module_name', 'course_year_id']
        }
        
        schema_issues = []
        for table_name, required_fields in schema_checks.items():
            if table_name in actual_tables:
                # Get table schema
                schema_query = f"PRAGMA table_info({table_name})"
                columns = [row[1] for row in conn.execute(schema_query).fetchall()]
                
                missing_fields = [field for field in required_fields if field not in columns]
                if missing_fields:
                    print(f"   ❌ {table_name}: Missing fields {missing_fields}")
                    schema_issues.append((table_name, missing_fields))
                else:
                    print(f"   ✅ {table_name}: All required fields present")
            else:
                print(f"   ⚠️  {table_name}: Table not found")
        
        print("\n" + "=" * 60)
        
        # Check for admin users
        print("👤 Checking Admin User Access:")
        
        try:
            admin_users = conn.execute("""
                SELECT u.username, u.first_name, u.last_name, ur.role_name
                FROM users u
                JOIN user_roles ur ON u.role_id = ur.role_id
                WHERE ur.role_name IN ('admin', 'super_admin')
            """).fetchall()
            
            if admin_users:
                print(f"   ✅ Found {len(admin_users)} admin user(s):")
                for user in admin_users:
                    print(f"      - {user['username']} ({user['first_name']} {user['last_name']}) - {user['role_name']}")
            else:
                print("   ❌ No admin users found!")
                schema_issues.append(('admin_access', 'No admin users'))
                
        except Exception as e:
            print(f"   ❌ Error checking admin users: {e}")
            schema_issues.append(('admin_access', str(e)))
        
        print("\n" + "=" * 60)
        
        # Check data integrity
        print("🔍 Checking Data Integrity:")
        
        try:
            # Check if there's sample data
            student_count = conn.execute("SELECT COUNT(*) as count FROM students").fetchone()['count']
            module_count = conn.execute("SELECT COUNT(*) as count FROM modules").fetchone()['count']
            session_count = conn.execute("SELECT COUNT(*) as count FROM attendance_sessions").fetchone()['count']
            
            print(f"   📊 Students: {student_count}")
            print(f"   📚 Modules: {module_count}")
            print(f"   📅 Sessions: {session_count}")
            
            if student_count > 0 and module_count > 0:
                print("   ✅ Database contains sample data")
            else:
                print("   ⚠️  Database appears to be empty")
                
        except Exception as e:
            print(f"   ❌ Error checking data: {e}")
        
        print("\n" + "=" * 60)
        
        # Final assessment
        if not missing_tables and not schema_issues:
            print("🎉 COMPATIBILITY CHECK PASSED!")
            print("✅ The admin system is fully compatible with the current database")
            print("✅ All required tables and fields are present")
            print("✅ Admin users are configured")
            print("\n🚀 You can safely use the admin interface at: http://127.0.0.1:5000/admin")
            return True
        else:
            print("⚠️  COMPATIBILITY ISSUES FOUND!")
            if missing_tables:
                print(f"❌ Missing tables: {len(missing_tables)}")
            if schema_issues:
                print(f"❌ Schema issues: {len(schema_issues)}")
            print("\n🔧 The admin system may have limited functionality")
            return False
            
    except Exception as e:
        print(f"❌ Error connecting to database: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """Main function"""
    print("🔍 Admin System Compatibility Checker")
    print("=" * 60)
    
    success = verify_database_compatibility()
    
    if success:
        print("\n✅ Verification completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Verification found issues!")
        sys.exit(1)

if __name__ == "__main__":
    main()
