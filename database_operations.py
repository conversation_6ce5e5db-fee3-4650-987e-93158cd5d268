"""
Database Operations for Facial Recognition Attendance System
Provides secure database operations with anti-tampering features
"""

import sqlite3
import hashlib
import json
import datetime
import numpy as np
from typing import Optional, List, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AttendanceDatabase:
    """
    Secure database operations for facial recognition attendance system
    """
    
    def __init__(self, db_path: str = "attendance.db"):
        self.db_path = db_path
        self.connection = None
        self._connect()
    
    def _connect(self):
        """Establish database connection with security settings"""
        try:
            self.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            self.connection.row_factory = sqlite3.Row
            
            # Enable foreign key constraints
            self.connection.execute("PRAGMA foreign_keys = ON")
            
            # Enable WAL mode for better concurrency
            self.connection.execute("PRAGMA journal_mode = WAL")
            
            logger.info("Database connection established")
            
        except sqlite3.Error as e:
            logger.error(f"Database connection failed: {e}")
            raise
    
    def _generate_record_hash(self, data: Dict[str, Any]) -> str:
        """Generate SHA-256 hash for record integrity"""
        # Sort keys for consistent hashing
        sorted_data = json.dumps(data, sort_keys=True, default=str)
        return hashlib.sha256(sorted_data.encode()).hexdigest()
    
    def _get_previous_record_hash(self, table: str) -> Optional[str]:
        """Get the hash of the most recent record for chaining"""
        try:
            cursor = self.connection.execute(
                f"SELECT record_hash FROM {table} ORDER BY created_at DESC LIMIT 1"
            )
            result = cursor.fetchone()
            return result['record_hash'] if result else None
        except sqlite3.Error:
            return None
    
    def register_student(self, student_data: Dict[str, Any]) -> int:
        """
        Register a new student in the system
        
        Args:
            student_data: Dictionary containing student information
            
        Returns:
            student_id: The ID of the newly created student
        """
        try:
            cursor = self.connection.execute("""
                INSERT INTO students (
                    institution_id, department_id, student_number, email,
                    first_name, last_name, date_of_birth, phone, address,
                    enrollment_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                student_data['institution_id'],
                student_data.get('department_id'),
                student_data['student_number'],
                student_data.get('email'),
                student_data['first_name'],
                student_data['last_name'],
                student_data.get('date_of_birth'),
                student_data.get('phone'),
                student_data.get('address'),
                student_data.get('enrollment_date', datetime.date.today())
            ))
            
            student_id = cursor.lastrowid
            self.connection.commit()
            
            logger.info(f"Student registered successfully: ID {student_id}")
            return student_id
            
        except sqlite3.Error as e:
            self.connection.rollback()
            logger.error(f"Failed to register student: {e}")
            raise
    
    def store_facial_embedding(self, student_id: int, embedding: np.ndarray, 
                             model_version: str, created_by: int,
                             confidence_threshold: float = 0.85) -> int:
        """
        Store facial embedding for a student with security features
        
        Args:
            student_id: Student ID
            embedding: Facial embedding vector
            model_version: Version of the ML model used
            created_by: User ID who created this embedding
            confidence_threshold: Recognition confidence threshold
            
        Returns:
            embedding_id: The ID of the stored embedding
        """
        try:
            # Convert embedding to binary
            embedding_blob = embedding.tobytes()
            
            # Generate hash for integrity verification
            embedding_hash = hashlib.sha256(embedding_blob).hexdigest()
            
            cursor = self.connection.execute("""
                INSERT INTO facial_embeddings (
                    student_id, embedding_vector, embedding_hash,
                    model_version, confidence_threshold, created_by
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                student_id, embedding_blob, embedding_hash,
                model_version, confidence_threshold, created_by
            ))
            
            embedding_id = cursor.lastrowid
            self.connection.commit()
            
            logger.info(f"Facial embedding stored: ID {embedding_id}")
            return embedding_id
            
        except sqlite3.Error as e:
            self.connection.rollback()
            logger.error(f"Failed to store facial embedding: {e}")
            raise
    
    def record_attendance(self, session_id: int, student_id: int,
                         confidence: float, recognition_method: str = 'facial',
                         device_info: str = None, ip_address: str = None,
                         processed_by: int = None) -> int:
        """
        Record student attendance with anti-tampering features
        
        Args:
            session_id: Class session ID
            student_id: Student ID
            confidence: Recognition confidence score
            recognition_method: Method used for recognition
            device_info: Information about the device/camera used
            ip_address: IP address of the request
            processed_by: User who processed this attendance
            
        Returns:
            attendance_id: The ID of the attendance record
        """
        try:
            attendance_timestamp = datetime.datetime.now()
            
            # Prepare data for hashing
            record_data = {
                'session_id': session_id,
                'student_id': student_id,
                'confidence': confidence,
                'timestamp': attendance_timestamp.isoformat(),
                'method': recognition_method
            }
            
            # Generate record hash
            record_hash = self._generate_record_hash(record_data)
            
            # Get previous record hash for chaining
            previous_hash = self._get_previous_record_hash('attendance_records')
            
            cursor = self.connection.execute("""
                INSERT INTO attendance_records (
                    session_id, student_id, recognition_confidence,
                    attendance_timestamp, recognition_method, device_info,
                    ip_address, processed_by, record_hash, previous_record_hash
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                session_id, student_id, confidence, attendance_timestamp,
                recognition_method, device_info, ip_address, processed_by,
                record_hash, previous_hash
            ))
            
            attendance_id = cursor.lastrowid
            self.connection.commit()
            
            logger.info(f"Attendance recorded: ID {attendance_id}")
            return attendance_id
            
        except sqlite3.IntegrityError as e:
            self.connection.rollback()
            if "UNIQUE constraint failed" in str(e):
                logger.warning(f"Duplicate attendance record for session {session_id}, student {student_id}")
                raise ValueError("Attendance already recorded for this session")
            else:
                logger.error(f"Integrity error recording attendance: {e}")
                raise
        except sqlite3.Error as e:
            self.connection.rollback()
            logger.error(f"Failed to record attendance: {e}")
            raise
    
    def get_student_embeddings(self, student_id: int) -> List[Dict[str, Any]]:
        """
        Retrieve all active facial embeddings for a student
        
        Args:
            student_id: Student ID
            
        Returns:
            List of embedding records with metadata
        """
        try:
            cursor = self.connection.execute("""
                SELECT embedding_id, embedding_vector, embedding_hash,
                       model_version, confidence_threshold, is_primary,
                       created_at
                FROM facial_embeddings
                WHERE student_id = ? AND is_active = TRUE
                ORDER BY is_primary DESC, created_at DESC
            """, (student_id,))
            
            embeddings = []
            for row in cursor.fetchall():
                # Convert binary back to numpy array
                embedding_vector = np.frombuffer(row['embedding_vector'], dtype=np.float32)
                
                embeddings.append({
                    'embedding_id': row['embedding_id'],
                    'embedding_vector': embedding_vector,
                    'embedding_hash': row['embedding_hash'],
                    'model_version': row['model_version'],
                    'confidence_threshold': row['confidence_threshold'],
                    'is_primary': row['is_primary'],
                    'created_at': row['created_at']
                })
            
            return embeddings
            
        except sqlite3.Error as e:
            logger.error(f"Failed to retrieve embeddings: {e}")
            raise
    
    def get_session_attendance(self, session_id: int) -> List[Dict[str, Any]]:
        """
        Get attendance records for a specific session
        
        Args:
            session_id: Class session ID
            
        Returns:
            List of attendance records with student information
        """
        try:
            cursor = self.connection.execute("""
                SELECT ar.attendance_id, ar.student_id, ar.recognition_confidence,
                       ar.attendance_timestamp, ar.recognition_method,
                       s.student_number, s.first_name, s.last_name,
                       ar.is_verified
                FROM attendance_records ar
                JOIN students s ON ar.student_id = s.student_id
                WHERE ar.session_id = ?
                ORDER BY ar.attendance_timestamp
            """, (session_id,))
            
            return [dict(row) for row in cursor.fetchall()]
            
        except sqlite3.Error as e:
            logger.error(f"Failed to retrieve session attendance: {e}")
            raise
    
    def verify_record_integrity(self, attendance_id: int) -> bool:
        """
        Verify the integrity of an attendance record using its hash
        
        Args:
            attendance_id: Attendance record ID
            
        Returns:
            True if record is valid, False otherwise
        """
        try:
            cursor = self.connection.execute("""
                SELECT session_id, student_id, recognition_confidence,
                       attendance_timestamp, recognition_method, record_hash
                FROM attendance_records
                WHERE attendance_id = ?
            """, (attendance_id,))
            
            record = cursor.fetchone()
            if not record:
                return False
            
            # Recreate the hash from current data
            record_data = {
                'session_id': record['session_id'],
                'student_id': record['student_id'],
                'confidence': record['recognition_confidence'],
                'timestamp': record['attendance_timestamp'],
                'method': record['recognition_method']
            }
            
            calculated_hash = self._generate_record_hash(record_data)
            stored_hash = record['record_hash']
            
            is_valid = calculated_hash == stored_hash
            
            if not is_valid:
                logger.warning(f"Record integrity check failed for attendance ID {attendance_id}")
            
            return is_valid
            
        except sqlite3.Error as e:
            logger.error(f"Failed to verify record integrity: {e}")
            return False
    
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logger.info("Database connection closed")

# Example usage and testing functions
def example_usage():
    """Demonstrate database operations"""
    db = AttendanceDatabase("test_attendance.db")
    
    try:
        # Example: Register a student
        student_data = {
            'institution_id': 1,
            'department_id': 1,
            'student_number': 'CS2024001',
            'email': '<EMAIL>',
            'first_name': 'John',
            'last_name': 'Doe',
            'enrollment_date': datetime.date.today()
        }
        
        student_id = db.register_student(student_data)
        print(f"Registered student with ID: {student_id}")
        
        # Example: Store facial embedding
        fake_embedding = np.random.rand(512).astype(np.float32)
        embedding_id = db.store_facial_embedding(
            student_id=student_id,
            embedding=fake_embedding,
            model_version="FaceNet_v1.0",
            created_by=1
        )
        print(f"Stored embedding with ID: {embedding_id}")
        
        # Example: Record attendance
        attendance_id = db.record_attendance(
            session_id=1,
            student_id=student_id,
            confidence=0.92,
            recognition_method='facial',
            device_info='Camera_001',
            ip_address='*************'
        )
        print(f"Recorded attendance with ID: {attendance_id}")
        
        # Verify record integrity
        is_valid = db.verify_record_integrity(attendance_id)
        print(f"Record integrity check: {'PASSED' if is_valid else 'FAILED'}")
        
    finally:
        db.close()

if __name__ == "__main__":
    example_usage()
    