#!/usr/bin/env python3
"""
Check and fix face encoding dimensions
"""

import sqlite3
import json

def check_face_encodings():
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    print("🔍 Checking face encoding dimensions...")
    
    # Get all face encodings
    encodings = conn.execute("""
        SELECT sfe.encoding_id, sfe.student_id, sfe.face_encoding,
               s.student_number, s.first_name, s.last_name
        FROM student_face_encodings sfe
        JOIN students s ON sfe.student_id = s.student_id
    """).fetchall()
    
    print(f"📊 Found {len(encodings)} face encodings:")
    
    issues_found = []
    
    for encoding in encodings:
        try:
            face_data = json.loads(encoding['face_encoding'])
            size = len(face_data)
            
            status = "✅" if size == 10000 else "❌"
            expected = "100x100 grayscale" if size == 10000 else f"Expected 10,000, got {size}"
            
            print(f"   {status} Student {encoding['student_id']} ({encoding['student_number']}): {size} elements - {expected}")
            
            if size != 10000:
                issues_found.append({
                    'encoding_id': encoding['encoding_id'],
                    'student_id': encoding['student_id'],
                    'student_number': encoding['student_number'],
                    'current_size': size,
                    'name': f"{encoding['first_name']} {encoding['last_name']}"
                })
                
        except Exception as e:
            print(f"   ❌ Student {encoding['student_id']} ({encoding['student_number']}): Error parsing - {e}")
            issues_found.append({
                'encoding_id': encoding['encoding_id'],
                'student_id': encoding['student_id'],
                'student_number': encoding['student_number'],
                'current_size': 'ERROR',
                'name': f"{encoding['first_name']} {encoding['last_name']}"
            })
    
    if issues_found:
        print(f"\n⚠️  Found {len(issues_found)} problematic encodings:")
        for issue in issues_found:
            print(f"   - {issue['name']} ({issue['student_number']}): {issue['current_size']} elements")
        
        print(f"\n💡 Recommendation:")
        print(f"   These students need to re-register their faces to fix the dimension mismatch.")
        print(f"   You can either:")
        print(f"   1. Delete these encodings and ask students to re-register")
        print(f"   2. Or fix them programmatically if they're 30,000 elements (color → grayscale)")
        
        # Check if any are 30,000 (color images that can be fixed)
        fixable = [issue for issue in issues_found if issue['current_size'] == 30000]
        if fixable:
            print(f"\n🔧 {len(fixable)} encodings appear to be color images (30,000 elements) and can be auto-fixed:")
            for issue in fixable:
                print(f"   - {issue['name']} ({issue['student_number']})")
    else:
        print(f"\n✅ All face encodings have correct dimensions!")
    
    conn.close()
    return issues_found

def fix_color_encodings():
    """Fix encodings that are 30,000 elements (color) by converting to grayscale"""
    conn = sqlite3.connect('attendance_fixed.db')
    
    print("\n🔧 Attempting to fix color encodings...")
    
    # Get encodings that are 30,000 elements
    encodings = conn.execute("""
        SELECT encoding_id, student_id, face_encoding
        FROM student_face_encodings
    """).fetchall()
    
    fixed_count = 0
    
    for encoding in encodings:
        try:
            face_data = json.loads(encoding['face_encoding'])
            if len(face_data) == 30000:  # 100x100x3 color image
                print(f"   🔄 Fixing encoding for student {encoding['student_id']}...")
                
                # Convert 30,000 elements (100x100x3) to 10,000 elements (100x100x1)
                # This is a simple approach - take every 3rd element (R channel only)
                grayscale_data = [face_data[i] for i in range(0, len(face_data), 3)]
                
                if len(grayscale_data) == 10000:
                    # Update the encoding
                    new_encoding_json = json.dumps(grayscale_data)
                    conn.execute("""
                        UPDATE student_face_encodings 
                        SET face_encoding = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE encoding_id = ?
                    """, (new_encoding_json, encoding['encoding_id']))
                    
                    fixed_count += 1
                    print(f"   ✅ Fixed encoding for student {encoding['student_id']}")
                else:
                    print(f"   ❌ Failed to fix encoding for student {encoding['student_id']} - unexpected result size: {len(grayscale_data)}")
                    
        except Exception as e:
            print(f"   ❌ Error fixing encoding for student {encoding['student_id']}: {e}")
    
    if fixed_count > 0:
        conn.commit()
        print(f"\n✅ Fixed {fixed_count} face encodings!")
    else:
        print(f"\n💡 No encodings needed fixing.")
    
    conn.close()
    return fixed_count

if __name__ == "__main__":
    issues = check_face_encodings()
    
    if issues:
        # Check if any can be auto-fixed
        fixable = [issue for issue in issues if issue['current_size'] == 30000]
        if fixable:
            response = input(f"\n🤔 Would you like to auto-fix {len(fixable)} color encodings? (y/n): ")
            if response.lower() == 'y':
                fixed = fix_color_encodings()
                if fixed > 0:
                    print(f"\n🎉 Re-checking after fixes...")
                    check_face_encodings()
