{% extends "base.html" %}

{% block title %}Database Tables - Admin Panel{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-table me-2 text-primary"></i>
                        Database Tables
                    </h1>
                    <p class="text-muted mb-0">Manage all system data tables</p>
                </div>
                <div>
                    <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Tables Grid -->
    <div class="row">
        {% for table in tables %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-circle bg-primary text-white me-3">
                            <i class="fas fa-{{ table.icon }}"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0">{{ table.name.replace('_', ' ').title() }}</h5>
                            <small class="text-muted">{{ table.description }}</small>
                        </div>
                    </div>
                    
                    <p class="card-text text-muted small flex-grow-1">
                        Table: <code>{{ table.name }}</code>
                    </p>
                    
                    <div class="mt-auto">
                        <div class="btn-group w-100" role="group">
                            <a href="{{ url_for('admin_view_table', table_name=table.name) }}" 
                               class="btn btn-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>
                                View
                            </a>
                            {% if table.name not in ['student_face_encodings', 'attendance_marks', 'student_attendance'] %}
                            <a href="{{ url_for('admin_add_record', table_name=table.name) }}" 
                               class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                Add
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- System Information -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        System Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Core Tables</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-university text-muted me-2"></i> Institutions, Faculties, Departments</li>
                                <li><i class="fas fa-users text-muted me-2"></i> Users, Students, Roles</li>
                                <li><i class="fas fa-book text-muted me-2"></i> Courses, Modules, Enrollments</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">Attendance System</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-calendar text-muted me-2"></i> Attendance Sessions</li>
                                <li><i class="fas fa-check-circle text-muted me-2"></i> Student Attendance Records</li>
                                <li><i class="fas fa-face-smile text-muted me-2"></i> Facial Recognition Data</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Tip:</strong> Use the search and sort features in each table view to quickly find specific records. 
                        Be careful when editing or deleting records as this may affect system functionality.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.btn-group .btn {
    flex: 1;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}
</style>
{% endblock %}
