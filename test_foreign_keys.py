#!/usr/bin/env python3
"""
Test Foreign Key Handling
Tests the foreign key queries to ensure they return the correct format
"""

import sqlite3

def test_foreign_key_queries():
    """Test all foreign key queries used in the admin system"""
    
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row 
    
    print("🔍 Testing Foreign Key Queries")
    print("=" * 50)
    
    # Test queries used in the admin system
    test_queries = {
        'institutions': 'SELECT institution_id, institution_name FROM institutions ORDER BY institution_name',
        'faculties': 'SELECT faculty_id, faculty_name FROM faculties ORDER BY faculty_name',
        'departments': 'SELECT department_id, department_name FROM departments ORDER BY department_name',
        'user_roles': 'SELECT role_id, role_name FROM user_roles ORDER BY role_name',
        'courses': 'SELECT course_id, course_name FROM courses ORDER BY course_name',
        'course_years': 'SELECT course_year_id, COALESCE(year_name, "Year " || year_number) as display_name FROM course_years ORDER BY year_number',
        'modules': 'SELECT module_id, module_name FROM modules ORDER BY module_name',
        'students': 'SELECT student_id, (first_name || " " || last_name) as name FROM students ORDER BY first_name',
        'users': 'SELECT user_id, (first_name || " " || last_name) as name FROM users ORDER BY first_name'
    }
    
    for table_name, query in test_queries.items():
        try:
            print(f"\n📊 Testing {table_name}:")
            print(f"   Query: {query}")
            
            results = conn.execute(query).fetchall()
            print(f"   Results: {len(results)} rows")
            
            if results:
                # Test first few results
                for i, row in enumerate(results[:3]):
                    print(f"   Row {i+1}: {tuple(row)}")
                    
                    # Verify the row has the expected structure
                    if len(row) >= 2:
                        print(f"     ✅ ID: {row[0]}, Name: {row[1]}")
                    elif len(row) == 1:
                        print(f"     ⚠️  Single column: {row[0]}")
                    else:
                        print(f"     ❌ Unexpected structure: {row}")
            else:
                print(f"   ⚠️  No data found")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    
    # Test foreign key relationships
    print("\n🔗 Testing Foreign Key Relationships:")
    
    try:
        # Test a table with foreign keys
        fk_query = "PRAGMA foreign_key_list(students)"
        foreign_keys = conn.execute(fk_query).fetchall()
        
        print(f"   Students table foreign keys: {len(foreign_keys)}")
        for fk in foreign_keys:
            print(f"     {fk['from']} -> {fk['table']}.{fk['to']}")
            
    except Exception as e:
        print(f"   ❌ Error checking foreign keys: {e}")
    
    conn.close()
    print("\n✅ Foreign key testing completed!")

if __name__ == "__main__":
    test_foreign_key_queries()
