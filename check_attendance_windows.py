import sqlite3
from datetime import datetime

def check_attendance_windows():
    conn = sqlite3.connect('attendance_fixed.db')
    conn.row_factory = sqlite3.Row
    
    print("🔍 Checking attendance session windows...")
    
    # Check all sessions and their window status
    sessions = conn.execute('''
        SELECT as_.session_id, as_.session_name, as_.session_date, as_.scheduled_start_time, as_.scheduled_end_time,
               as_.attendance_window_start, as_.attendance_window_end, as_.is_active, as_.is_completed,
               m.module_code, m.module_name, u.username as lecturer
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN users u ON as_.lecturer_id = u.user_id
        ORDER BY as_.session_date DESC, as_.scheduled_start_time DESC
    ''').fetchall()
    
    print(f"📊 Found {len(sessions)} sessions:")
    current_time = datetime.now()
    
    for session in sessions:
        print(f"\n🎯 Session {session['session_id']}: {session['session_name']}")
        print(f"   Module: {session['module_code']} - {session['module_name']}")
        print(f"   Lecturer: {session['lecturer']}")
        print(f"   Date: {session['session_date']}")
        print(f"   Scheduled: {session['scheduled_start_time']} - {session['scheduled_end_time']}")
        print(f"   Is Active: {session['is_active']}")
        print(f"   Is Completed: {session['is_completed']}")
        print(f"   Window Start: {session['attendance_window_start']}")
        print(f"   Window End: {session['attendance_window_end']}")
        
        # Check if attendance window is currently open
        if session['attendance_window_start'] and session['attendance_window_end']:
            window_start = datetime.fromisoformat(session['attendance_window_start'].replace('Z', '+00:00'))
            window_end = datetime.fromisoformat(session['attendance_window_end'].replace('Z', '+00:00'))
            
            if window_start <= current_time <= window_end:
                print(f"   ✅ ATTENDANCE WINDOW IS OPEN")
            elif current_time < window_start:
                print(f"   ⏰ Window opens in the future")
            else:
                print(f"   ❌ Window has closed")
        else:
            print(f"   ⚠️  NO ATTENDANCE WINDOW SET")
    
    # Check what the student mark attendance page looks for
    print(f"\n🎓 Checking what students would see...")
    
    # This is the query from the mark attendance page
    student_id = 1  # Test with student ID 1
    available_sessions = conn.execute('''
        SELECT as_.session_id, as_.session_name, as_.session_date,
               as_.attendance_window_start, as_.attendance_window_end,
               m.module_code, m.module_name,
               CASE WHEN ar.student_id IS NOT NULL THEN 1 ELSE 0 END as already_attended
        FROM attendance_sessions as_
        JOIN modules m ON as_.module_id = m.module_id
        JOIN module_registrations mr ON m.module_id = mr.module_id
        LEFT JOIN attendance_records ar ON as_.session_id = ar.session_id AND ar.student_id = ?
        WHERE mr.student_id = ? AND mr.registration_status = 'active'
        AND as_.attendance_window_start IS NOT NULL 
        AND as_.attendance_window_end IS NOT NULL
        AND datetime('now') BETWEEN as_.attendance_window_start AND as_.attendance_window_end
        ORDER BY as_.session_date DESC
    ''', (student_id, student_id)).fetchall()
    
    print(f"   Student {student_id} can see {len(available_sessions)} sessions for attendance:")
    for session in available_sessions:
        print(f"     - {session['session_name']} ({session['module_code']})")
        print(f"       Already attended: {session['already_attended']}")
    
    if len(available_sessions) == 0:
        print(f"   ❌ No sessions available for attendance!")
        print(f"   This means either:")
        print(f"     1. No attendance windows are currently open")
        print(f"     2. Sessions don't have attendance windows set")
        print(f"     3. Student is not enrolled in modules with active sessions")
    
    conn.close()

if __name__ == "__main__":
    check_attendance_windows()
