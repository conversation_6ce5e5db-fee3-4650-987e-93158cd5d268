{% extends "base.html" %}

{% block title %}Student Registration - Step 2: Faculty Selection{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Progress Steps -->
            <div class="progress-step mb-4">
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-line"></div>
                    <small>Basic Info</small>
                </div>
                <div class="step active">
                    <div class="step-number">2</div>
                    <div class="step-line"></div>
                    <small>Faculty</small>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-line"></div>
                    <small>Course</small>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-line"></div>
                    <small>Year</small>
                </div>
                <div class="step">
                    <div class="step-number">5</div>
                    <div class="step-line"></div>
                    <small>Modules</small>
                </div>
                <div class="step">
                    <div class="step-number">6</div>
                    <small>Facial Setup</small>
                </div>
            </div>

            <!-- Faculty Selection -->
            <div class="card shadow-lg border-0">
                <div class="card-header text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-university me-2"></i>
                        Select Your Faculty
                    </h2>
                    <p class="mb-0 mt-2">Step 2: Choose the faculty where your course is offered</p>
                </div>
                <div class="card-body p-5">
                    {% if faculties %}
                        <div class="row g-4">
                            {% for faculty in faculties %}
                            <div class="col-md-6 col-lg-4">
                                <div class="card h-100 faculty-card border-2" style="cursor: pointer;" 
                                     onclick="selectFaculty({{ faculty.faculty_id }})">
                                    <div class="card-body text-center p-4">
                                        <div class="mb-3">
                                            {% if faculty.faculty_code == 'ENG' %}
                                                <i class="fas fa-cogs text-primary" style="font-size: 3rem;"></i>
                                            {% elif faculty.faculty_code == 'BUS' %}
                                                <i class="fas fa-briefcase text-success" style="font-size: 3rem;"></i>
                                            {% elif faculty.faculty_code == 'SCI' %}
                                                <i class="fas fa-flask text-info" style="font-size: 3rem;"></i>
                                            {% elif faculty.faculty_code == 'ART' %}
                                                <i class="fas fa-palette text-warning" style="font-size: 3rem;"></i>
                                            {% elif faculty.faculty_code == 'MED' %}
                                                <i class="fas fa-user-md text-danger" style="font-size: 3rem;"></i>
                                            {% elif faculty.faculty_code == 'LAW' %}
                                                <i class="fas fa-balance-scale text-dark" style="font-size: 3rem;"></i>
                                            {% else %}
                                                <i class="fas fa-graduation-cap text-secondary" style="font-size: 3rem;"></i>
                                            {% endif %}
                                        </div>
                                        <h5 class="card-title">{{ faculty.faculty_name }}</h5>
                                        <p class="card-text text-muted small">
                                            Code: {{ faculty.faculty_code }}
                                        </p>
                                        {% if faculty.description %}
                                        <p class="card-text small">
                                            {{ faculty.description[:100] }}{% if faculty.description|length > 100 %}...{% endif %}
                                        </p>
                                        {% endif %}
                                        <div class="mt-3">
                                            <span class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-arrow-right me-1"></i>
                                                Select Faculty
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                            <h4 class="mt-3">No Faculties Available</h4>
                            <p class="text-muted">
                                No faculties are currently available for registration. 
                                Please contact the administration for assistance.
                            </p>
                            <a href="{{ url_for('student_register') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Go Back
                            </a>
                        </div>
                    {% endif %}

                    <!-- Navigation Buttons -->
                    <div class="d-flex justify-content-between mt-5">
                        <a href="{{ url_for('student_register') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Basic Info
                        </a>
                        
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                Click on a faculty card to continue
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-4">
                            <h6 class="mb-3">
                                <i class="fas fa-question-circle text-info me-2"></i>
                                Need Help Choosing?
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-2">
                                        <strong>Not sure which faculty?</strong> 
                                        Your course determines your faculty. Common examples:
                                    </p>
                                    <ul class="small text-muted">
                                        <li>Computer Science, Information Technology → Engineering</li>
                                        <li>Business Administration, Accounting → Business</li>
                                        <li>Biology, Chemistry, Physics → Science</li>
                                        <li>Fine Arts, Design, Music → Arts</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-2">
                                        <strong>Still unsure?</strong> 
                                        Contact student services for guidance.
                                    </p>
                                    <div class="d-flex gap-2">
                                        <a href="#" class="btn btn-sm btn-info">
                                            <i class="fas fa-phone me-1"></i>
                                            Call Support
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-envelope me-1"></i>
                                            Email Help
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.faculty-card {
    transition: all 0.3s ease;
    border-color: #e9ecef !important;
}

.faculty-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    border-color: #667eea !important;
}

.faculty-card:hover .card-title {
    color: #667eea;
}

.faculty-card:hover .btn-outline-primary {
    background-color: #667eea;
    color: white;
    border-color: #667eea;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function selectFaculty(facultyId) {
    // Add loading state
    const cards = document.querySelectorAll('.faculty-card');
    cards.forEach(card => {
        card.style.opacity = '0.6';
        card.style.pointerEvents = 'none';
    });
    
    // Show loading message
    const selectedCard = event.currentTarget;
    const btn = selectedCard.querySelector('.btn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Loading...';
    btn.classList.remove('btn-outline-primary');
    btn.classList.add('btn-primary');
    
    // Navigate to course selection
    setTimeout(() => {
        window.location.href = `/student/select_course/${facultyId}`;
    }, 500);
}

// Add keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        window.location.href = "{{ url_for('student_register') }}";
    }
});
</script>
{% endblock %}
