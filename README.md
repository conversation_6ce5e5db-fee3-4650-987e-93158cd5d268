# Facial Recognition Attendance System

A comprehensive facial recognition-based student attendance system built with Flask, featuring secure biometric data handling, anti-tampering measures, and a user-friendly interface.

## 🚀 Features

### For Students
- **Self-Registration**: Easy step-by-step registration process
- **Faculty & Course Selection**: Intuitive selection of faculty, courses, and modules
- **Facial Recognition Setup**: Secure biometric enrollment
- **Attendance Tracking**: Automatic attendance recording via facial recognition

### For Staff/Lecturers
- **Dashboard**: Comprehensive overview of attendance data
- **Class Management**: Create and manage class sessions
- **Attendance Reports**: Generate detailed attendance reports
- **Student Management**: View enrolled students and their attendance

### Security Features
- **Anti-Tampering**: Record hashing and digital signatures
- **Audit Trail**: Complete immutable log of all changes
- **Encrypted Storage**: Secure biometric data protection
- **Role-Based Access**: Granular permission system

## 📋 Prerequisites

- Python 3.8 or higher
- SQLite (included with Python)
- Webcam/Camera for facial recognition
- Modern web browser

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd FacialRecongition
```

### 2. Create Virtual Environment
```bash
python -m venv venv

# On Windows
venv\Scripts\activate

# On macOS/Linux
source venv/bin/activate
```

### 3. Install Dependencies
```bash
pip install -r FacialRecongition/requirements.txt
```

### 4. Initialize Database
```bash
python init_database.py
```

This will create the SQLite database with sample data including:
- Sample faculties (Engineering, Business, Science, Arts, Medicine, Law)
- Sample courses and modules
- Default admin and lecturer accounts

### 5. Run the Application
```bash
python app.py
```

The application will be available at `http://localhost:5000`

## 🔐 Default Login Credentials

### Admin Account
- **Username**: `admin`
- **Password**: `admin123`

### Lecturer Account
- **Username**: `prof.smith`
- **Password**: `lecturer123`

> **⚠️ Important**: Change these default passwords in production!

## 📖 User Guide

### Student Registration Flow

1. **Basic Information**
   - Enter student number, name, email, and password
   - Accept terms and conditions

2. **Faculty Selection**
   - Choose your faculty from available options
   - Each faculty has different courses

3. **Course Selection**
   - Select your specific course within the faculty
   - View course details and duration

4. **Academic Year Selection**
   - Choose your current academic year
   - Different years have different modules

5. **Module Registration**
   - Select modules you're enrolled in
   - Core modules are typically required

6. **Facial Recognition Setup**
   - Complete biometric enrollment
   - Multiple angle captures for accuracy

### Staff Features

- **Dashboard**: Overview of attendance statistics
- **Class Sessions**: Create and manage class sessions
- **Reports**: Generate attendance reports
- **Student Management**: View and manage student enrollments

## 🗄️ Database Schema

The system uses a comprehensive database schema with the following key tables:

- **Students**: Student information and credentials
- **Faculties**: Academic faculties
- **Courses**: Degree programs
- **Modules**: Individual subjects/courses
- **Attendance Records**: Biometric attendance data with anti-tampering
- **Audit Logs**: Complete change history

See `database_schema.sql` for the complete schema.

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the root directory:

```env
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///attendance.db
FLASK_ENV=development
FACIAL_RECOGNITION_THRESHOLD=0.85
```

### Security Settings
- Change the `SECRET_KEY` in production
- Configure proper SSL/TLS certificates
- Set up proper backup procedures
- Review and update security policies

## 🚀 Deployment

### Development
```bash
python app.py
```

### Production (using Gunicorn)
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8000 app:app
```

### Docker Deployment
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
RUN python init_database.py

EXPOSE 5000
CMD ["python", "app.py"]
```

## 📊 Performance Optimization

### For Large Deployments
- Use PostgreSQL or MySQL instead of SQLite
- Implement database connection pooling
- Set up read replicas for reporting
- Use Redis for session management
- Configure proper indexing

### Facial Recognition Optimization
- Use GPU acceleration for ML models
- Implement model caching
- Optimize image preprocessing
- Use appropriate confidence thresholds

## 🔒 Security Considerations

### Data Protection
- Facial embeddings are encrypted at rest
- All passwords are hashed using bcrypt
- Session data is secured
- HTTPS is recommended for production

### Compliance
- GDPR compliance for biometric data
- FERPA compliance for student records
- Regular security audits recommended
- Data retention policies configurable

## 🧪 Testing

Run the test suite:
```bash
pytest tests/
```

### Test Coverage
- Unit tests for database operations
- Integration tests for API endpoints
- Facial recognition accuracy tests
- Security vulnerability tests

## 📝 API Documentation

### Student Registration Endpoints
- `POST /student/register` - Basic registration
- `GET /student/select_faculty` - Faculty selection
- `GET /student/select_course/<faculty_id>` - Course selection
- `POST /student/complete_registration` - Finalize registration

### Staff Endpoints
- `POST /staff/login` - Staff authentication
- `GET /staff/dashboard` - Dashboard data
- `GET /api/attendance/<session_id>` - Attendance data
- `POST /api/sessions` - Create class session

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation
- Review existing issues
- Contact IT support at your institution
- Submit bug reports via GitHub issues

## 🔄 Version History

- **v1.0.0** - Initial release with basic functionality
- **v1.1.0** - Added anti-tampering features
- **v1.2.0** - Enhanced security and audit logging

## 🙏 Acknowledgments

- OpenCV community for computer vision tools
- Flask community for the web framework
- Face recognition library contributors
- Educational institutions for requirements and feedback
